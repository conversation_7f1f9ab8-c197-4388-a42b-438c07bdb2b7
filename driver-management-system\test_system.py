#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام إدارة السائقين
"""

import requests
import json
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://localhost:5000"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

class SystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول"""
        try:
            # الحصول على صفحة تسجيل الدخول
            response = self.session.get(f"{BASE_URL}/login")
            if response.status_code != 200:
                print(f"❌ خطأ في الوصول لصفحة تسجيل الدخول: {response.status_code}")
                return False
                
            # تسجيل الدخول
            login_data = {
                "username": TEST_USER["username"],
                "password": TEST_USER["password"]
            }
            response = self.session.post(f"{BASE_URL}/login", data=login_data)
            
            if response.status_code == 200 and "dashboard" in response.url:
                print("✅ تم تسجيل الدخول بنجاح")
                self.logged_in = True
                return True
            else:
                print(f"❌ فشل تسجيل الدخول: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {str(e)}")
            return False
    
    def test_pages(self):
        """اختبار الصفحات الأساسية"""
        pages = {
            "/": "الصفحة الرئيسية",
            "/drivers": "صفحة السائقين", 
            "/payments": "صفحة المدفوعات",
            "/reports": "صفحة التقارير",
            "/export": "مركز التصدير",
            "/settings": "صفحة الإعدادات",
            "/map": "صفحة الخريطة",
            "/notifications": "صفحة الإشعارات"
        }
        
        print("\n🔍 اختبار الصفحات:")
        for url, name in pages.items():
            try:
                response = self.session.get(f"{BASE_URL}{url}")
                if response.status_code == 200:
                    print(f"✅ {name}: يعمل بشكل صحيح")
                else:
                    print(f"❌ {name}: خطأ {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: خطأ في الاتصال - {str(e)}")
    
    def test_add_driver(self):
        """اختبار إضافة سائق"""
        print("\n🔍 اختبار إضافة سائق:")
        
        test_driver = {
            "full_name": "سائق تجريبي",
            "phone": "**********",
            "national_id": "**********",
            "license_number": "L123456",
            "vehicle_type": "سيارة",
            "vehicle_plate": "ABC-123",
            "payment_type": "شهري",
            "payment_amount": "1000"
        }
        
        try:
            response = self.session.post(f"{BASE_URL}/drivers/add", data=test_driver)
            if response.status_code == 200 or response.status_code == 302:
                print("✅ إضافة السائق: تم بنجاح")
                return True
            else:
                print(f"❌ إضافة السائق: خطأ {response.status_code}")
                print(f"Response: {response.text[:200]}...")
                return False
        except Exception as e:
            print(f"❌ إضافة السائق: خطأ - {str(e)}")
            return False
    
    def test_export_functions(self):
        """اختبار وظائف التصدير"""
        print("\n🔍 اختبار وظائف التصدير:")
        
        export_urls = {
            "/export/drivers/excel": "تصدير السائقين",
            "/export/payments/excel": "تصدير المدفوعات", 
            "/export/financial/excel": "التقرير المالي"
        }
        
        for url, name in export_urls.items():
            try:
                response = self.session.get(f"{BASE_URL}{url}")
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'excel' in content_type or 'spreadsheet' in content_type:
                        print(f"✅ {name}: يعمل بشكل صحيح")
                    else:
                        print(f"⚠️ {name}: يعمل لكن نوع المحتوى غير متوقع: {content_type}")
                else:
                    print(f"❌ {name}: خطأ {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: خطأ - {str(e)}")
    
    def test_api_endpoints(self):
        """اختبار نقاط API"""
        print("\n🔍 اختبار نقاط API:")
        
        api_endpoints = {
            "/api/drivers": "API السائقين",
            "/api/payments": "API المدفوعات",
            "/api/stats": "API الإحصائيات"
        }
        
        for url, name in api_endpoints.items():
            try:
                response = self.session.get(f"{BASE_URL}{url}")
                if response.status_code == 200:
                    print(f"✅ {name}: يعمل بشكل صحيح")
                elif response.status_code == 404:
                    print(f"⚠️ {name}: غير موجود (404)")
                else:
                    print(f"❌ {name}: خطأ {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: خطأ - {str(e)}")
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار النظام الشامل")
        print("=" * 50)
        
        # تسجيل الدخول
        if not self.login():
            print("❌ فشل في تسجيل الدخول - توقف الاختبار")
            return
        
        # اختبار الصفحات
        self.test_pages()
        
        # اختبار إضافة سائق
        self.test_add_driver()
        
        # اختبار التصدير
        self.test_export_functions()
        
        # اختبار API
        self.test_api_endpoints()
        
        print("\n" + "=" * 50)
        print("✅ انتهى الاختبار الشامل")

if __name__ == "__main__":
    tester = SystemTester()
    tester.run_all_tests()
