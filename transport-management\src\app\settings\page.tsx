'use client'

import { useState } from 'react'
import { 
  Settings, 
  Save, 
  Building, 
  Phone, 
  Mail, 
  MapPin,
  Percent,
  DollarSign,
  Bell,
  Shield,
  Database
} from 'lucide-react'

interface CompanySettings {
  name: string
  phone: string
  email: string
  address: string
  defaultCommissionRate: number
  currency: string
}

interface NotificationSettings {
  newTrip: boolean
  tripCompleted: boolean
  driverRegistration: boolean
  lowBalance: boolean
  emailNotifications: boolean
  smsNotifications: boolean
}

export default function SettingsPage() {
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    name: 'شركة النقل المتميز',
    phone: '+966501234567',
    email: '<EMAIL>',
    address: 'الرياض، المملكة العربية السعودية',
    defaultCommissionRate: 10,
    currency: 'ريال سعودي'
  })

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    newTrip: true,
    tripCompleted: true,
    driverRegistration: true,
    lowBalance: false,
    emailNotifications: true,
    smsNotifications: false
  })

  const [activeTab, setActiveTab] = useState('company')
  const [isSaving, setIsSaving] = useState(false)

  const handleSaveCompanySettings = async () => {
    setIsSaving(true)
    // TODO: حفظ إعدادات الشركة في قاعدة البيانات
    setTimeout(() => {
      setIsSaving(false)
      alert('تم حفظ الإعدادات بنجاح')
    }, 1000)
  }

  const handleSaveNotificationSettings = async () => {
    setIsSaving(true)
    // TODO: حفظ إعدادات الإشعارات في قاعدة البيانات
    setTimeout(() => {
      setIsSaving(false)
      alert('تم حفظ إعدادات الإشعارات بنجاح')
    }, 1000)
  }

  const tabs = [
    { id: 'company', name: 'معلومات الشركة', icon: Building },
    { id: 'notifications', name: 'الإشعارات', icon: Bell },
    { id: 'security', name: 'الأمان', icon: Shield },
    { id: 'database', name: 'قاعدة البيانات', icon: Database }
  ]

  return (
    <div className="min-h-screen bg-gray-100" dir="rtl">
      {/* شريط الإجراءات */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Settings className="h-6 w-6 text-blue-600 ml-2" />
              <h2 className="text-lg font-semibold text-gray-900">الإعدادات</h2>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* قائمة التبويبات */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon size={18} className="ml-3" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* محتوى التبويبات */}
          <div className="lg:col-span-3">
            {activeTab === 'company' && (
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">معلومات الشركة</h3>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Building size={16} className="inline ml-1" />
                      اسم الشركة
                    </label>
                    <input
                      type="text"
                      value={companySettings.name}
                      onChange={(e) => setCompanySettings({...companySettings, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Phone size={16} className="inline ml-1" />
                        رقم الهاتف
                      </label>
                      <input
                        type="tel"
                        value={companySettings.phone}
                        onChange={(e) => setCompanySettings({...companySettings, phone: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Mail size={16} className="inline ml-1" />
                        البريد الإلكتروني
                      </label>
                      <input
                        type="email"
                        value={companySettings.email}
                        onChange={(e) => setCompanySettings({...companySettings, email: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <MapPin size={16} className="inline ml-1" />
                      العنوان
                    </label>
                    <textarea
                      value={companySettings.address}
                      onChange={(e) => setCompanySettings({...companySettings, address: e.target.value})}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Percent size={16} className="inline ml-1" />
                        نسبة العمولة الافتراضية (%)
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={companySettings.defaultCommissionRate}
                        onChange={(e) => setCompanySettings({...companySettings, defaultCommissionRate: parseFloat(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <DollarSign size={16} className="inline ml-1" />
                        العملة
                      </label>
                      <select
                        value={companySettings.currency}
                        onChange={(e) => setCompanySettings({...companySettings, currency: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="ريال سعودي">ريال سعودي</option>
                        <option value="درهم إماراتي">درهم إماراتي</option>
                        <option value="دينار كويتي">دينار كويتي</option>
                        <option value="ريال قطري">ريال قطري</option>
                      </select>
                    </div>
                  </div>

                  <div className="pt-4">
                    <button
                      onClick={handleSaveCompanySettings}
                      disabled={isSaving}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      <Save size={20} className="ml-2" />
                      {isSaving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">إعدادات الإشعارات</h3>
                
                <div className="space-y-6">
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">إشعارات النظام</h4>
                    <div className="space-y-3">
                      {[
                        { key: 'newTrip', label: 'رحلة جديدة' },
                        { key: 'tripCompleted', label: 'اكتمال رحلة' },
                        { key: 'driverRegistration', label: 'تسجيل سائق جديد' },
                        { key: 'lowBalance', label: 'رصيد منخفض' }
                      ].map((item) => (
                        <label key={item.key} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={notificationSettings[item.key as keyof NotificationSettings] as boolean}
                            onChange={(e) => setNotificationSettings({
                              ...notificationSettings,
                              [item.key]: e.target.checked
                            })}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="mr-3 text-sm text-gray-700">{item.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">طرق الإشعار</h4>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={notificationSettings.emailNotifications}
                          onChange={(e) => setNotificationSettings({
                            ...notificationSettings,
                            emailNotifications: e.target.checked
                          })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="mr-3 text-sm text-gray-700">إشعارات البريد الإلكتروني</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={notificationSettings.smsNotifications}
                          onChange={(e) => setNotificationSettings({
                            ...notificationSettings,
                            smsNotifications: e.target.checked
                          })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="mr-3 text-sm text-gray-700">إشعارات الرسائل النصية</span>
                      </label>
                    </div>
                  </div>

                  <div className="pt-4">
                    <button
                      onClick={handleSaveNotificationSettings}
                      disabled={isSaving}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      <Save size={20} className="ml-2" />
                      {isSaving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">إعدادات الأمان</h3>
                <div className="text-center py-12">
                  <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">قريباً</h4>
                  <p className="text-gray-600">ستتوفر إعدادات الأمان في التحديث القادم</p>
                </div>
              </div>
            )}

            {activeTab === 'database' && (
              <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">إعدادات قاعدة البيانات</h3>
                <div className="text-center py-12">
                  <Database className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">قريباً</h4>
                  <p className="text-gray-600">ستتوفر إعدادات قاعدة البيانات في التحديث القادم</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
