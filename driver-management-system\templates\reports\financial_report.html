{% extends "base.html" %}

{% block title %}التقرير المالي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line"></i> التقرير المالي المفصل</h2>
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للتقارير
                    </a>
                    <a href="{{ url_for('export_financial_excel') }}" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات مالية -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ "%.2f"|format(total_revenue) }} ريال</h4>
                            <p class="mb-0">إجمالي الإيرادات المحصلة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ "%.2f"|format(pending_revenue) }} ريال</h4>
                            <p class="mb-0">إيرادات معلقة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hourglass-half fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ "%.2f"|format(total_revenue + pending_revenue) }} ريال</h4>
                            <p class="mb-0">إجمالي الإيرادات المتوقعة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني للإيرادات الشهرية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> الإيرادات الشهرية</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyRevenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الإيرادات الشهرية -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table"></i> تفاصيل الإيرادات الشهرية</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="monthlyTable">
                    <thead class="table-dark">
                        <tr>
                            <th>الشهر</th>
                            <th>إجمالي الإيرادات</th>
                            <th>عدد المدفوعات</th>
                            <th>متوسط الدفعة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month_data in monthly_payments %}
                        <tr>
                            <td>{{ month_data.month }}</td>
                            <td>{{ "%.2f"|format(month_data.total) }} ريال</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// رسم بياني للإيرادات الشهرية
const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
const monthlyRevenueChart = new Chart(monthlyRevenueCtx, {
    type: 'bar',
    data: {
        labels: [{% for month_data in monthly_payments %}'{{ month_data.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'الإيرادات (ريال)',
            data: [{% for month_data in monthly_payments %}{{ month_data.total }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + ' ريال';
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.parsed.y + ' ريال';
                    }
                }
            }
        }
    }
});

// تفعيل DataTable
$(document).ready(function() {
    $('#monthlyTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        order: [[0, 'desc']], // ترتيب حسب الشهر
        pageLength: 12
    });
});
</script>

    <!-- الرسم البياني للإيرادات الشهرية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> الإيرادات الشهرية</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyRevenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الإيرادات الشهرية -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table"></i> تفاصيل الإيرادات الشهرية</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="monthlyTable">
                    <thead class="table-dark">
                        <tr>
                            <th>الشهر</th>
                            <th>إجمالي الإيرادات</th>
                            <th>عدد المدفوعات</th>
                            <th>متوسط الدفعة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month_data in monthly_payments %}
                        <tr>
                            <td>{{ month_data.month }}</td>
                            <td>{{ "%.2f"|format(month_data.total) }} ريال</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// رسم بياني للإيرادات الشهرية
const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
const monthlyRevenueChart = new Chart(monthlyRevenueCtx, {
    type: 'bar',
    data: {
        labels: [{% for month_data in monthly_payments %}'{{ month_data.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'الإيرادات (ريال)',
            data: [{% for month_data in monthly_payments %}{{ month_data.total }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + ' ريال';
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.parsed.y + ' ريال';
                    }
                }
            }
        }
    }
});

// تفعيل DataTable
$(document).ready(function() {
    $('#monthlyTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        order: [[0, 'desc']], // ترتيب حسب الشهر
        pageLength: 12
    });
});
</script>
{% endblock %}
