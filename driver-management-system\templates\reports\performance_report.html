{% extends "base.html" %}

{% block title %}تقرير الأداء{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-trophy"></i> تقرير الأداء</h2>
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للتقارير
                    </a>
                    <a href="{{ url_for('export_drivers_excel') }}" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الأداء -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ avg_rating }}</h4>
                            <p class="mb-0">متوسط التقييم العام</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ top_drivers|length }}</h4>
                            <p class="mb-0">عدد السائقين المقيمين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أفضل السائقين -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-medal"></i> أفضل السائقين (أعلى تقييم)</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="topDriversTable">
                    <thead class="table-dark">
                        <tr>
                            <th>الترتيب</th>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>نوع المركبة</th>
                            <th>التقييم</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for driver in top_drivers %}
                        <tr>
                            <td>
                                {% if loop.index == 1 %}
                                    <i class="fas fa-trophy text-warning"></i> {{ loop.index }}
                                {% elif loop.index == 2 %}
                                    <i class="fas fa-medal text-secondary"></i> {{ loop.index }}
                                {% elif loop.index == 3 %}
                                    <i class="fas fa-award text-warning"></i> {{ loop.index }}
                                {% else %}
                                    {{ loop.index }}
                                {% endif %}
                            </td>
                            <td>{{ driver.name }}</td>
                            <td>{{ driver.phone }}</td>
                            <td>{{ driver.vehicle_type or '-' }}</td>
                            <td>
                                {% if driver.rating %}
                                    {% for i in range(driver.rating|int) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    ({{ driver.rating }})
                                {% else %}
                                    <span class="text-muted">غير مقيم</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if driver.status == 'نشط' %}
                                    <span class="badge bg-success">{{ driver.status }}</span>
                                {% elif driver.status == 'معلق' %}
                                    <span class="badge bg-warning">{{ driver.status }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ driver.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ driver.created_at.strftime('%Y-%m-%d') if driver.created_at else '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل DataTable
$(document).ready(function() {
    $('#topDriversTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        order: [[4, 'desc']], // ترتيب حسب التقييم
        pageLength: 25,
        paging: false, // إخفاء التصفح للقائمة المحدودة
        info: false // إخفاء معلومات العدد
    });
});
</script>
{% endblock %}
