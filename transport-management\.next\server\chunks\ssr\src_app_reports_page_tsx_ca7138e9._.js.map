{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/%D9%85%D9%84%D9%81%20%D8%B4%D9%87%D9%8A%D8%AF%20%D9%85%D9%88%D8%B3%D9%89%20%D8%B3%D9%84%D8%B7%D8%A7%D9%86/%D9%85%D9%88%D9%82%D8%B9%20%D8%AC%D8%AF%D9%8A%D8%AF/transport-management/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { \n  TrendingUp, \n  DollarSign, \n  Users, \n  Car,\n  Calendar,\n  Download,\n  Filter\n} from 'lucide-react'\nimport { \n  LineChart, \n  Line, \n  BarChart, \n  Bar, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell\n} from 'recharts'\n\n// بيانات وهمية للرسوم البيانية\nconst monthlyEarnings = [\n  { month: 'يناير', earnings: 45000, trips: 180 },\n  { month: 'فبراير', earnings: 52000, trips: 210 },\n  { month: 'مارس', earnings: 48000, trips: 195 },\n  { month: 'أبريل', earnings: 61000, trips: 245 },\n  { month: 'مايو', earnings: 55000, trips: 220 },\n  { month: 'يونيو', earnings: 67000, trips: 270 }\n]\n\nconst driverPerformance = [\n  { name: 'أحمد محمد', trips: 45, earnings: 11250 },\n  { name: 'محمد علي', trips: 38, earnings: 9500 },\n  { name: 'سعد عبدالله', trips: 42, earnings: 10500 },\n  { name: 'عبدالرحمن سالم', trips: 35, earnings: 8750 },\n  { name: 'خالد أحمد', trips: 40, earnings: 10000 }\n]\n\nconst tripStatusData = [\n  { name: 'مكتملة', value: 850, color: '#10B981' },\n  { name: 'جارية', value: 25, color: '#3B82F6' },\n  { name: 'في الانتظار', value: 15, color: '#F59E0B' },\n  { name: 'ملغية', value: 35, color: '#EF4444' }\n]\n\n// مكون بطاقة الإحصائيات\nfunction StatCard({ title, value, change, icon: Icon, color = \"blue\" }: {\n  title: string\n  value: string | number\n  change?: string\n  icon: any\n  color?: string\n}) {\n  const colorClasses = {\n    blue: \"bg-blue-500\",\n    green: \"bg-green-500\", \n    yellow: \"bg-yellow-500\",\n    purple: \"bg-purple-500\"\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n          {change && (\n            <p className=\"text-sm text-green-600 mt-1\">\n              <TrendingUp size={12} className=\"inline ml-1\" />\n              {change}\n            </p>\n          )}\n        </div>\n        <div className={`p-3 rounded-full ${colorClasses[color as keyof typeof colorClasses]} text-white`}>\n          <Icon size={24} />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default function ReportsPage() {\n  const [dateRange, setDateRange] = useState('6months')\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // محاكاة تحميل البيانات\n    setTimeout(() => {\n      setIsLoading(false)\n    }, 1000)\n  }, [])\n\n  const handleExport = () => {\n    // TODO: تنفيذ تصدير التقارير\n    console.log('تصدير التقارير')\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-100 flex items-center justify-center\" dir=\"rtl\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">جاري تحميل التقارير...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-100\" dir=\"rtl\">\n      {/* شريط الإجراءات */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <TrendingUp className=\"h-6 w-6 text-blue-600 ml-2\" />\n              <h2 className=\"text-lg font-semibold text-gray-900\">التقارير المالية</h2>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <select\n                value={dateRange}\n                onChange={(e) => setDateRange(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"1month\">الشهر الماضي</option>\n                <option value=\"3months\">آخر 3 أشهر</option>\n                <option value=\"6months\">آخر 6 أشهر</option>\n                <option value=\"1year\">السنة الماضية</option>\n              </select>\n              <button\n                onClick={handleExport}\n                className=\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <Download size={20} className=\"ml-2\" />\n                تصدير التقرير\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* المحتوى الرئيسي */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* الإحصائيات الرئيسية */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <StatCard\n            title=\"إجمالي الأرباح\"\n            value=\"328,000 ريال\"\n            change=\"+12.5% من الشهر الماضي\"\n            icon={DollarSign}\n            color=\"green\"\n          />\n          <StatCard\n            title=\"عدد الرحلات\"\n            value=\"1,320\"\n            change=\"+8.2% من الشهر الماضي\"\n            icon={Car}\n            color=\"blue\"\n          />\n          <StatCard\n            title=\"السائقين النشطين\"\n            value=\"485\"\n            change=\"+3.1% من الشهر الماضي\"\n            icon={Users}\n            color=\"purple\"\n          />\n          <StatCard\n            title=\"متوسط الأجرة\"\n            value=\"248 ريال\"\n            change=\"+5.7% من الشهر الماضي\"\n            icon={TrendingUp}\n            color=\"yellow\"\n          />\n        </div>\n\n        {/* الرسوم البيانية */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n          {/* رسم بياني للأرباح الشهرية */}\n          <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الأرباح الشهرية</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={monthlyEarnings}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip\n                  formatter={(value, name) => [\n                    `${Number(value).toLocaleString('en-US')} ريال`,\n                    name === 'earnings' ? 'الأرباح' : 'الرحلات'\n                  ]}\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"earnings\" \n                  stroke=\"#3B82F6\" \n                  strokeWidth={3}\n                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 6 }}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* رسم بياني لحالة الرحلات */}\n          <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">توزيع حالة الرحلات</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={tripStatusData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={100}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                >\n                  {tripStatusData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip formatter={(value) => [`${value} رحلة`, 'العدد']} />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* أداء السائقين */}\n        <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">أداء أفضل السائقين</h3>\n          <ResponsiveContainer width=\"100%\" height={400}>\n            <BarChart data={driverPerformance} layout=\"horizontal\">\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis type=\"number\" />\n              <YAxis dataKey=\"name\" type=\"category\" width={120} />\n              <Tooltip \n                formatter={(value, name) => [\n                  name === 'trips' ? `${value} رحلة` : `${value.toLocaleString()} ريال`,\n                  name === 'trips' ? 'عدد الرحلات' : 'الأرباح'\n                ]}\n              />\n              <Bar dataKey=\"earnings\" fill=\"#10B981\" name=\"earnings\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;AA2BA,+BAA+B;AAC/B,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAS,UAAU;QAAO,OAAO;IAAI;IAC9C;QAAE,OAAO;QAAU,UAAU;QAAO,OAAO;IAAI;IAC/C;QAAE,OAAO;QAAQ,UAAU;QAAO,OAAO;IAAI;IAC7C;QAAE,OAAO;QAAS,UAAU;QAAO,OAAO;IAAI;IAC9C;QAAE,OAAO;QAAQ,UAAU;QAAO,OAAO;IAAI;IAC7C;QAAE,OAAO;QAAS,UAAU;QAAO,OAAO;IAAI;CAC/C;AAED,MAAM,oBAAoB;IACxB;QAAE,MAAM;QAAa,OAAO;QAAI,UAAU;IAAM;IAChD;QAAE,MAAM;QAAY,OAAO;QAAI,UAAU;IAAK;IAC9C;QAAE,MAAM;QAAe,OAAO;QAAI,UAAU;IAAM;IAClD;QAAE,MAAM;QAAkB,OAAO;QAAI,UAAU;IAAK;IACpD;QAAE,MAAM;QAAa,OAAO;QAAI,UAAU;IAAM;CACjD;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAU,OAAO;QAAK,OAAO;IAAU;IAC/C;QAAE,MAAM;QAAS,OAAO;QAAI,OAAO;IAAU;IAC7C;QAAE,MAAM;QAAe,OAAO;QAAI,OAAO;IAAU;IACnD;QAAE,MAAM;QAAS,OAAO;QAAI,OAAO;IAAU;CAC9C;AAED,wBAAwB;AACxB,SAAS,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,EAAE,QAAQ,MAAM,EAMnE;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;wBAChD,wBACC,8OAAC;4BAAE,WAAU;;8CACX,8OAAC,kNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAC/B;;;;;;;;;;;;;8BAIP,8OAAC;oBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAmC,CAAC,WAAW,CAAC;8BAC/F,cAAA,8OAAC;wBAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKtB;AAEe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wBAAwB;QACxB,WAAW;YACT,aAAa;QACf,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,6BAA6B;QAC7B,QAAQ,GAAG,CAAC;IACd;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;YAA4D,KAAI;sBAC7E,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA2B,KAAI;;0BAE5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;kDAExB,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,MAAM,kNAAA,CAAA,aAAU;gCAChB,OAAM;;;;;;0CAER,8OAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,MAAM,gMAAA,CAAA,MAAG;gCACT,OAAM;;;;;;0CAER,8OAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,MAAM,oMAAA,CAAA,QAAK;gCACX,OAAM;;;;;;0CAER,8OAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,MAAM,kNAAA,CAAA,aAAU;gCAChB,OAAM;;;;;;;;;;;;kCAKV,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC,mKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;4CAAC,MAAM;;8DACf,8OAAC,6JAAA,CAAA,gBAAa;oDAAC,iBAAgB;;;;;;8DAC/B,8OAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;;;;;;8DACf,8OAAC,qJAAA,CAAA,QAAK;;;;;8DACN,8OAAC,uJAAA,CAAA,UAAO;oDACN,WAAW,CAAC,OAAO,OAAS;4DAC1B,GAAG,OAAO,OAAO,cAAc,CAAC,SAAS,KAAK,CAAC;4DAC/C,SAAS,aAAa,YAAY;yDACnC;;;;;;8DAEH,8OAAC,oJAAA,CAAA,OAAI;oDACH,MAAK;oDACL,SAAQ;oDACR,QAAO;oDACP,aAAa;oDACb,KAAK;wDAAE,MAAM;wDAAW,aAAa;wDAAG,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;;;;;0CAOrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC,mKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;8DACP,8OAAC,+IAAA,CAAA,MAAG;oDACF,MAAM;oDACN,IAAG;oDACH,IAAG;oDACH,aAAa;oDACb,MAAK;oDACL,SAAQ;oDACR,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;8DAErE,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,oJAAA,CAAA,OAAI;4DAAuB,MAAM,MAAM,KAAK;2DAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;8DAG9B,8OAAC,uJAAA,CAAA,UAAO;oDAAC,WAAW,CAAC,QAAU;4DAAC,GAAG,MAAM,KAAK,CAAC;4DAAE;yDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAmB,QAAO;;sDACxC,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,MAAK;;;;;;sDACZ,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,MAAK;4CAAW,OAAO;;;;;;sDAC7C,8OAAC,uJAAA,CAAA,UAAO;4CACN,WAAW,CAAC,OAAO,OAAS;oDAC1B,SAAS,UAAU,GAAG,MAAM,KAAK,CAAC,GAAG,GAAG,MAAM,cAAc,GAAG,KAAK,CAAC;oDACrE,SAAS,UAAU,gBAAgB;iDACpC;;;;;;sDAEH,8OAAC,mJAAA,CAAA,MAAG;4CAAC,SAAQ;4CAAW,MAAK;4CAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1D", "debugId": null}}]}