{% extends "base.html" %}

{% block title %}لوحة التحكم الذكية - نظام إدارة السائقين{% endblock %}

{% block extra_head %}
<style>
.dashboard-card {
    transition: transform 0.2s;
}
.dashboard-card:hover {
    transform: translateY(-2px);
}
.growth-positive {
    color: #28a745;
}
.growth-negative {
    color: #dc3545;
}
.metric-icon {
    font-size: 2.5rem;
    opacity: 0.3;
}
.chart-container {
    position: relative;
    height: 300px;
}
.mini-chart {
    height: 200px;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم الذكية
                <small class="text-muted">- تحديث مباشر</small>
            </h1>
            <div>
                <a href="{{ url_for('dashboard_analytics') }}" class="btn btn-primary">
                    <i class="fas fa-chart-bar me-2"></i>
                    التحليلات المتقدمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="row mb-4">
    <!-- Total Drivers Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي السائقين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="total-drivers">{{ total_drivers }}</div>
                        <div class="small text-muted">
                            <i class="fas fa-user-check text-success"></i> {{ active_drivers }} نشط
                            <i class="fas fa-user-times text-warning ms-2"></i> {{ inactive_drivers }} معلق
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users metric-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Drivers Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            السائقين النشطين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="active-drivers">{{ active_drivers }}</div>
                        <div class="small text-muted">
                            {{ "%.1f"|format((active_drivers/total_drivers*100) if total_drivers > 0 else 0) }}% من الإجمالي
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check metric-icon text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Late Payments Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            المدفوعات المتأخرة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overdue_count }}</div>
                        <div class="small text-muted">
                            <i class="fas fa-clock text-warning"></i> يحتاج متابعة
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle metric-icon text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            إيرادات هذا الشهر
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="revenue">{{ "{:,.0f}".format(this_month_revenue) }} ريال</div>
                        <div class="small">
                            {% if growth_rate > 0 %}
                                <i class="fas fa-arrow-up growth-positive"></i>
                                <span class="growth-positive">+{{ "%.1f"|format(growth_rate) }}%</span>
                            {% elif growth_rate < 0 %}
                                <i class="fas fa-arrow-down growth-negative"></i>
                                <span class="growth-negative">{{ "%.1f"|format(growth_rate) }}%</span>
                            {% else %}
                                <i class="fas fa-minus text-muted"></i>
                                <span class="text-muted">0%</span>
                            {% endif %}
                            من الشهر الماضي
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line metric-icon text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics Row -->
<div class="row mb-4">
    <!-- Total Revenue Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            إجمالي الإيرادات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "{:,.0f}".format(total_revenue) }} ريال</div>
                        <div class="small text-muted">
                            متوسط: {{ "{:,.0f}".format(avg_payment) }} ريال
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave metric-icon text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Paid Payments Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            المدفوعات المكتملة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" data-stat="paid-count">{{ paid_count }}</div>
                        <div class="small text-muted">
                            <i class="fas fa-hourglass-half text-warning"></i> {{ pending_count }} معلق
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle metric-icon text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Drivers with Location Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            السائقين بمواقع
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ drivers_with_location }}</div>
                        <div class="small text-muted">
                            {{ "%.1f"|format((drivers_with_location/total_drivers*100) if total_drivers > 0 else 0) }}% مُحدد الموقع
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-map-marker-alt metric-icon text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-right-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="text-center">
                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-2">
                        إجراءات سريعة
                    </div>
                    <div class="btn-group-vertical w-100" role="group">
                        <a href="{{ url_for('add_driver') }}" class="btn btn-primary btn-sm mb-1">
                            <i class="fas fa-plus"></i> إضافة سائق
                        </a>
                        <a href="{{ url_for('add_payment') }}" class="btn btn-success btn-sm mb-1">
                            <i class="fas fa-dollar-sign"></i> تسجيل دفعة
                        </a>
                        <a href="{{ url_for('maps_view') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-map"></i> عرض الخريطة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Charts Row -->
<div class="row mb-4">
    <!-- Revenue Trend Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line me-2"></i>
                    اتجاه الإيرادات الشهرية
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <div class="dropdown-header">خيارات الرسم البياني:</div>
                        <a class="dropdown-item" href="#" onclick="toggleChartType('revenueChart', 'line')">خط</a>
                        <a class="dropdown-item" href="#" onclick="toggleChartType('revenueChart', 'bar')">أعمدة</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        آخر تحديث: {{ moment().format('YYYY-MM-DD HH:mm') }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Status Pie Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>
                    حالة المدفوعات
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container mini-chart">
                    <canvas id="paymentStatusChart"></canvas>
                </div>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-success font-weight-bold">{{ paid_count }}</div>
                            <div class="small text-muted">مدفوع</div>
                        </div>
                        <div class="col-4">
                            <div class="text-warning font-weight-bold">{{ pending_count }}</div>
                            <div class="small text-muted">معلق</div>
                        </div>
                        <div class="col-4">
                            <div class="text-danger font-weight-bold">{{ overdue_count }}</div>
                            <div class="small text-muted">متأخر</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Charts Row -->
<div class="row mb-4">
    <!-- Vehicle Types Chart -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-car me-2"></i>
                    توزيع أنواع المركبات
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container mini-chart">
                    <canvas id="vehicleTypesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods Chart -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-credit-card me-2"></i>
                    طرق الدفع المفضلة
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container mini-chart">
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
                <div class="mt-3">
                    {% for method, count, amount in payment_method_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">{{ method }}</span>
                        <div>
                            <span class="badge bg-primary">{{ count }}</span>
                            <span class="small text-muted">{{ "{:,.0f}".format(amount) }} ريال</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <!-- Recent Drivers -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">السائقين الجدد</h6>
            </div>
            <div class="card-body">
                {% if recent_drivers %}
                    {% for driver in recent_drivers %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">{{ driver.created_at.strftime('%Y-%m-%d') }}</div>
                            <div class="font-weight-bold">{{ driver.name }}</div>
                            <div class="text-muted small">{{ driver.phone }}</div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{{ 'success' if driver.status == 'نشط' else 'secondary' }}">
                                {{ driver.status }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد سائقين جدد</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">المدفوعات الأخيرة</h6>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                    {% for payment in recent_payments %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="icon-circle bg-success">
                                <i class="fas fa-dollar-sign text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">{{ payment.payment_date.strftime('%Y-%m-%d') }}</div>
                            <div class="font-weight-bold">{{ payment.driver.name }}</div>
                            <div class="text-muted small">{{ payment.payment_method }}</div>
                        </div>
                        <div class="text-end">
                            <div class="font-weight-bold text-success">{{ "{:,.0f}".format(payment.amount) }} ريال</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد مدفوعات حديثة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Chart.js Configuration
Chart.defaults.font.family = 'Cairo, sans-serif';
Chart.defaults.plugins.legend.rtl = true;

// Revenue Chart with Real Data
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: {{ monthly_labels | tojson }},
        datasets: [{
            label: 'الإيرادات (ريال)',
            data: {{ monthly_revenue | tojson }},
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.3,
            pointBackgroundColor: '#4e73df',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'top',
                labels: {
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0,0,0,0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#4e73df',
                borderWidth: 1,
                callbacks: {
                    label: function(context) {
                        return 'الإيرادات: ' + context.parsed.y.toLocaleString() + ' ريال';
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#858796'
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(234, 236, 244, 0.5)'
                },
                ticks: {
                    color: '#858796',
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        }
    }
});

// Enhanced Payment Status Chart
const paymentStatusCtx = document.getElementById('paymentStatusChart').getContext('2d');
const paymentStatusChart = new Chart(paymentStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['مدفوع', 'معلق', 'متأخر'],
        datasets: [{
            data: [{{ paid_count }}, {{ pending_count }}, {{ overdue_count }}],
            backgroundColor: [
                '#1cc88a',
                '#f6c23e',
                '#e74a3b'
            ],
            borderWidth: 3,
            borderColor: '#ffffff',
            hoverBorderWidth: 5,
            hoverOffset: 10
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 15,
                    generateLabels: function(chart) {
                        const data = chart.data;
                        if (data.labels.length && data.datasets.length) {
                            return data.labels.map((label, i) => {
                                const dataset = data.datasets[0];
                                const value = dataset.data[i];
                                const total = dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);

                                return {
                                    text: `${label} (${percentage}%)`,
                                    fillStyle: dataset.backgroundColor[i],
                                    strokeStyle: dataset.borderColor,
                                    lineWidth: dataset.borderWidth,
                                    pointStyle: 'circle',
                                    hidden: false,
                                    index: i
                                };
                            });
                        }
                        return [];
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return `${context.label}: ${context.parsed} (${percentage}%)`;
                    }
                }
            }
        },
        cutout: '60%'
    }
});

// Vehicle Types Chart
const vehicleTypesCtx = document.getElementById('vehicleTypesChart').getContext('2d');
const vehicleTypesChart = new Chart(vehicleTypesCtx, {
    type: 'bar',
    data: {
        labels: [{% for vehicle_type, count in vehicle_stats %}'{{ vehicle_type }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'عدد السائقين',
            data: [{% for vehicle_type, count in vehicle_stats %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b'
            ],
            borderColor: '#ffffff',
            borderWidth: 2,
            borderRadius: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                }
            },
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Payment Methods Chart
const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
const paymentMethodsChart = new Chart(paymentMethodsCtx, {
    type: 'polarArea',
    data: {
        labels: [{% for method, count, amount in payment_method_stats %}'{{ method }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for method, count, amount in payment_method_stats %}{{ amount }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                'rgba(78, 115, 223, 0.8)',
                'rgba(28, 200, 138, 0.8)',
                'rgba(54, 185, 204, 0.8)',
                'rgba(246, 194, 62, 0.8)',
                'rgba(231, 74, 59, 0.8)'
            ],
            borderColor: '#ffffff',
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    usePointStyle: true
                }
            }
        },
        scales: {
            r: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        }
    }
});

// Chart Type Toggle Function
function toggleChartType(chartId, newType) {
    const chart = Chart.getChart(chartId);
    if (chart) {
        chart.config.type = newType;
        chart.update();
    }
}

// Real-time dashboard updates
let updateInterval;

function updateDashboardData() {
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update statistics cards
                updateStatisticsCards(data.data);

                // Update charts with new data
                updateCharts(data.data);

                // Update last updated time
                const lastUpdated = new Date(data.data.last_updated).toLocaleString('ar-SA');
                document.querySelectorAll('.last-updated').forEach(el => {
                    el.textContent = `آخر تحديث: ${lastUpdated}`;
                });

                console.log('Dashboard updated successfully');
            }
        })
        .catch(error => {
            console.error('Error updating dashboard:', error);
        });
}

function updateStatisticsCards(data) {
    // Update total drivers
    const totalDriversEl = document.querySelector('[data-stat="total-drivers"]');
    if (totalDriversEl) totalDriversEl.textContent = data.total_drivers;

    // Update active drivers
    const activeDriversEl = document.querySelector('[data-stat="active-drivers"]');
    if (activeDriversEl) activeDriversEl.textContent = data.active_drivers;

    // Update revenue
    const revenueEl = document.querySelector('[data-stat="revenue"]');
    if (revenueEl) revenueEl.textContent = data.this_month_revenue.toLocaleString() + ' ريال';

    // Update payment counts
    const paidCountEl = document.querySelector('[data-stat="paid-count"]');
    if (paidCountEl) paidCountEl.textContent = data.paid_count;

    const pendingCountEl = document.querySelector('[data-stat="pending-count"]');
    if (pendingCountEl) pendingCountEl.textContent = data.pending_count;

    const overdueCountEl = document.querySelector('[data-stat="overdue-count"]');
    if (overdueCountEl) overdueCountEl.textContent = data.overdue_count;
}

function updateCharts(data) {
    // Update payment status chart
    if (paymentStatusChart) {
        paymentStatusChart.data.datasets[0].data = [
            data.paid_count,
            data.pending_count,
            data.overdue_count
        ];
        paymentStatusChart.update('none');
    }

    // Update daily revenue chart if available
    if (data.daily_revenue && revenueChart) {
        const dailyLabels = data.daily_revenue.map(item => {
            const date = new Date(item.date);
            return date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
        });
        const dailyValues = data.daily_revenue.map(item => item.revenue);

        // Add daily revenue as a second dataset
        if (revenueChart.data.datasets.length === 1) {
            revenueChart.data.datasets.push({
                label: 'الإيرادات اليومية (آخر 7 أيام)',
                data: dailyValues,
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.2,
                pointBackgroundColor: '#1cc88a',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4
            });

            // Update labels to show daily data
            revenueChart.data.labels = dailyLabels;
            revenueChart.update();
        }
    }
}

// Start real-time updates every 30 seconds
function startRealTimeUpdates() {
    updateInterval = setInterval(updateDashboardData, 30000);
    console.log('Real-time updates started');
}

// Stop real-time updates
function stopRealTimeUpdates() {
    if (updateInterval) {
        clearInterval(updateInterval);
        console.log('Real-time updates stopped');
    }
}

// Auto-refresh full page every 10 minutes as fallback
setInterval(function() {
    location.reload();
}, 600000);

// Add loading animation and initialize real-time updates
document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Start real-time updates after page load
    setTimeout(() => {
        startRealTimeUpdates();
    }, 2000);

    // Add real-time indicator
    const indicator = document.createElement('div');
    indicator.innerHTML = '<i class="fas fa-circle text-success"></i> <small>تحديث مباشر</small>';
    indicator.className = 'position-fixed bottom-0 end-0 m-3 p-2 bg-white rounded shadow-sm';
    indicator.style.zIndex = '1000';
    document.body.appendChild(indicator);
});
</script>
{% endblock %}
