{% extends "base.html" %}

{% block title %}تقرير مخصص{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-filter"></i> تقرير مخصص</h2>
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للتقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج التصفية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-search"></i> معايير التقرير</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('custom_report') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="start_date" value="{{ start_date or '' }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="end_date" value="{{ end_date or '' }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">نوع التقرير</label>
                            <select class="form-select" name="report_type">
                                <option value="drivers" {% if report_type == 'drivers' %}selected{% endif %}>السائقين</option>
                                <option value="payments" {% if report_type == 'payments' %}selected{% endif %}>المدفوعات</option>
                                <option value="financial" {% if report_type == 'financial' %}selected{% endif %}>مالي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="نشط" {% if status == 'نشط' %}selected{% endif %}>نشط</option>
                                <option value="غير نشط" {% if status == 'غير نشط' %}selected{% endif %}>غير نشط</option>
                                <option value="معلق" {% if status == 'معلق' %}selected{% endif %}>معلق</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> إنشاء التقرير
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- نتائج التقرير -->
    {% if results %}
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-table"></i> نتائج التقرير</h5>
            <div>
                <span class="badge bg-info">{{ results|length }} نتيجة</span>
                <button class="btn btn-success btn-sm" onclick="exportResults()">
                    <i class="fas fa-file-excel"></i> تصدير
                </button>
            </div>
        </div>
        <div class="card-body">
            {% if report_type == 'drivers' %}
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="resultsTable">
                    <thead class="table-dark">
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>نوع المركبة</th>
                            <th>لوحة المركبة</th>
                            <th>نوع الدفع</th>
                            <th>مبلغ الدفع</th>
                            <th>الحالة</th>
                            <th>التقييم</th>
                            <th>تاريخ التسجيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for driver in results %}
                        <tr>
                            <td>{{ driver.name }}</td>
                            <td>{{ driver.phone }}</td>
                            <td>{{ driver.vehicle_type or '-' }}</td>
                            <td>{{ driver.vehicle_plate or '-' }}</td>
                            <td>{{ driver.payment_type or '-' }}</td>
                            <td>{{ driver.payment_amount or 0 }} ريال</td>
                            <td>
                                {% if driver.status == 'نشط' %}
                                    <span class="badge bg-success">{{ driver.status }}</span>
                                {% elif driver.status == 'معلق' %}
                                    <span class="badge bg-warning">{{ driver.status }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ driver.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if driver.rating %}
                                    {% for i in range(driver.rating|int) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    ({{ driver.rating }})
                                {% else %}
                                    <span class="text-muted">غير مقيم</span>
                                {% endif %}
                            </td>
                            <td>{{ driver.created_at.strftime('%Y-%m-%d') if driver.created_at else '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
// تفعيل DataTable
$(document).ready(function() {
    {% if results %}
    $('#resultsTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        pageLength: 25,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });
    {% endif %}
});

function exportResults() {
    // تصدير النتائج
    const table = $('#resultsTable').DataTable();
    table.button('.buttons-excel').trigger();
}
</script>
{% endblock %}
