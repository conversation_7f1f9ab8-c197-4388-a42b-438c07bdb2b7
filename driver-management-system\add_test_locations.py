#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة مواقع تجريبية للسائقين
Add test locations for drivers
"""

from app import app, db
from models import Driver
from datetime import datetime

def add_test_locations():
    """إضافة مواقع تجريبية للسائقين الموجودين"""
    
    with app.app_context():
        # الحصول على السائقين الموجودين
        drivers = Driver.query.limit(10).all()
        
        if not drivers:
            print("لا يوجد سائقين في قاعدة البيانات")
            return
        
        # مواقع تجريبية في الرياض ومحيطها
        test_locations = [
            {"lat": 24.7136, "lng": 46.6753, "address": "وسط الرياض"},
            {"lat": 24.7500, "lng": 46.6900, "address": "شمال الرياض"},
            {"lat": 24.6800, "lng": 46.6600, "address": "جنوب الرياض"},
            {"lat": 24.7200, "lng": 46.7200, "address": "شرق الرياض"},
            {"lat": 24.7000, "lng": 46.6400, "address": "غرب الرياض"},
            {"lat": 24.7300, "lng": 46.6800, "address": "حي الملز"},
            {"lat": 24.6900, "lng": 46.6700, "address": "حي العليا"},
            {"lat": 24.7400, "lng": 46.6500, "address": "حي الصحافة"},
            {"lat": 24.7100, "lng": 46.7000, "address": "حي النخيل"},
            {"lat": 24.6700, "lng": 46.6800, "address": "حي الفيصلية"}
        ]
        
        # إضافة المواقع للسائقين
        for i, driver in enumerate(drivers):
            if i < len(test_locations):
                location = test_locations[i]
                driver.current_latitude = location["lat"]
                driver.current_longitude = location["lng"]
                driver.current_address = location["address"]
                driver.updated_at = datetime.now()
                
                print(f"تم إضافة موقع للسائق: {driver.name} - {location['address']}")
        
        # حفظ التغييرات
        db.session.commit()
        print(f"تم إضافة مواقع لـ {min(len(drivers), len(test_locations))} سائق")

if __name__ == "__main__":
    add_test_locations()
