#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية للمدفوعات
Add sample payment data for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import Driver, Payment
from datetime import datetime, timedelta
import random

def add_sample_payments():
    """إضافة مدفوعات تجريبية"""
    with app.app_context():
        # الحصول على جميع السائقين
        drivers = Driver.query.all()
        
        if not drivers:
            print("❌ لا توجد سائقين في قاعدة البيانات")
            return False
        
        print(f"🔄 إضافة مدفوعات تجريبية لـ {len(drivers)} سائق...")
        
        payment_methods = ['تحويل بنكي', 'كاش', 'شيك']
        statuses = ['مكتمل', 'معلق', 'ملغي']
        payment_types = ['شهري', 'أسبوعي', 'يومي']
        
        payments_added = 0
        
        for driver in drivers:
            # إضافة 2-5 مدفوعات لكل سائق
            num_payments = random.randint(2, 5)
            
            for i in range(num_payments):
                # تاريخ عشوائي في آخر 6 أشهر
                days_ago = random.randint(1, 180)
                payment_date = datetime.now() - timedelta(days=days_ago)
                
                # مبلغ عشوائي بناءً على نوع دفع السائق
                base_amount = driver.payment_amount or 500
                amount = base_amount + random.randint(-100, 200)
                
                payment = Payment(
                    driver_id=driver.id,
                    amount=amount,
                    payment_method=random.choice(payment_methods),
                    payment_date=payment_date,
                    status=random.choice(statuses) if random.random() > 0.7 else 'مكتمل',  # 70% مكتمل
                    payment_type=random.choice(payment_types),
                    reference_number=f"REF{random.randint(10000, 99999)}",
                    notes=f"دفعة {driver.payment_type or 'شهرية'} للسائق {driver.name}"
                )
                
                db.session.add(payment)
                payments_added += 1
        
        try:
            db.session.commit()
            print(f"✅ تم إضافة {payments_added} مدفوعة تجريبية بنجاح")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إضافة المدفوعات: {e}")
            return False

if __name__ == '__main__':
    print("🔄 بدء إضافة المدفوعات التجريبية...")
    success = add_sample_payments()
    
    if success:
        print("✅ انتهت العملية بنجاح")
    else:
        print("❌ فشلت العملية")
