#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التسجيل (إنشاء حساب جديد)
"""

import sys
import os
from datetime import datetime
from werkzeug.security import generate_password_hash

# إضافة مسار التطبيق
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_user_model():
    """اختبار نموذج المستخدم مع حقل الهاتف"""
    try:
        from app import app, db, User
        
        with app.app_context():
            print("🧪 اختبار إنشاء مستخدم جديد مع حقل الهاتف...")
            
            # إنشاء مستخدم تجريبي
            test_user = User(
                username='test_registration_user',
                email='<EMAIL>',
                full_name='مستخدم تجريبي للتسجيل',
                phone='0123456789',
                password_hash=generate_password_hash('test123456'),
                role='مستخدم',
                is_active=True
            )
            
            # إضافة المستخدم
            db.session.add(test_user)
            db.session.flush()  # للتحقق من صحة البيانات
            
            print(f"✅ تم إنشاء المستخدم بنجاح:")
            print(f"   - اسم المستخدم: {test_user.username}")
            print(f"   - البريد الإلكتروني: {test_user.email}")
            print(f"   - الاسم الكامل: {test_user.full_name}")
            print(f"   - رقم الهاتف: {test_user.phone}")
            print(f"   - الدور: {test_user.role}")
            
            # إلغاء العملية (لا نريد حفظ المستخدم التجريبي)
            db.session.rollback()
            
            return True
            
    except Exception as e:
        print(f"❌ فشل اختبار نموذج المستخدم: {e}")
        return False

def test_registration_route():
    """اختبار مسار التسجيل"""
    try:
        from app import app
        
        with app.test_client() as client:
            print("\n🌐 اختبار مسار التسجيل...")
            
            # اختبار GET request
            response = client.get('/register')
            if response.status_code == 200:
                print("✅ صفحة التسجيل تعمل بشكل صحيح (GET)")
            else:
                print(f"❌ خطأ في صفحة التسجيل: {response.status_code}")
                return False
            
            # اختبار POST request مع بيانات صحيحة
            test_data = {
                'username': 'test_user_' + str(int(datetime.now().timestamp())),
                'email': f'test_{int(datetime.now().timestamp())}@example.com',
                'full_name': 'مستخدم تجريبي',
                'phone': '0123456789',
                'password': 'test123456',
                'confirm_password': 'test123456'
            }
            
            response = client.post('/register', data=test_data, follow_redirects=True)
            
            if response.status_code == 200:
                if 'تم إنشاء الحساب بنجاح' in response.get_data(as_text=True):
                    print("✅ تم إنشاء الحساب بنجاح عبر النموذج")
                    return True
                elif 'تسجيل الدخول' in response.get_data(as_text=True):
                    print("✅ تم إعادة التوجيه إلى صفحة تسجيل الدخول")
                    return True
                else:
                    print("⚠️ تم إرسال النموذج ولكن لم يتم التأكيد")
                    return True
            else:
                print(f"❌ خطأ في إرسال نموذج التسجيل: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ فشل اختبار مسار التسجيل: {e}")
        return False

def test_database_schema():
    """اختبار مخطط قاعدة البيانات"""
    try:
        import sqlite3
        
        db_path = 'instance/driver_management.db'
        if not os.path.exists(db_path):
            print("❌ قاعدة البيانات غير موجودة")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من مخطط جدول المستخدمين
        cursor.execute("PRAGMA table_info(users)")
        columns = {column[1]: column[2] for column in cursor.fetchall()}
        
        required_columns = {
            'id': 'INTEGER',
            'username': 'VARCHAR(80)',
            'email': 'VARCHAR(120)',
            'full_name': 'VARCHAR(200)',
            'phone': 'VARCHAR(20)',
            'password_hash': 'VARCHAR(255)',
            'role': 'VARCHAR(50)',
            'is_active': 'BOOLEAN',
            'created_at': 'DATETIME'
        }
        
        print("\n📋 فحص مخطط جدول المستخدمين:")
        all_good = True
        
        for col_name, col_type in required_columns.items():
            if col_name in columns:
                print(f"   ✅ {col_name} ({columns[col_name]})")
            else:
                print(f"   ❌ {col_name} مفقود!")
                all_good = False
        
        conn.close()
        return all_good
        
    except Exception as e:
        print(f"❌ فشل فحص مخطط قاعدة البيانات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام التسجيل...")
    print("=" * 50)
    
    tests = [
        ("فحص مخطط قاعدة البيانات", test_database_schema),
        ("اختبار نموذج المستخدم", test_user_model),
        ("اختبار مسار التسجيل", test_registration_route)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} نجح")
            else:
                print(f"❌ {test_name} فشل")
        except Exception as e:
            print(f"❌ {test_name} فشل: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! نظام التسجيل يعمل بشكل صحيح")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
