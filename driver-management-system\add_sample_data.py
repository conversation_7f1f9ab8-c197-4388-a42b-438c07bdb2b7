#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية شاملة للنظام
Add comprehensive sample data for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import Driver, Payment, User
from datetime import datetime, timedelta
import random

def add_sample_drivers():
    """إضافة سائقين تجريبيين"""
    sample_drivers = [
        {
            'name': 'أحمد محمد علي',
            'phone': '**********',
            'national_id': '**********',
            'license_number': 'LIC001',
            'vehicle_type': 'سيارة صالون',
            'vehicle_plate': 'أ ب ج 123',
            'payment_type': 'شهري',
            'payment_amount': 800.0,
            'status': 'نشط',
            'rating': 4.5
        },
        {
            'name': 'محمد عبدالله سالم',
            'phone': '**********',
            'national_id': '**********',
            'license_number': 'LIC002',
            'vehicle_type': 'باص صغير',
            'vehicle_plate': 'د هـ و 456',
            'payment_type': 'أسبوعي',
            'payment_amount': 200.0,
            'status': 'نشط',
            'rating': 5.0
        },
        {
            'name': 'سالم أحمد محمد',
            'phone': '**********',
            'national_id': '**********',
            'license_number': 'LIC003',
            'vehicle_type': 'شاحنة صغيرة',
            'vehicle_plate': 'ز ح ط 789',
            'payment_type': 'شهري',
            'payment_amount': 1200.0,
            'status': 'معلق',
            'rating': 3.5
        },
        {
            'name': 'عبدالرحمن خالد',
            'phone': '**********',
            'national_id': '**********',
            'license_number': 'LIC004',
            'vehicle_type': 'سيارة صالون',
            'vehicle_plate': 'ي ك ل 012',
            'payment_type': 'شهري',
            'payment_amount': 750.0,
            'status': 'نشط',
            'rating': 4.0
        },
        {
            'name': 'خالد عبدالعزيز',
            'phone': '**********',
            'national_id': '**********',
            'license_number': 'LIC005',
            'vehicle_type': 'دراجة نارية',
            'vehicle_plate': 'م ن س 345',
            'payment_type': 'أسبوعي',
            'payment_amount': 150.0,
            'status': 'غير نشط',
            'rating': 2.5
        }
    ]
    
    drivers_added = 0
    for driver_data in sample_drivers:
        # التحقق من عدم وجود السائق
        existing = Driver.query.filter_by(phone=driver_data['phone']).first()
        if not existing:
            driver = Driver(**driver_data)
            db.session.add(driver)
            drivers_added += 1
    
    return drivers_added

def add_sample_payments():
    """إضافة مدفوعات تجريبية"""
    drivers = Driver.query.all()
    
    if not drivers:
        return 0
    
    payment_methods = ['تحويل بنكي', 'كاش', 'شيك']
    statuses = ['مكتمل', 'معلق', 'ملغي']
    payment_types = ['شهري', 'أسبوعي', 'يومي']
    
    payments_added = 0
    
    for driver in drivers:
        # إضافة 3-8 مدفوعات لكل سائق
        num_payments = random.randint(3, 8)
        
        for i in range(num_payments):
            # تاريخ عشوائي في آخر 12 شهر
            days_ago = random.randint(1, 365)
            payment_date = datetime.now() - timedelta(days=days_ago)
            
            # مبلغ بناءً على نوع دفع السائق
            base_amount = driver.payment_amount or 500
            amount = base_amount + random.randint(-50, 100)
            
            # تحديد الحالة (80% مكتمل)
            status = 'مكتمل' if random.random() > 0.2 else random.choice(['معلق', 'ملغي'])
            
            payment = Payment(
                driver_id=driver.id,
                amount=amount,
                payment_method=random.choice(payment_methods),
                payment_date=payment_date,
                status=status,
                payment_type=driver.payment_type,
                reference_number=f"REF{random.randint(10000, 99999)}",
                notes=f"دفعة {driver.payment_type} للسائق {driver.name}"
            )
            
            db.session.add(payment)
            payments_added += 1
    
    return payments_added

def main():
    """الدالة الرئيسية"""
    with app.app_context():
        print("🔄 بدء إضافة البيانات التجريبية...")
        
        try:
            # إضافة السائقين
            drivers_added = add_sample_drivers()
            print(f"✅ تم إضافة {drivers_added} سائق جديد")
            
            # إضافة المدفوعات
            payments_added = add_sample_payments()
            print(f"✅ تم إضافة {payments_added} مدفوعة")
            
            # حفظ التغييرات
            db.session.commit()
            print("✅ تم حفظ جميع البيانات بنجاح")
            
            # عرض الإحصائيات
            total_drivers = Driver.query.count()
            total_payments = Payment.query.count()
            print(f"📊 إجمالي السائقين: {total_drivers}")
            print(f"📊 إجمالي المدفوعات: {total_payments}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إضافة البيانات: {e}")
            return False

if __name__ == '__main__':
    success = main()
    
    if success:
        print("✅ انتهت العملية بنجاح")
    else:
        print("❌ فشلت العملية")
