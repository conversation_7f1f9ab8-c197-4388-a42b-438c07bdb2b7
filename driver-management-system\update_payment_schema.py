#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث مخطط قاعدة البيانات لإضافة حقل الحالة للمدفوعات
Update database schema to add status field to payments
"""

import sqlite3
import os

def update_payment_schema():
    """تحديث مخطط جدول المدفوعات"""
    db_path = 'instance/driver_management.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود عمود status
        cursor.execute("PRAGMA table_info(payments)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'status' not in columns:
            print("🔄 إضافة عمود status إلى جدول payments...")
            cursor.execute("ALTER TABLE payments ADD COLUMN status VARCHAR(20) DEFAULT 'مكتمل'")
            print("✅ تم إضافة عمود status")
        else:
            print("✅ عمود status موجود بالفعل")
        
        if 'payment_type' not in columns:
            print("🔄 إضافة عمود payment_type إلى جدول payments...")
            cursor.execute("ALTER TABLE payments ADD COLUMN payment_type VARCHAR(50)")
            print("✅ تم إضافة عمود payment_type")
        else:
            print("✅ عمود payment_type موجود بالفعل")
        
        # تحديث المدفوعات الموجودة لتكون مكتملة
        cursor.execute("UPDATE payments SET status = 'مكتمل' WHERE status IS NULL")
        
        conn.commit()
        conn.close()
        
        print("✅ تم تحديث مخطط قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

if __name__ == '__main__':
    print("🔄 بدء تحديث مخطط قاعدة البيانات...")
    success = update_payment_schema()
    
    if success:
        print("✅ انتهى التحديث بنجاح")
    else:
        print("❌ فشل التحديث")
