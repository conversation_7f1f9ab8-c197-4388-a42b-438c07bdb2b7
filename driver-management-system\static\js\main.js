// نظام إدارة السائقين - JavaScript الرئيسي

$(document).ready(function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة البحث الذكي
    initializeSmartSearch();
    
    // تهيئة رفع الملفات
    initializeFileUpload();
    
    // تهيئة الإشعارات
    initializeNotifications();
});

// تهيئة التطبيق
function initializeApp() {
    // إضافة تأثيرات الرسوم المتحركة
    $('.card').addClass('fade-in');
    
    // تهيئة التلميحات
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // تهيئة النوافذ المنبثقة
    $('[data-bs-toggle="popover"]').popover();
    
    // تحديث الوقت كل دقيقة
    updateTime();
    setInterval(updateTime, 60000);
}

// البحث الذكي
function initializeSmartSearch() {
    const searchInput = $('#smartSearch');
    const searchResults = $('#searchResults');
    
    if (searchInput.length) {
        searchInput.on('input', function() {
            const query = $(this).val().trim();
            
            if (query.length >= 2) {
                performSmartSearch(query);
            } else {
                searchResults.hide();
            }
        });
        
        // إخفاء النتائج عند النقر خارجها
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-container').length) {
                searchResults.hide();
            }
        });
    }
}

// تنفيذ البحث الذكي
function performSmartSearch(query) {
    $.ajax({
        url: '/api/search',
        method: 'GET',
        data: { q: query },
        success: function(data) {
            displaySearchResults(data);
        },
        error: function() {
            showNotification('حدث خطأ في البحث', 'error');
        }
    });
}

// عرض نتائج البحث
function displaySearchResults(data) {
    const searchResults = $('#searchResults');
    let html = '';

    if (!data.success) {
        html = '<div class="search-no-results text-danger">' + data.message + '</div>';
        searchResults.html(html).show();
        return;
    }

    const results = data.results;

    if (results.drivers && results.drivers.length > 0) {
        html += '<div class="search-category">';
        html += '<h6 class="search-category-title"><i class="fas fa-users me-2"></i>السائقين</h6>';
        results.drivers.forEach(driver => {
            const statusClass = driver.status === 'نشط' ? 'success' : 'secondary';
            html += `
                <div class="search-item" onclick="goToDriver(${driver.id})">
                    <div class="search-item-content">
                        <div class="search-item-main">
                            <i class="fas fa-user me-2 text-primary"></i>
                            <span class="search-item-title">${driver.name}</span>
                            <span class="badge bg-${statusClass} ms-2">${driver.status}</span>
                        </div>
                        <div class="search-item-details">
                            <small class="text-muted">
                                <i class="fas fa-phone me-1"></i>${driver.phone} |
                                <i class="fas fa-car me-1"></i>${driver.vehicle_type} - ${driver.vehicle_plate}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    if (results.payments && results.payments.length > 0) {
        html += '<div class="search-category">';
        html += '<h6 class="search-category-title"><i class="fas fa-money-bill-wave me-2"></i>المدفوعات</h6>';
        results.payments.forEach(payment => {
            const statusClass = payment.status === 'مكتمل' ? 'success' : 'warning';
            html += `
                <div class="search-item" onclick="goToPayment(${payment.id})">
                    <div class="search-item-content">
                        <div class="search-item-main">
                            <i class="fas fa-money-bill me-2 text-success"></i>
                            <span class="search-item-title">${payment.driver_name}</span>
                            <span class="badge bg-${statusClass} ms-2">${payment.status}</span>
                        </div>
                        <div class="search-item-details">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>${payment.payment_date} |
                                <i class="fas fa-coins me-1"></i>${payment.amount} ريال |
                                ${payment.payment_method}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    if (results.documents && results.documents.length > 0) {
        html += '<div class="search-category">';
        html += '<h6 class="search-category-title"><i class="fas fa-file-alt me-2"></i>المستندات</h6>';
        results.documents.forEach(document => {
            html += `
                <div class="search-item" onclick="goToDocument(${document.id})">
                    <div class="search-item-content">
                        <div class="search-item-main">
                            <i class="fas fa-file me-2 text-info"></i>
                            <span class="search-item-title">${document.driver_name}</span>
                            <span class="badge bg-info ms-2">${document.document_type}</span>
                        </div>
                        <div class="search-item-details">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>${document.upload_date} |
                                <i class="fas fa-file-alt me-1"></i>${document.file_name}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    if (html === '') {
        html = '<div class="search-no-results"><i class="fas fa-search me-2"></i>لا توجد نتائج للبحث عن "' + data.query + '"</div>';
    } else {
        html += '<div class="search-footer"><small class="text-muted">تم العثور على ' + results.total + ' نتيجة</small></div>';
    }

    searchResults.html(html).show();
}

// الانتقال إلى صفحة السائق
function goToDriver(driverId) {
    window.location.href = `/drivers/${driverId}`;
}

// الانتقال إلى صفحة المدفوعة
function goToPayment(paymentId) {
    window.location.href = `/payments/${paymentId}`;
}

// الانتقال إلى صفحة المستندات
function goToDocument(documentId) {
    window.location.href = `/documents?document_id=${documentId}`;
}

// تهيئة رفع الملفات
function initializeFileUpload() {
    const uploadArea = $('.file-upload-area');
    
    if (uploadArea.length) {
        // منع السلوك الافتراضي للسحب والإفلات
        uploadArea.on('dragover dragenter', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('dragover');
        });
        
        uploadArea.on('dragleave dragend', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
        });
        
        uploadArea.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
            
            const files = e.originalEvent.dataTransfer.files;
            handleFileUpload(files);
        });
        
        // رفع الملفات عند النقر
        uploadArea.on('click', function() {
            $('#fileInput').click();
        });
        
        $('#fileInput').on('change', function() {
            const files = this.files;
            handleFileUpload(files);
        });
    }
}

// معالجة رفع الملفات
function handleFileUpload(files) {
    if (files.length === 0) return;
    
    const formData = new FormData();
    
    for (let i = 0; i < files.length; i++) {
        formData.append('files[]', files[i]);
    }
    
    // عرض شريط التقدم
    showUploadProgress();
    
    $.ajax({
        url: '/api/upload',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    updateUploadProgress(percentComplete);
                }
            });
            return xhr;
        },
        success: function(response) {
            hideUploadProgress();
            showNotification('تم رفع الملفات بنجاح', 'success');
            
            // تحديث قائمة الملفات
            if (typeof updateFilesList === 'function') {
                updateFilesList(response.files);
            }
        },
        error: function() {
            hideUploadProgress();
            showNotification('حدث خطأ في رفع الملفات', 'error');
        }
    });
}

// عرض شريط التقدم
function showUploadProgress() {
    const progressHtml = `
        <div id="uploadProgress" class="mt-3">
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted">جاري رفع الملفات...</small>
        </div>
    `;
    $('.file-upload-area').after(progressHtml);
}

// تحديث شريط التقدم
function updateUploadProgress(percent) {
    $('#uploadProgress .progress-bar').css('width', percent + '%');
}

// إخفاء شريط التقدم
function hideUploadProgress() {
    $('#uploadProgress').remove();
}

// تهيئة الإشعارات
function initializeNotifications() {
    // طلب إذن الإشعارات
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
    
    // التحقق من الإشعارات الجديدة كل 5 دقائق
    checkNotifications();
    setInterval(checkNotifications, 300000);
}

// التحقق من الإشعارات
function checkNotifications() {
    $.ajax({
        url: '/api/notifications',
        method: 'GET',
        success: function(data) {
            if (data.notifications && data.notifications.length > 0) {
                data.notifications.forEach(notification => {
                    showDesktopNotification(notification.title, notification.message);
                });
            }
        }
    });
}

// عرض إشعار سطح المكتب
function showDesktopNotification(title, message) {
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: message,
            icon: '/static/images/logo.png',
            dir: 'rtl'
        });
    }
}

// عرض الإشعارات داخل التطبيق
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
    const icon = getNotificationIcon(type);
    
    const notificationHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show notification-toast" role="alert">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة الإشعار إلى أعلى الصفحة
    $('main').prepend(notificationHtml);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.notification-toast').fadeOut();
    }, 5000);
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    return icons[type] || icons['info'];
}

// تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    $('#currentTime').text(timeString);
}

// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// تصدير البيانات
function exportData(format, endpoint) {
    const url = `/api/export/${endpoint}?format=${format}`;
    window.open(url, '_blank');
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تحديث الموقع الجغرافي
function updateLocation(driverId) {
    if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                
                $.ajax({
                    url: `/api/drivers/${driverId}/location`,
                    method: 'POST',
                    data: {
                        latitude: lat,
                        longitude: lng
                    },
                    success: function() {
                        showNotification('تم تحديث الموقع بنجاح', 'success');
                    },
                    error: function() {
                        showNotification('حدث خطأ في تحديث الموقع', 'error');
                    }
                });
            },
            function() {
                showNotification('لا يمكن الحصول على الموقع الحالي', 'warning');
            }
        );
    } else {
        showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
    }
}

// تحديث الإحصائيات في الوقت الفعلي
function updateDashboardStats() {
    $.ajax({
        url: '/api/dashboard/stats',
        method: 'GET',
        success: function(data) {
            // تحديث الإحصائيات
            $('#totalDrivers').text(data.total_drivers);
            $('#activeDrivers').text(data.active_drivers);
            $('#lateDrivers').text(data.late_drivers);
            $('#monthlyRevenue').text(data.monthly_revenue.toLocaleString() + ' ريال');
        }
    });
}

// تهيئة الخريطة (سيتم تطويرها لاحقاً)
function initializeMap() {
    // سيتم إضافة كود Google Maps هنا
}

// معالجة الأخطاء العامة
window.addEventListener('error', function(e) {
    console.error('خطأ في JavaScript:', e.error);
    showNotification('حدث خطأ غير متوقع', 'error');
});

// معالجة الأخطاء في AJAX
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    if (xhr.status === 401) {
        window.location.href = '/login';
    } else if (xhr.status === 403) {
        showNotification('ليس لديك صلاحية للقيام بهذا الإجراء', 'error');
    } else if (xhr.status >= 500) {
        showNotification('حدث خطأ في الخادم', 'error');
    }
});
