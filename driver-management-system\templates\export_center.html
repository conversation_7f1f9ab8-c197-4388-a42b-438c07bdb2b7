{% extends "base.html" %}

{% block title %}مركز التصدير{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-download me-2"></i>
                    مركز التصدير المتقدم
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">مركز التصدير</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.total_drivers }}</h4>
                            <p class="mb-0">إجمالي السائقين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.active_drivers }}</h4>
                            <p class="mb-0">السائقين النشطين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.total_payments }}</h4>
                            <p class="mb-0">إجمالي المدفوعات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.this_month_payments }}</h4>
                            <p class="mb-0">مدفوعات هذا الشهر</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- خيارات التصدير -->
    <div class="row">
        <!-- تصدير Excel -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-excel me-2"></i>
                        تصدير Excel
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تصدير البيانات إلى ملفات Excel مع تنسيق احترافي ورسوم بيانية</p>
                    
                    <!-- تصدير قائمة السائقين -->
                    <div class="mb-3">
                        <h6 class="text-primary">قائمة السائقين</h6>
                        <form method="GET" action="{{ url_for('export_drivers_excel') }}" class="d-inline">
                            <div class="row g-2 mb-2">
                                <div class="col-md-6">
                                    <select name="status" class="form-select form-select-sm">
                                        <option value="">جميع الحالات</option>
                                        <option value="نشط">نشط</option>
                                        <option value="غير نشط">غير نشط</option>
                                        <option value="معلق">معلق</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <select name="payment_type" class="form-select form-select-sm">
                                        <option value="">جميع أنواع الدفع</option>
                                        <option value="شهري">شهري</option>
                                        <option value="أسبوعي">أسبوعي</option>
                                        <option value="يومي">يومي</option>
                                    </select>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success btn-sm">
                                <i class="fas fa-download me-1"></i>
                                تصدير قائمة السائقين
                            </button>
                        </form>
                    </div>

                    <hr>

                    <!-- تصدير تقرير المدفوعات -->
                    <div class="mb-3">
                        <h6 class="text-primary">تقرير المدفوعات</h6>
                        <form method="GET" action="{{ url_for('export_payments_excel') }}" class="d-inline">
                            <div class="row g-2 mb-2">
                                <div class="col-md-6">
                                    <label class="form-label small">من تاريخ</label>
                                    <input type="date" name="start_date" class="form-control form-control-sm">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label small">إلى تاريخ</label>
                                    <input type="date" name="end_date" class="form-control form-control-sm">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success btn-sm">
                                <i class="fas fa-chart-bar me-1"></i>
                                تصدير تقرير المدفوعات
                            </button>
                        </form>
                    </div>

                    <hr>

                    <!-- التقرير المالي الشامل -->
                    <div class="mb-3">
                        <h6 class="text-primary">التقرير المالي الشامل</h6>
                        <form method="GET" action="{{ url_for('export_financial_excel') }}" class="d-inline">
                            <div class="row g-2 mb-2">
                                <div class="col-md-6">
                                    <label class="form-label small">السنة</label>
                                    <select name="year" class="form-select form-select-sm">
                                        {% for year in range(2020, 2030) %}
                                        <option value="{{ year }}" {% if year == 2024 %}selected{% endif %}>{{ year }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label small">الشهر (اختياري)</label>
                                    <select name="month" class="form-select form-select-sm">
                                        <option value="">جميع الأشهر</option>
                                        <option value="1">يناير</option>
                                        <option value="2">فبراير</option>
                                        <option value="3">مارس</option>
                                        <option value="4">أبريل</option>
                                        <option value="5">مايو</option>
                                        <option value="6">يونيو</option>
                                        <option value="7">يوليو</option>
                                        <option value="8">أغسطس</option>
                                        <option value="9">سبتمبر</option>
                                        <option value="10">أكتوبر</option>
                                        <option value="11">نوفمبر</option>
                                        <option value="12">ديسمبر</option>
                                    </select>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success btn-sm">
                                <i class="fas fa-chart-pie me-1"></i>
                                تصدير التقرير المالي
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- تصدير PDF -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-pdf me-2"></i>
                        تصدير PDF
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تصدير التقارير المفصلة إلى ملفات PDF مع تخطيط احترافي</p>
                    
                    <!-- تقارير السائقين الفردية -->
                    <div class="mb-3">
                        <h6 class="text-primary">تقارير السائقين الفردية</h6>
                        <p class="small text-muted">يمكنك تصدير تقرير مفصل لأي سائق من صفحة السائقين</p>
                        <a href="{{ url_for('drivers') }}" class="btn btn-danger btn-sm">
                            <i class="fas fa-users me-1"></i>
                            انتقل إلى صفحة السائقين
                        </a>
                    </div>

                    <hr>

                    <!-- تقارير مخصصة -->
                    <div class="mb-3">
                        <h6 class="text-primary">تقارير مخصصة</h6>
                        <p class="small text-muted">قريباً: إمكانية إنشاء تقارير PDF مخصصة مع شعار الشركة</p>
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="fas fa-cog me-1"></i>
                            قيد التطوير
                        </button>
                    </div>

                    <hr>

                    <!-- فواتير المدفوعات -->
                    <div class="mb-3">
                        <h6 class="text-primary">فواتير المدفوعات</h6>
                        <p class="small text-muted">قريباً: إنشاء فواتير PDF للمدفوعات مع تفاصيل كاملة</p>
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="fas fa-receipt me-1"></i>
                            قيد التطوير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات مهمة حول التصدير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">ميزات تصدير Excel:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>تنسيق احترافي مع خطوط عربية</li>
                                <li><i class="fas fa-check text-success me-2"></i>رسوم بيانية تفاعلية</li>
                                <li><i class="fas fa-check text-success me-2"></i>إحصائيات مفصلة</li>
                                <li><i class="fas fa-check text-success me-2"></i>فلترة البيانات حسب التاريخ والحالة</li>
                                <li><i class="fas fa-check text-success me-2"></i>تخطيط مناسب للطباعة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">ميزات تصدير PDF:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>تقارير مفصلة لكل سائق</li>
                                <li><i class="fas fa-check text-success me-2"></i>دعم كامل للغة العربية</li>
                                <li><i class="fas fa-check text-success me-2"></i>تخطيط احترافي للطباعة</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>شعار الشركة (قيد التطوير)</li>
                                <li><i class="fas fa-clock text-warning me-2"></i>فواتير المدفوعات (قيد التطوير)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ملاحظة:</strong> جميع الملفات المصدرة يتم حفظها في مجلد "exports" ويمكن الوصول إليها من خلال روابط التحميل المباشر.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.form-control-sm, .form-select-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.text-muted {
    color: #6c757d !important;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}
</style>
{% endblock %}
