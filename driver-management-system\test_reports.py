#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صفحة التقارير
"""

import requests
import sys

def test_reports_page():
    """اختبار صفحة التقارير"""
    base_url = "http://localhost:5000"
    
    print("🔍 اختبار صفحة التقارير...")
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        # تسجيل الدخول أولاً
        print("1. تسجيل الدخول...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة التقارير الرئيسية
        print("2. اختبار صفحة التقارير الرئيسية...")
        reports_page = session.get(f"{base_url}/reports")
        
        if reports_page.status_code == 200:
            print("✅ صفحة التقارير تعمل")
            
            # التحقق من وجود العناصر المهمة
            content = reports_page.text
            
            # التحقق من وجود الإحصائيات
            if "إجمالي السائقين" in content:
                print("✅ إحصائيات السائقين موجودة")
            else:
                print("⚠️ إحصائيات السائقين مفقودة")
                
            if "إجمالي المدفوعات" in content:
                print("✅ إحصائيات المدفوعات موجودة")
            else:
                print("⚠️ إحصائيات المدفوعات مفقودة")
                
            # التحقق من وجود الرسوم البيانية
            if "chart" in content.lower() or "canvas" in content.lower():
                print("✅ الرسوم البيانية موجودة")
            else:
                print("⚠️ الرسوم البيانية مفقودة")
                
            # التحقق من وجود أزرار التصدير
            if "تصدير" in content:
                print("✅ أزرار التصدير موجودة")
            else:
                print("⚠️ أزرار التصدير مفقودة")
                
        else:
            print(f"❌ صفحة التقارير: خطأ {reports_page.status_code}")
            return False
            
        # اختبار تقرير السائقين
        print("3. اختبار تقرير السائقين...")
        drivers_report = session.get(f"{base_url}/reports/drivers")
        
        if drivers_report.status_code == 200:
            print("✅ تقرير السائقين يعمل")
        else:
            print(f"❌ تقرير السائقين: خطأ {drivers_report.status_code}")
            
        # اختبار تقرير المدفوعات
        print("4. اختبار تقرير المدفوعات...")
        payments_report = session.get(f"{base_url}/reports/payments")
        
        if payments_report.status_code == 200:
            print("✅ تقرير المدفوعات يعمل")
        else:
            print(f"❌ تقرير المدفوعات: خطأ {payments_report.status_code}")
            
        # اختبار التقرير المالي
        print("5. اختبار التقرير المالي...")
        financial_report = session.get(f"{base_url}/reports/financial")
        
        if financial_report.status_code == 200:
            print("✅ التقرير المالي يعمل")
        else:
            print(f"❌ التقرير المالي: خطأ {financial_report.status_code}")
            
        # اختبار تقرير الأداء
        print("6. اختبار تقرير الأداء...")
        performance_report = session.get(f"{base_url}/reports/performance")
        
        if performance_report.status_code == 200:
            print("✅ تقرير الأداء يعمل")
        else:
            print(f"❌ تقرير الأداء: خطأ {performance_report.status_code}")
            
        # اختبار تقرير مخصص
        print("7. اختبار التقرير المخصص...")
        custom_report = session.post(f"{base_url}/reports/custom", data={
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "report_type": "drivers",
            "status": "نشط"
        })
        
        if custom_report.status_code == 200:
            print("✅ التقرير المخصص يعمل")
        else:
            print(f"❌ التقرير المخصص: خطأ {custom_report.status_code}")
            
        print("\n✅ انتهى اختبار صفحة التقارير")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    test_reports_page()
