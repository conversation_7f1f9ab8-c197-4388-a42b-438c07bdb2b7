#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة إنشاء قاعدة البيانات مع المخطط الجديد
Recreate database with new schema
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import User, Driver, Payment, Settings, Role, ActivityLog, BackupLog, Document, AIAnalysis
from werkzeug.security import generate_password_hash

def recreate_database():
    """إعادة إنشاء قاعدة البيانات"""
    with app.app_context():
        try:
            print("🔄 إعادة إنشاء قاعدة البيانات...")
            
            # حذف جميع الجداول
            db.drop_all()
            print("✅ تم حذف الجداول القديمة")
            
            # إنشاء الجداول الجديدة
            db.create_all()
            print("✅ تم إنشاء الجداول الجديدة")
            
            # إنشاء مستخدم مدير
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='مدير',
                is_active=True,
                failed_login_attempts=0,
                two_factor_enabled=False
            )
            admin_user.set_password('Admin@123!')
            db.session.add(admin_user)
            
            # إضافة بعض الإعدادات الأساسية
            default_settings = [
                ('company_name', 'شركة النقل المتميز', 'general', 'اسم الشركة', 'string'),
                ('company_phone', '0501234567', 'general', 'هاتف الشركة', 'string'),
                ('company_address', 'الرياض، المملكة العربية السعودية', 'general', 'عنوان الشركة', 'string'),
                ('notification_enabled', 'true', 'notifications', 'تفعيل الإشعارات', 'boolean'),
                ('backup_enabled', 'true', 'backup', 'تفعيل النسخ الاحتياطي', 'boolean')
            ]

            for key, value, category, description, data_type in default_settings:
                setting = Settings(
                    key=key,
                    value=value,
                    category=category,
                    description=description,
                    data_type=data_type
                )
                db.session.add(setting)
            
            db.session.commit()
            print("✅ تم إنشاء المستخدم المدير والإعدادات الأساسية")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إعادة إنشاء قاعدة البيانات: {e}")
            return False

if __name__ == '__main__':
    success = recreate_database()
    
    if success:
        print("✅ تم إعادة إنشاء قاعدة البيانات بنجاح")
        print("👤 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: Admin@123!")
    else:
        print("❌ فشل في إعادة إنشاء قاعدة البيانات")
