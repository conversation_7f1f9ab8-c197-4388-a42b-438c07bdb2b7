#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات نظام إدارة السائقين
Configuration for Driver Management System
"""

import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

class Config:
    """الإعدادات الأساسية"""
    
    # إعدادات Flask
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'sqlite:///driver_management.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # إعدادات رفع الملفات
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    REPORTS_FOLDER = os.getenv('REPORTS_FOLDER', 'reports')
    DOCUMENTS_FOLDER = os.getenv('DOCUMENTS_FOLDER', 'documents')
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls', 'csv'}
    
    # إعدادات Google Maps
    GOOGLE_MAPS_API_KEY = os.getenv('GOOGLE_MAPS_API_KEY', '')
    
    # إعدادات البريد الإلكتروني
    MAIL_SERVER = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.getenv('MAIL_PORT', 587))
    MAIL_USE_TLS = os.getenv('MAIL_USE_TLS', 'True').lower() == 'true'
    MAIL_USERNAME = os.getenv('MAIL_USERNAME', '')
    MAIL_PASSWORD = os.getenv('MAIL_PASSWORD', '')
    MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', '')
    
    # إعدادات Twilio (WhatsApp/SMS)
    TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID', '')
    TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN', '')
    TWILIO_PHONE_NUMBER = os.getenv('TWILIO_PHONE_NUMBER', '')
    TWILIO_WHATSAPP_NUMBER = os.getenv('TWILIO_WHATSAPP_NUMBER', '')
    
    # إعدادات الذكاء الاصطناعي
    AI_ENABLED = os.getenv('AI_ENABLED', 'True').lower() == 'true'
    AI_PREDICTION_THRESHOLD = float(os.getenv('AI_PREDICTION_THRESHOLD', 0.7))
    AI_ANALYSIS_INTERVAL_DAYS = int(os.getenv('AI_ANALYSIS_INTERVAL_DAYS', 7))
    
    # إعدادات التقارير
    REPORTS_RETENTION_DAYS = int(os.getenv('REPORTS_RETENTION_DAYS', 90))
    AUTO_REPORT_GENERATION = os.getenv('AUTO_REPORT_GENERATION', 'True').lower() == 'true'
    
    # إعدادات الأمان
    SESSION_COOKIE_SECURE = os.getenv('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = int(os.getenv('PERMANENT_SESSION_LIFETIME', 3600))  # 1 hour
    
    # إعدادات التخزين المؤقت
    CACHE_TYPE = os.getenv('CACHE_TYPE', 'simple')
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', 300))  # 5 minutes
    
    # إعدادات التسجيل
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'driver_management.log')
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق مع الإعدادات"""
        # إنشاء المجلدات المطلوبة
        folders = [
            app.config['UPLOAD_FOLDER'],
            app.config['REPORTS_FOLDER'],
            app.config['DOCUMENTS_FOLDER']
        ]
        
        for folder in folders:
            os.makedirs(folder, exist_ok=True)

class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    TESTING = False
    
    # إعدادات قاعدة البيانات للتطوير
    SQLALCHEMY_DATABASE_URI = os.getenv('DEV_DATABASE_URL', 'sqlite:///driver_management_dev.db')
    
    # تفعيل التسجيل المفصل
    SQLALCHEMY_ECHO = True

class TestingConfig(Config):
    """إعدادات الاختبار"""
    TESTING = True
    DEBUG = True
    
    # قاعدة بيانات في الذاكرة للاختبارات
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # تعطيل CSRF للاختبارات
    WTF_CSRF_ENABLED = False
    
    # تعطيل الإشعارات في الاختبارات
    MAIL_SUPPRESS_SEND = True

class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    TESTING = False
    
    # إعدادات الأمان للإنتاج
    SESSION_COOKIE_SECURE = True
    
    # قاعدة بيانات PostgreSQL للإنتاج
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', '').replace('postgres://', 'postgresql://')
    
    # إعدادات التسجيل للإنتاج
    LOG_LEVEL = 'WARNING'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # إعداد التسجيل للإنتاج
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            file_handler = RotatingFileHandler(
                cls.LOG_FILE,
                maxBytes=10240000,  # 10MB
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('Driver Management System startup')

# قاموس الإعدادات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """الحصول على إعدادات البيئة الحالية"""
    return config[os.getenv('FLASK_ENV', 'default')]
