#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لنظام إدارة السائقين
Database Models for Driver Management System
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
import json

# إنشاء مثيل قاعدة البيانات
db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False, default='مشاهد')  # مدير، مشرف، مشاهد
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    def set_password(self, password):
        """تعيين كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, action):
        """التحقق من الصلاحيات"""
        permissions = {
            'مدير': ['create', 'read', 'update', 'delete', 'admin'],
            'مشرف': ['create', 'read', 'update'],
            'مشاهد': ['read']
        }
        return action in permissions.get(self.role, [])

class Driver(db.Model):
    """نموذج السائقين"""
    __tablename__ = 'drivers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20), unique=True, nullable=False)
    national_id = db.Column(db.String(20), unique=True)
    license_number = db.Column(db.String(50))
    vehicle_type = db.Column(db.String(100))
    vehicle_plate = db.Column(db.String(20))
    
    # نوع الدفع والحالة
    payment_type = db.Column(db.String(20), nullable=False)  # شهري، أسبوعي
    payment_amount = db.Column(db.Float, nullable=False, default=0.0)
    payment_status = db.Column(db.String(20), default='ملتزم')  # ملتزم، متوسط، متأخر
    
    # الموقع الجغرافي
    current_latitude = db.Column(db.Float)
    current_longitude = db.Column(db.Float)
    current_address = db.Column(db.Text)
    
    # معلومات إضافية
    status = db.Column(db.String(20), default='نشط')  # نشط، غير نشط، معلق
    notes = db.Column(db.Text)
    rating = db.Column(db.Float, default=5.0)  # تقييم من 1-5
    
    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_payment_date = db.Column(db.DateTime)
    next_payment_due = db.Column(db.DateTime)
    
    # العلاقات
    payments = db.relationship('Payment', backref='driver', lazy=True, cascade='all, delete-orphan')
    documents = db.relationship('Document', backref='driver', lazy=True, cascade='all, delete-orphan')
    ai_analysis = db.relationship('AIAnalysis', backref='driver', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Driver {self.name}>'
    
    def calculate_total_paid(self):
        """حساب إجمالي المدفوعات"""
        return sum(payment.amount for payment in self.payments)
    
    def calculate_outstanding_amount(self):
        """حساب المبلغ المستحق"""
        # حساب عدد الفترات المستحقة
        if not self.next_payment_due:
            return 0
        
        today = datetime.now().date()
        due_date = self.next_payment_due.date()
        
        if due_date <= today:
            if self.payment_type == 'أسبوعي':
                weeks_overdue = (today - due_date).days // 7
                return (weeks_overdue + 1) * self.payment_amount
            elif self.payment_type == 'شهري':
                months_overdue = (today.year - due_date.year) * 12 + (today.month - due_date.month)
                return (months_overdue + 1) * self.payment_amount
        
        return 0
    
    def update_payment_status(self):
        """تحديث حالة الدفع تلقائياً"""
        outstanding = self.calculate_outstanding_amount()
        
        if outstanding == 0:
            self.payment_status = 'ملتزم'
        elif outstanding <= self.payment_amount:
            self.payment_status = 'متوسط'
        else:
            self.payment_status = 'متأخر'
        
        db.session.commit()
    
    def get_location_dict(self):
        """الحصول على الموقع كقاموس"""
        if self.current_latitude and self.current_longitude:
            return {
                'lat': self.current_latitude,
                'lng': self.current_longitude,
                'address': self.current_address or ''
            }
        return None

class Payment(db.Model):
    """نموذج المدفوعات"""
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)  # تحويل، كاش، شيك
    payment_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    reference_number = db.Column(db.String(100))  # رقم المرجع للتحويل
    notes = db.Column(db.Text)
    
    # معلومات إضافية
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Payment {self.amount} for Driver {self.driver_id}>'

class Document(db.Model):
    """نموذج المستندات"""
    __tablename__ = 'documents'
    
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'), nullable=False)
    document_type = db.Column(db.String(100), nullable=False)  # رخصة، هوية، إيصال دفع
    file_name = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    def __repr__(self):
        return f'<Document {self.document_type} for Driver {self.driver_id}>'

class AIAnalysis(db.Model):
    """نموذج تحليلات الذكاء الاصطناعي"""
    __tablename__ = 'ai_analysis'
    
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'), nullable=False)
    analysis_type = db.Column(db.String(100), nullable=False)  # payment_pattern, delay_prediction, performance
    analysis_data = db.Column(db.Text)  # JSON data
    prediction_score = db.Column(db.Float)
    recommendations = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_analysis_data(self):
        """الحصول على بيانات التحليل كقاموس"""
        if self.analysis_data:
            return json.loads(self.analysis_data)
        return {}
    
    def set_analysis_data(self, data):
        """تعيين بيانات التحليل"""
        self.analysis_data = json.dumps(data, ensure_ascii=False)
    
    def __repr__(self):
        return f'<AIAnalysis {self.analysis_type} for Driver {self.driver_id}>'
