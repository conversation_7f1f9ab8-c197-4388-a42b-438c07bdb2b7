#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لنظام إدارة السائقين
Database Models for Driver Management System
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
import json

# إنشاء مثيل قاعدة البيانات
db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False, default='مشاهد')  # مدير، مشرف، مشاهد
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # ميزات الأمان المتقدمة
    failed_login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    password_reset_token = db.Column(db.String(100))
    password_reset_expires = db.Column(db.DateTime)
    two_factor_secret = db.Column(db.String(32))
    two_factor_enabled = db.Column(db.Boolean, default=False)
    last_password_change = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        """تعيين كلمة المرور"""
        if not self.is_password_strong(password):
            raise ValueError("كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل، حروف كبيرة وصغيرة، أرقام ورموز")

        self.password_hash = generate_password_hash(password)
        self.last_password_change = datetime.utcnow()

    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def is_password_strong(self, password):
        """التحقق من قوة كلمة المرور"""
        if len(password) < 8:
            return False

        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

        return has_upper and has_lower and has_digit and has_special

    def is_account_locked(self):
        """التحقق من قفل الحساب"""
        if self.locked_until and self.locked_until > datetime.utcnow():
            return True
        return False

    def lock_account(self, minutes=30):
        """قفل الحساب لفترة محددة"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=minutes)
        self.failed_login_attempts = 0

    def unlock_account(self):
        """إلغاء قفل الحساب"""
        self.locked_until = None
        self.failed_login_attempts = 0

    def increment_failed_attempts(self):
        """زيادة عدد محاولات تسجيل الدخول الفاشلة"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:
            self.lock_account()

    def reset_failed_attempts(self):
        """إعادة تعيين محاولات تسجيل الدخول الفاشلة"""
        self.failed_login_attempts = 0

    def has_permission(self, action):
        """التحقق من الصلاحيات"""
        permissions = {
            'مدير': ['create', 'read', 'update', 'delete', 'admin'],
            'مشرف': ['create', 'read', 'update'],
            'مشاهد': ['read']
        }
        return action in permissions.get(self.role, [])

class Driver(db.Model):
    """نموذج السائقين"""
    __tablename__ = 'drivers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20), unique=True, nullable=False)
    national_id = db.Column(db.String(20), unique=True)
    license_number = db.Column(db.String(50))
    vehicle_type = db.Column(db.String(100))
    vehicle_plate = db.Column(db.String(20))
    
    # نوع الدفع والحالة
    payment_type = db.Column(db.String(20), nullable=False)  # شهري، أسبوعي
    payment_amount = db.Column(db.Float, nullable=False, default=0.0)
    payment_status = db.Column(db.String(20), default='ملتزم')  # ملتزم، متوسط، متأخر
    
    # الموقع الجغرافي
    current_latitude = db.Column(db.Float)
    current_longitude = db.Column(db.Float)
    current_address = db.Column(db.Text)
    
    # معلومات إضافية
    status = db.Column(db.String(20), default='نشط')  # نشط، غير نشط، معلق
    notes = db.Column(db.Text)
    rating = db.Column(db.Float, default=5.0)  # تقييم من 1-5
    
    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_payment_date = db.Column(db.DateTime)
    next_payment_due = db.Column(db.DateTime)
    
    # العلاقات
    payments = db.relationship('Payment', backref='driver', lazy=True, cascade='all, delete-orphan')
    documents = db.relationship('Document', backref='driver', lazy=True, cascade='all, delete-orphan')
    ai_analysis = db.relationship('AIAnalysis', backref='driver', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Driver {self.name}>'
    
    def calculate_total_paid(self):
        """حساب إجمالي المدفوعات"""
        return sum(payment.amount for payment in self.payments)
    
    def calculate_outstanding_amount(self):
        """حساب المبلغ المستحق"""
        # حساب عدد الفترات المستحقة
        if not self.next_payment_due:
            return 0
        
        today = datetime.now().date()
        due_date = self.next_payment_due.date()
        
        if due_date <= today:
            if self.payment_type == 'أسبوعي':
                weeks_overdue = (today - due_date).days // 7
                return (weeks_overdue + 1) * self.payment_amount
            elif self.payment_type == 'شهري':
                months_overdue = (today.year - due_date.year) * 12 + (today.month - due_date.month)
                return (months_overdue + 1) * self.payment_amount
        
        return 0
    
    def update_payment_status(self):
        """تحديث حالة الدفع تلقائياً"""
        outstanding = self.calculate_outstanding_amount()
        
        if outstanding == 0:
            self.payment_status = 'ملتزم'
        elif outstanding <= self.payment_amount:
            self.payment_status = 'متوسط'
        else:
            self.payment_status = 'متأخر'
        
        db.session.commit()
    
    def get_location_dict(self):
        """الحصول على الموقع كقاموس"""
        if self.current_latitude and self.current_longitude:
            return {
                'lat': self.current_latitude,
                'lng': self.current_longitude,
                'address': self.current_address or ''
            }
        return None

class Payment(db.Model):
    """نموذج المدفوعات"""
    __tablename__ = 'payments'

    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)  # تحويل، كاش، شيك
    payment_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    reference_number = db.Column(db.String(100))  # رقم المرجع للتحويل
    notes = db.Column(db.Text)
    status = db.Column(db.String(20), default='مكتمل')  # مكتمل، معلق، ملغي
    payment_type = db.Column(db.String(50))  # نوع الدفعة

    # معلومات إضافية
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Payment {self.amount} for Driver {self.driver_id}>'

class Document(db.Model):
    """نموذج المستندات"""
    __tablename__ = 'documents'
    
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'), nullable=False)
    document_type = db.Column(db.String(100), nullable=False)  # رخصة، هوية، إيصال دفع
    file_name = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    def __repr__(self):
        return f'<Document {self.document_type} for Driver {self.driver_id}>'

class AIAnalysis(db.Model):
    """نموذج تحليلات الذكاء الاصطناعي"""
    __tablename__ = 'ai_analysis'
    
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.id'), nullable=False)
    analysis_type = db.Column(db.String(100), nullable=False)  # payment_pattern, delay_prediction, performance
    analysis_data = db.Column(db.Text)  # JSON data
    prediction_score = db.Column(db.Float)
    recommendations = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_analysis_data(self):
        """الحصول على بيانات التحليل كقاموس"""
        if self.analysis_data:
            return json.loads(self.analysis_data)
        return {}
    
    def set_analysis_data(self, data):
        """تعيين بيانات التحليل"""
        self.analysis_data = json.dumps(data, ensure_ascii=False)
    
    def __repr__(self):
        return f'<AIAnalysis {self.analysis_type} for Driver {self.driver_id}>'

class Settings(db.Model):
    """نموذج الإعدادات"""
    __tablename__ = 'settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    category = db.Column(db.String(50), nullable=False)  # general, notifications, payments, users, backup
    description = db.Column(db.String(255))
    data_type = db.Column(db.String(20), default='string')  # string, integer, boolean, json
    is_public = db.Column(db.Boolean, default=False)  # هل يمكن للمستخدمين العاديين رؤيتها
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_value(self):
        """الحصول على القيمة بالنوع الصحيح"""
        if self.data_type == 'boolean':
            return self.value.lower() in ['true', '1', 'yes', 'on']
        elif self.data_type == 'integer':
            return int(self.value) if self.value else 0
        elif self.data_type == 'float':
            return float(self.value) if self.value else 0.0
        elif self.data_type == 'json':
            return json.loads(self.value) if self.value else {}
        return self.value or ''

    def set_value(self, value):
        """تعيين القيمة"""
        if self.data_type == 'json':
            self.value = json.dumps(value, ensure_ascii=False)
        else:
            self.value = str(value)

    @staticmethod
    def get_setting(key, default=None):
        """الحصول على إعداد معين"""
        setting = Settings.query.filter_by(key=key).first()
        return setting.get_value() if setting else default

    @staticmethod
    def set_setting(key, value, category='general', description='', data_type='string'):
        """تعيين إعداد"""
        setting = Settings.query.filter_by(key=key).first()
        if not setting:
            setting = Settings(key=key, category=category, description=description, data_type=data_type)
            db.session.add(setting)

        setting.set_value(value)
        setting.updated_at = datetime.utcnow()
        db.session.commit()
        return setting

    def __repr__(self):
        return f'<Settings {self.key}={self.value}>'

class Role(db.Model):
    """نموذج الأدوار والصلاحيات"""
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.String(255))
    permissions = db.Column(db.Text)  # JSON array of permissions
    is_system = db.Column(db.Boolean, default=False)  # الأدوار الأساسية للنظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_permissions(self):
        """الحصول على قائمة الصلاحيات"""
        if self.permissions:
            return json.loads(self.permissions)
        return []

    def set_permissions(self, permissions_list):
        """تعيين الصلاحيات"""
        self.permissions = json.dumps(permissions_list, ensure_ascii=False)

    def has_permission(self, permission):
        """التحقق من وجود صلاحية معينة"""
        return permission in self.get_permissions()

    def __repr__(self):
        return f'<Role {self.name}>'

class ActivityLog(db.Model):
    """نموذج سجل النشاطات"""
    __tablename__ = 'activity_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)  # create, update, delete, login, logout
    resource_type = db.Column(db.String(50))  # driver, payment, settings, user
    resource_id = db.Column(db.Integer)
    details = db.Column(db.Text)  # JSON data with additional details
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref=db.backref('activity_logs', lazy=True))

    def get_details(self):
        """الحصول على تفاصيل النشاط"""
        if self.details:
            return json.loads(self.details)
        return {}

    def set_details(self, details_dict):
        """تعيين تفاصيل النشاط"""
        self.details = json.dumps(details_dict, ensure_ascii=False)

    @staticmethod
    def log_activity(user_id, action, resource_type=None, resource_id=None, details=None, request_obj=None):
        """تسجيل نشاط جديد"""
        log = ActivityLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id
        )

        if details:
            log.set_details(details)

        if request_obj:
            log.ip_address = request_obj.remote_addr
            log.user_agent = request_obj.headers.get('User-Agent', '')

        db.session.add(log)
        db.session.commit()
        return log

    def __repr__(self):
        return f'<ActivityLog {self.action} by User {self.user_id}>'

class BackupLog(db.Model):
    """نموذج سجل النسخ الاحتياطية"""
    __tablename__ = 'backup_logs'

    id = db.Column(db.Integer, primary_key=True)
    backup_type = db.Column(db.String(50), nullable=False)  # full, incremental, settings
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    status = db.Column(db.String(20), default='completed')  # pending, completed, failed
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    error_message = db.Column(db.Text)

    # العلاقات
    user = db.relationship('User', backref=db.backref('backup_logs', lazy=True))

    def __repr__(self):
        return f'<BackupLog {self.backup_type} - {self.status}>'
