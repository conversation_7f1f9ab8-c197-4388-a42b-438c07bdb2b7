{% extends "base.html" %}

{% block title %}التقارير والإحصائيات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير والإحصائيات
                </h2>
                <div class="btn-group">
                    <button class="btn btn-success" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>
                        تصدير Excel
                    </button>
                    <button class="btn btn-danger" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>

            <!-- فلاتر التقارير -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">فلاتر التقارير</h5>
                </div>
                <div class="card-body">
                    <form id="reportFilters">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="start_date">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="end_date">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">نوع التقرير</label>
                                    <select class="form-select" name="report_type">
                                        <option value="all">جميع التقارير</option>
                                        <option value="drivers">تقرير السائقين</option>
                                        <option value="payments">تقرير المدفوعات</option>
                                        <option value="performance">تقرير الأداء</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary d-block" onclick="generateReport()">
                                        <i class="fas fa-search me-2"></i>
                                        إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary">0</h4>
                            <p class="mb-0">إجمالي السائقين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                            <h4 class="text-success">0 ر.س</h4>
                            <p class="mb-0">إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-receipt fa-2x text-info mb-2"></i>
                            <h4 class="text-info">0</h4>
                            <p class="mb-0">عدد المدفوعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning">0.0</h4>
                            <p class="mb-0">متوسط التقييم</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">الإيرادات الشهرية</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyRevenueChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">توزيع السائقين حسب التصنيف</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="driversClassificationChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقارير مفصلة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أفضل السائقين أداءً</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>التقييم</th>
                                            <th>المدفوعات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">لا توجد بيانات</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">السائقين المتأخرين في الدفع</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>أيام التأخير</th>
                                            <th>المبلغ المستحق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">لا توجد بيانات</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقرير تفصيلي -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">التقرير التفصيلي</h5>
                </div>
                <div class="card-body">
                    <div id="detailedReport" class="text-center text-muted py-5">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <h5>اختر الفلاتر وانقر على "إنشاء التقرير" لعرض البيانات</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إعداد الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني للإيرادات الشهرية
    const monthlyCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات (ر.س)',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });

    // رسم بياني لتوزيع السائقين
    const classificationCtx = document.getElementById('driversClassificationChart').getContext('2d');
    new Chart(classificationCtx, {
        type: 'doughnut',
        data: {
            labels: ['ملتزم', 'متوسط', 'متأخر'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
});

function generateReport() {
    // هنا سيتم إضافة كود إنشاء التقرير
    document.getElementById('detailedReport').innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            سيتم تطوير هذه الميزة قريباً لعرض التقارير التفصيلية
        </div>
    `;
}

function exportReport(format) {
    // هنا سيتم إضافة كود تصدير التقرير
    alert('سيتم تطوير ميزة التصدير إلى ' + format.toUpperCase() + ' قريباً');
}
</script>
{% endblock %}
