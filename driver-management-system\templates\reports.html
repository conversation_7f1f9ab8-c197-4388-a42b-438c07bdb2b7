{% extends "base.html" %}

{% block title %}التقارير والإحصائيات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير والإحصائيات
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-file-excel me-2"></i>
                        تصدير Excel
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('export_payments_excel') }}">
                            <i class="fas fa-money-bill-wave text-success me-2"></i>
                            تقرير المدفوعات
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('export_financial_excel') }}">
                            <i class="fas fa-chart-pie text-primary me-2"></i>
                            التقرير المالي الشامل
                        </a></li>
                    </ul>
                    <a href="{{ url_for('export_center') }}" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>
                        مركز التصدير المتقدم
                    </a>
                </div>
            </div>

            <!-- فلاتر التقارير -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">فلاتر التقارير</h5>
                </div>
                <div class="card-body">
                    <form id="reportFilters">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="start_date">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="end_date">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">نوع التقرير</label>
                                    <select class="form-select" name="report_type">
                                        <option value="all">جميع التقارير</option>
                                        <option value="drivers">تقرير السائقين</option>
                                        <option value="payments">تقرير المدفوعات</option>
                                        <option value="performance">تقرير الأداء</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary d-block" onclick="generateReport()">
                                        <i class="fas fa-search me-2"></i>
                                        إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary">{{ total_drivers or 0 }}</h4>
                            <p class="mb-0">إجمالي السائقين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                            <h4 class="text-success">{{ "%.0f"|format(total_amount or 0) }} ريال</h4>
                            <p class="mb-0">إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-receipt fa-2x text-info mb-2"></i>
                            <h4 class="text-info">{{ total_payments or 0 }}</h4>
                            <p class="mb-0">عدد المدفوعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning">{{ active_drivers or 0 }}</h4>
                            <p class="mb-0">السائقين النشطين</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">الإيرادات الشهرية</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyRevenueChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">توزيع السائقين حسب التصنيف</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="driversClassificationChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقارير مفصلة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أفضل السائقين أداءً</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>عدد المدفوعات</th>
                                            <th>إجمالي المبلغ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if top_drivers %}
                                            {% for driver in top_drivers %}
                                            <tr>
                                                <td>{{ driver.name }}</td>
                                                <td><span class="badge bg-primary">{{ driver.payment_count }}</span></td>
                                                <td><strong class="text-success">{{ "%.0f"|format(driver.total_amount or 0) }} ريال</strong></td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">لا توجد بيانات</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">السائقين المتأخرين في الدفع</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>أيام التأخير</th>
                                            <th>المبلغ المستحق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if overdue_drivers %}
                                            {% for driver in overdue_drivers %}
                                            <tr>
                                                <td>{{ driver.name }}</td>
                                                <td>
                                                    {% set days_overdue = (moment().date() - driver.next_payment_due.date()).days if driver.next_payment_due else 0 %}
                                                    <span class="badge bg-danger">{{ days_overdue }} يوم</span>
                                                </td>
                                                <td><strong class="text-danger">{{ "%.0f"|format(driver.payment_amount or 0) }} ريال</strong></td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">لا توجد مدفوعات متأخرة</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقرير تفصيلي -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">التقرير التفصيلي</h5>
                </div>
                <div class="card-body">
                    <div id="detailedReport" class="text-center text-muted py-5">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <h5>اختر الفلاتر وانقر على "إنشاء التقرير" لعرض البيانات</h5>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم رفع ملفات CSV -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>رفع ملف CSV
                    </h5>
                </div>
                <div class="card-body">
                    <form id="csvUploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="csvFile" class="form-label">اختر ملف CSV</label>
                            <input type="file" class="form-control" id="csvFile" name="csvFile" accept=".csv" required>
                            <div class="form-text">يجب أن يكون الملف بصيغة CSV مع العناوين المناسبة</div>
                        </div>

                        <div class="mb-3">
                            <label for="uploadType" class="form-label">نوع البيانات</label>
                            <select class="form-select" id="uploadType" name="uploadType" required>
                                <option value="">اختر نوع البيانات</option>
                                <option value="drivers">بيانات السائقين</option>
                                <option value="payments">بيانات المدفوعات</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="updateExisting" name="updateExisting">
                                <label class="form-check-label" for="updateExisting">
                                    تحديث البيانات الموجودة
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-upload me-2"></i>رفع الملف
                        </button>

                        <button type="button" class="btn btn-info ms-2" onclick="downloadTemplate()">
                            <i class="fas fa-download me-2"></i>تحميل نموذج
                        </button>
                    </form>

                    <div id="uploadProgress" class="mt-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">جاري رفع الملف...</small>
                    </div>

                    <div id="uploadResult" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إعداد الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني للإيرادات الشهرية
    const monthlyCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات (ر.س)',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });

    // رسم بياني لتوزيع السائقين
    const classificationCtx = document.getElementById('driversClassificationChart').getContext('2d');
    new Chart(classificationCtx, {
        type: 'doughnut',
        data: {
            labels: ['ملتزم', 'متوسط', 'متأخر'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
});

function generateReport() {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const statusFilter = document.getElementById('statusFilter').value;

    if (!reportType) {
        showAlert('يرجى اختيار نوع التقرير', 'warning');
        return;
    }

    // إظهار مؤشر التحميل
    const reportContainer = document.getElementById('detailedReport');
    reportContainer.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري إنشاء التقرير...</p>
        </div>
    `;

    // إرسال طلب AJAX لإنشاء التقرير
    const params = new URLSearchParams({
        report_type: reportType,
        start_date: startDate || '',
        end_date: endDate || '',
        status: statusFilter || ''
    });

    fetch(`/api/generate-report?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayReportData(data.data, reportType);
            } else {
                showAlert('حدث خطأ في إنشاء التقرير: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في الاتصال بالخادم', 'error');
        });
}

function displayReportData(data, reportType) {
    const reportContainer = document.getElementById('detailedReport');
    let html = '';

    switch(reportType) {
        case 'drivers':
            html = generateDriversReport(data);
            break;
        case 'payments':
            html = generatePaymentsReport(data);
            break;
        case 'financial':
            html = generateFinancialReport(data);
            break;
        case 'overdue':
            html = generateOverdueReport(data);
            break;
        default:
            html = '<div class="alert alert-warning">نوع تقرير غير مدعوم</div>';
    }

    reportContainer.innerHTML = html;

    // إضافة أزرار التصدير
    const exportButtons = `
        <div class="mt-4 text-center">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success" onclick="exportReport('excel')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
                <button type="button" class="btn btn-danger" onclick="exportReport('pdf')">
                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                </button>
                <button type="button" class="btn btn-info" onclick="exportReport('csv')">
                    <i class="fas fa-file-csv me-2"></i>تصدير CSV
                </button>
            </div>
        </div>
    `;

    reportContainer.innerHTML += exportButtons;
}

function generateDriversReport(data) {
    let html = `
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>تقرير السائقين</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-primary">${data.total_drivers}</h3>
                            <p class="text-muted">إجمالي السائقين</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-success">${data.active_drivers}</h3>
                            <p class="text-muted">السائقين النشطين</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-warning">${data.inactive_drivers}</h3>
                            <p class="text-muted">السائقين غير النشطين</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-danger">${data.suspended_drivers}</h3>
                            <p class="text-muted">السائقين المعلقين</p>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>نوع المركبة</th>
                                <th>الحالة</th>
                                <th>نوع الدفع</th>
                                <th>المبلغ</th>
                                <th>آخر دفعة</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    data.drivers.forEach(driver => {
        const statusClass = driver.status === 'نشط' ? 'success' : driver.status === 'معلق' ? 'danger' : 'warning';
        html += `
            <tr>
                <td>${driver.name}</td>
                <td>${driver.phone}</td>
                <td>${driver.vehicle_type || 'غير محدد'}</td>
                <td><span class="badge bg-${statusClass}">${driver.status}</span></td>
                <td>${driver.payment_type}</td>
                <td>${driver.payment_amount} ريال</td>
                <td>${driver.last_payment_date || 'لا توجد'}</td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    return html;
}

function generatePaymentsReport(data) {
    let html = `
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>تقرير المدفوعات</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="stat-card text-center">
                            <h3 class="text-success">${data.total_amount.toFixed(2)}</h3>
                            <p class="text-muted">إجمالي المبلغ (ريال)</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card text-center">
                            <h3 class="text-primary">${data.total_payments}</h3>
                            <p class="text-muted">عدد المدفوعات</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card text-center">
                            <h3 class="text-info">${data.avg_payment.toFixed(2)}</h3>
                            <p class="text-muted">متوسط الدفعة (ريال)</p>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>السائق</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    data.payments.forEach(payment => {
        const statusClass = payment.status === 'مكتمل' ? 'success' : payment.status === 'معلق' ? 'warning' : 'danger';
        html += `
            <tr>
                <td>${payment.driver_name}</td>
                <td>${payment.amount} ريال</td>
                <td>${payment.payment_method}</td>
                <td>${payment.payment_date}</td>
                <td><span class="badge bg-${statusClass}">${payment.status}</span></td>
                <td>${payment.notes || '-'}</td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    return html;
}

function generateFinancialReport(data) {
    return `
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>التقرير المالي</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الإيرادات الشهرية</h6>
                        <canvas id="monthlyRevenueChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-md-6">
                        <h6>توزيع طرق الدفع</h6>
                        <canvas id="paymentMethodsChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-success">${data.total_revenue.toFixed(2)}</h3>
                            <p class="text-muted">إجمالي الإيرادات</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-warning">${data.pending_revenue.toFixed(2)}</h3>
                            <p class="text-muted">الإيرادات المعلقة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-info">${data.this_month_revenue.toFixed(2)}</h3>
                            <p class="text-muted">إيرادات هذا الشهر</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card text-center">
                            <h3 class="text-primary">${data.growth_rate.toFixed(1)}%</h3>
                            <p class="text-muted">معدل النمو</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateOverdueReport(data) {
    let html = `
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تقرير المتأخرين</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    يوجد ${data.overdue_drivers.length} سائق متأخر في المدفوعات
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>السائق</th>
                                <th>الهاتف</th>
                                <th>المبلغ المستحق</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>أيام التأخير</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    data.overdue_drivers.forEach(driver => {
        html += `
            <tr>
                <td>${driver.name}</td>
                <td>${driver.phone}</td>
                <td class="text-danger fw-bold">${driver.outstanding_amount} ريال</td>
                <td>${driver.next_payment_due}</td>
                <td class="text-danger">${driver.days_overdue} يوم</td>
                <td>
                    <button class="btn btn-sm btn-warning" onclick="sendReminder(${driver.id})">
                        <i class="fas fa-bell"></i> تذكير
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    return html;
}

function exportReport(format) {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const statusFilter = document.getElementById('statusFilter').value;

    if (!reportType) {
        showAlert('يرجى إنشاء التقرير أولاً', 'warning');
        return;
    }

    // إنشاء رابط التصدير
    const params = new URLSearchParams({
        report_type: reportType,
        format: format,
        start_date: startDate || '',
        end_date: endDate || '',
        status: statusFilter || ''
    });

    // فتح رابط التصدير في نافذة جديدة
    window.open(`/api/export-report?${params}`, '_blank');
}

function showAlert(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' : type === 'warning' ? 'alert-warning' : 'alert-info';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const alertContainer = document.getElementById('alertContainer') || document.body;
    alertContainer.insertAdjacentHTML('afterbegin', alertHtml);
}

function sendReminder(driverId) {
    fetch(`/api/send-reminder/${driverId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إرسال التذكير بنجاح', 'success');
        } else {
            showAlert('حدث خطأ في إرسال التذكير', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال بالخادم', 'error');
    });
}

// وظائف رفع ملفات CSV
document.getElementById('csvUploadForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData();
    const fileInput = document.getElementById('csvFile');
    const uploadType = document.getElementById('uploadType').value;
    const updateExisting = document.getElementById('updateExisting').checked;

    if (!fileInput.files[0]) {
        showAlert('يرجى اختيار ملف CSV', 'warning');
        return;
    }

    if (!uploadType) {
        showAlert('يرجى اختيار نوع البيانات', 'warning');
        return;
    }

    formData.append('csvFile', fileInput.files[0]);
    formData.append('uploadType', uploadType);
    formData.append('updateExisting', updateExisting);

    // إظهار شريط التقدم
    document.getElementById('uploadProgress').style.display = 'block';
    document.getElementById('uploadResult').innerHTML = '';

    fetch('/api/upload-csv', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('uploadProgress').style.display = 'none';

        if (data.success) {
            showUploadResult(data, 'success');
            // إعادة تعيين النموذج
            document.getElementById('csvUploadForm').reset();
        } else {
            showUploadResult(data, 'error');
        }
    })
    .catch(error => {
        document.getElementById('uploadProgress').style.display = 'none';
        console.error('Error:', error);
        showAlert('حدث خطأ في رفع الملف', 'error');
    });
});

function showUploadResult(data, type) {
    const resultDiv = document.getElementById('uploadResult');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

    let html = `
        <div class="alert ${alertClass}">
            <i class="fas fa-${icon} me-2"></i>
            <strong>${data.message}</strong>
    `;

    if (data.details) {
        html += `
            <hr>
            <ul class="mb-0">
        `;

        if (data.details.processed) {
            html += `<li>تم معالجة: ${data.details.processed} سجل</li>`;
        }
        if (data.details.added) {
            html += `<li>تم إضافة: ${data.details.added} سجل جديد</li>`;
        }
        if (data.details.updated) {
            html += `<li>تم تحديث: ${data.details.updated} سجل</li>`;
        }
        if (data.details.errors && data.details.errors.length > 0) {
            html += `<li>أخطاء: ${data.details.errors.length} سجل</li>`;
            html += `<li><small>أول خطأ: ${data.details.errors[0]}</small></li>`;
        }

        html += `</ul>`;
    }

    html += `</div>`;
    resultDiv.innerHTML = html;
}

function downloadTemplate() {
    const uploadType = document.getElementById('uploadType').value;

    if (!uploadType) {
        showAlert('يرجى اختيار نوع البيانات أولاً', 'warning');
        return;
    }

    // فتح رابط تحميل النموذج
    window.open(`/api/download-template/${uploadType}`, '_blank');
}
</script>
{% endblock %}
