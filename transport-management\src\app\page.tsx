'use client'

import { useState, useEffect } from 'react'
import {
  Users,
  Car,
  TrendingUp,
  Clock,
  DollarSign,
  MapPin,
  Phone,
  Calendar
} from 'lucide-react'

// مكونات الإحصائيات
function StatCard({ title, value, icon: Icon, color = "blue" }: {
  title: string
  value: string | number
  icon: any
  color?: string
}) {
  const colorClasses = {
    blue: "bg-blue-500",
    green: "bg-green-500",
    yellow: "bg-yellow-500",
    purple: "bg-purple-500"
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color as keyof typeof colorClasses]} text-white`}>
          <Icon size={24} />
        </div>
      </div>
    </div>
  )
}

// مكون الرحلات الأخيرة
function RecentTrips() {
  const mockTrips = [
    {
      id: 1,
      driver: "أحمد محمد",
      from: "الرياض",
      to: "الدمام",
      fare: 250,
      status: "مكتملة",
      time: "منذ ساعة"
    },
    {
      id: 2,
      driver: "محمد علي",
      from: "جدة",
      to: "مكة",
      fare: 80,
      status: "جارية",
      time: "منذ 30 دقيقة"
    },
    {
      id: 3,
      driver: "سعد أحمد",
      from: "الرياض",
      to: "القصيم",
      fare: 180,
      status: "مكتملة",
      time: "منذ ساعتين"
    }
  ]

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">الرحلات الأخيرة</h3>
      <div className="space-y-4">
        {mockTrips.map((trip) => (
          <div key={trip.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="p-2 bg-blue-100 rounded-full">
                <Car size={16} className="text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{trip.driver}</p>
                <p className="text-sm text-gray-600 flex items-center">
                  <MapPin size={12} className="ml-1" />
                  {trip.from} ← {trip.to}
                </p>
              </div>
            </div>
            <div className="text-left">
              <p className="font-semibold text-green-600">{trip.fare} ريال</p>
              <p className="text-xs text-gray-500">{trip.time}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default function Dashboard() {
  const [stats, setStats] = useState({
    driversCount: 500,
    tripsCount: 1250,
    totalEarnings: 125000,
    activeTrips: 25
  })

  return (
    <div className="min-h-screen bg-gray-100" dir="rtl">
      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="إجمالي السائقين"
            value={stats.driversCount}
            icon={Users}
            color="blue"
          />
          <StatCard
            title="إجمالي الرحلات"
            value={stats.tripsCount}
            icon={Car}
            color="green"
          />
          <StatCard
            title="إجمالي الأرباح"
            value={`${stats.totalEarnings.toLocaleString()} ريال`}
            icon={DollarSign}
            color="yellow"
          />
          <StatCard
            title="الرحلات النشطة"
            value={stats.activeTrips}
            icon={Clock}
            color="purple"
          />
        </div>

        {/* الشبكة الرئيسية */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* الرحلات الأخيرة */}
          <div className="lg:col-span-2">
            <RecentTrips />
          </div>

          {/* الإجراءات السريعة */}
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الإجراءات السريعة</h3>
            <div className="space-y-3">
              <button className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Users className="ml-2" size={20} />
                إضافة سائق جديد
              </button>
              <button className="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Car className="ml-2" size={20} />
                إضافة رحلة جديدة
              </button>
              <button className="w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <TrendingUp className="ml-2" size={20} />
                عرض التقارير
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
