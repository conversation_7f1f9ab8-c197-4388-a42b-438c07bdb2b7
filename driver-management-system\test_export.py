#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف التصدير
"""

import requests
import os
import sys

def test_export_functions():
    """اختبار وظائف التصدير"""
    base_url = "http://localhost:5000"
    
    print("🔍 اختبار وظائف التصدير...")
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        # تسجيل الدخول أولاً
        print("1. تسجيل الدخول...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار مركز التصدير
        print("2. اختبار مركز التصدير...")
        export_center = session.get(f"{base_url}/export")
        
        if export_center.status_code == 200:
            print("✅ مركز التصدير يعمل")
        else:
            print(f"❌ مركز التصدير: خطأ {export_center.status_code}")
            
        # اختبار تصدير السائقين
        print("3. اختبار تصدير السائقين...")
        
        export_drivers = session.get(f"{base_url}/export/drivers/excel")
        
        if export_drivers.status_code == 200:
            content_type = export_drivers.headers.get('content-type', '')
            content_length = len(export_drivers.content)
            
            if content_length > 0:
                print(f"✅ تصدير السائقين: نجح (حجم الملف: {content_length} بايت)")
                
                # حفظ الملف للفحص
                with open('test_drivers_export.xlsx', 'wb') as f:
                    f.write(export_drivers.content)
                print("✅ تم حفظ ملف التصدير: test_drivers_export.xlsx")
                
            else:
                print("⚠️ تصدير السائقين: الملف فارغ")
        else:
            print(f"❌ تصدير السائقين: خطأ {export_drivers.status_code}")
            print(f"Response: {export_drivers.text[:300]}...")
            
        # اختبار تصدير المدفوعات
        print("4. اختبار تصدير المدفوعات...")
        
        export_payments = session.get(f"{base_url}/export/payments/excel")
        
        if export_payments.status_code == 200:
            content_length = len(export_payments.content)
            
            if content_length > 0:
                print(f"✅ تصدير المدفوعات: نجح (حجم الملف: {content_length} بايت)")
                
                # حفظ الملف للفحص
                with open('test_payments_export.xlsx', 'wb') as f:
                    f.write(export_payments.content)
                print("✅ تم حفظ ملف التصدير: test_payments_export.xlsx")
                
            else:
                print("⚠️ تصدير المدفوعات: الملف فارغ")
        else:
            print(f"❌ تصدير المدفوعات: خطأ {export_payments.status_code}")
            print(f"Response: {export_payments.text[:300]}...")
            
        # اختبار التقرير المالي
        print("5. اختبار التقرير المالي...")
        
        export_financial = session.get(f"{base_url}/export/financial/excel")
        
        if export_financial.status_code == 200:
            content_length = len(export_financial.content)
            
            if content_length > 0:
                print(f"✅ التقرير المالي: نجح (حجم الملف: {content_length} بايت)")
                
                # حفظ الملف للفحص
                with open('test_financial_export.xlsx', 'wb') as f:
                    f.write(export_financial.content)
                print("✅ تم حفظ ملف التصدير: test_financial_export.xlsx")
                
            else:
                print("⚠️ التقرير المالي: الملف فارغ")
        else:
            print(f"❌ التقرير المالي: خطأ {export_financial.status_code}")
            print(f"Response: {export_financial.text[:300]}...")
            
        # اختبار تصدير مع فلاتر
        print("6. اختبار التصدير مع فلاتر...")
        
        filtered_export = session.get(f"{base_url}/export/drivers/excel?status=نشط&payment_type=شهري")
        
        if filtered_export.status_code == 200:
            content_length = len(filtered_export.content)
            print(f"✅ التصدير مع فلاتر: نجح (حجم الملف: {content_length} بايت)")
        else:
            print(f"❌ التصدير مع فلاتر: خطأ {filtered_export.status_code}")
            
        print("\n✅ انتهى اختبار وظائف التصدير")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    test_export_functions()
