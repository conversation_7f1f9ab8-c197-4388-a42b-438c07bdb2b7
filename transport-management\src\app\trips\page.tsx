'use client'

import { useState, useEffect } from 'react'
import { 
  Car, 
  Plus, 
  Search, 
  MapPin, 
  Clock, 
  DollarSign,
  User,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play
} from 'lucide-react'

// نوع بيانات الرحلة
interface Trip {
  id: string
  driver_name: string
  customer_name?: string
  pickup_location: string
  destination: string
  distance?: number
  fare: number
  commission: number
  driver_earnings: number
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  trip_date: string
  completed_at?: string
  notes?: string
}

// مكون بطاقة الرحلة
function TripCard({ trip }: { trip: Trip }) {
  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    in_progress: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  }

  const statusIcons = {
    pending: Clock,
    in_progress: Play,
    completed: CheckCircle,
    cancelled: XCircle
  }

  const statusText = {
    pending: 'في الانتظار',
    in_progress: 'جارية',
    completed: 'مكتملة',
    cancelled: 'ملغية'
  }

  const StatusIcon = statusIcons[trip.status]

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-full ml-3">
            <Car size={20} className="text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">رحلة #{trip.id}</h3>
            <p className="text-sm text-gray-600 flex items-center">
              <User size={12} className="ml-1" />
              {trip.driver_name}
            </p>
          </div>
        </div>
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[trip.status]}`}>
          <StatusIcon size={12} className="ml-1" />
          {statusText[trip.status]}
        </span>
      </div>

      <div className="space-y-3">
        <div className="flex items-start">
          <MapPin size={16} className="text-green-600 mt-1 ml-2 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-gray-900">من: {trip.pickup_location}</p>
            <p className="text-sm font-medium text-gray-900">إلى: {trip.destination}</p>
          </div>
        </div>

        {trip.customer_name && (
          <div className="flex items-center">
            <User size={16} className="text-gray-400 ml-2" />
            <span className="text-sm text-gray-600">العميل: {trip.customer_name}</span>
          </div>
        )}

        <div className="flex items-center">
          <Calendar size={16} className="text-gray-400 ml-2" />
          <span className="text-sm text-gray-600">
            {new Date(trip.trip_date).toLocaleDateString('ar-SA')}
          </span>
        </div>

        {trip.distance && (
          <div className="flex items-center">
            <MapPin size={16} className="text-gray-400 ml-2" />
            <span className="text-sm text-gray-600">المسافة: {trip.distance} كم</span>
          </div>
        )}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-xs text-gray-500">إجمالي الأجرة</p>
            <p className="text-sm font-semibold text-gray-900">{trip.fare} ريال</p>
          </div>
          <div>
            <p className="text-xs text-gray-500">العمولة</p>
            <p className="text-sm font-semibold text-red-600">{trip.commission} ريال</p>
          </div>
          <div>
            <p className="text-xs text-gray-500">ربح السائق</p>
            <p className="text-sm font-semibold text-green-600">{trip.driver_earnings} ريال</p>
          </div>
        </div>
      </div>

      {trip.notes && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 mb-1">ملاحظات:</p>
          <p className="text-sm text-gray-700">{trip.notes}</p>
        </div>
      )}
    </div>
  )
}

export default function TripsPage() {
  const [trips, setTrips] = useState<Trip[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)

  // بيانات وهمية للاختبار
  useEffect(() => {
    const mockTrips: Trip[] = [
      {
        id: '001',
        driver_name: 'أحمد محمد السعيد',
        customer_name: 'سارة أحمد',
        pickup_location: 'الرياض - حي النخيل',
        destination: 'الدمام - الكورنيش',
        distance: 395,
        fare: 250,
        commission: 25,
        driver_earnings: 225,
        status: 'completed',
        trip_date: '2024-01-15T10:30:00Z',
        completed_at: '2024-01-15T15:45:00Z',
        notes: 'رحلة سلسة بدون مشاكل'
      },
      {
        id: '002',
        driver_name: 'محمد علي أحمد',
        customer_name: 'خالد محمد',
        pickup_location: 'جدة - المطار',
        destination: 'مكة المكرمة - الحرم',
        distance: 85,
        fare: 80,
        commission: 8,
        driver_earnings: 72,
        status: 'in_progress',
        trip_date: '2024-01-16T08:00:00Z'
      },
      {
        id: '003',
        driver_name: 'سعد عبدالله محمد',
        pickup_location: 'الرياض - المركز التجاري',
        destination: 'القصيم - بريدة',
        fare: 180,
        commission: 18,
        driver_earnings: 162,
        status: 'pending',
        trip_date: '2024-01-16T14:00:00Z',
        notes: 'في انتظار تأكيد العميل'
      },
      {
        id: '004',
        driver_name: 'عبدالرحمن سالم',
        customer_name: 'فاطمة علي',
        pickup_location: 'الدمام - الجامعة',
        destination: 'الخبر - المجمع التجاري',
        distance: 25,
        fare: 45,
        commission: 4.5,
        driver_earnings: 40.5,
        status: 'cancelled',
        trip_date: '2024-01-15T16:00:00Z',
        notes: 'ألغيت بطلب من العميل'
      }
    ]
    
    setTimeout(() => {
      setTrips(mockTrips)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredTrips = trips.filter(trip => {
    const matchesSearch = 
      trip.driver_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.pickup_location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.destination.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trip.id.includes(searchTerm)
    
    const matchesStatus = statusFilter === 'all' || trip.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const handleAddNew = () => {
    // TODO: فتح نموذج إضافة رحلة جديدة
    console.log('إضافة رحلة جديدة')
  }

  const statusOptions = [
    { value: 'all', label: 'جميع الرحلات' },
    { value: 'pending', label: 'في الانتظار' },
    { value: 'in_progress', label: 'جارية' },
    { value: 'completed', label: 'مكتملة' },
    { value: 'cancelled', label: 'ملغية' }
  ]

  return (
    <div className="min-h-screen bg-gray-100" dir="rtl">
      {/* شريط الإجراءات */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Car className="h-6 w-6 text-blue-600 ml-2" />
              <h2 className="text-lg font-semibold text-gray-900">إدارة الرحلات</h2>
            </div>
            <button
              onClick={handleAddNew}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={20} className="ml-2" />
              إضافة رحلة جديدة
            </button>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* شريط البحث والفلترة */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="البحث في الرحلات (السائق، المكان، رقم الرحلة...)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* قائمة الرحلات */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 border border-gray-200 animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            <div className="mb-4 text-sm text-gray-600">
              عرض {filteredTrips.length} من أصل {trips.length} رحلة
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTrips.map((trip) => (
                <TripCard key={trip.id} trip={trip} />
              ))}
            </div>
            {filteredTrips.length === 0 && (
              <div className="text-center py-12">
                <Car className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">لم يتم العثور على رحلات تطابق البحث</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
