{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/%D9%85%D9%84%D9%81%20%D8%B4%D9%87%D9%8A%D8%AF%20%D9%85%D9%88%D8%B3%D9%89%20%D8%B3%D9%84%D8%B7%D8%A7%D9%86/%D9%85%D9%88%D9%82%D8%B9%20%D8%AC%D8%AF%D9%8A%D8%AF/transport-management/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  Users,\n  Car,\n  TrendingUp,\n  Clock,\n  DollarSign,\n  MapPin,\n  Phone,\n  Calendar\n} from 'lucide-react'\n\n// مكونات الإحصائيات\nfunction StatCard({ title, value, icon: Icon, color = \"blue\" }: {\n  title: string\n  value: string | number\n  icon: any\n  color?: string\n}) {\n  const colorClasses = {\n    blue: \"bg-blue-500\",\n    green: \"bg-green-500\",\n    yellow: \"bg-yellow-500\",\n    purple: \"bg-purple-500\"\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n        </div>\n        <div className={`p-3 rounded-full ${colorClasses[color as keyof typeof colorClasses]} text-white`}>\n          <Icon size={24} />\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون الرحلات الأخيرة\nfunction RecentTrips() {\n  const mockTrips = [\n    {\n      id: 1,\n      driver: \"أحمد محمد\",\n      from: \"الرياض\",\n      to: \"الدمام\",\n      fare: 250,\n      status: \"مكتملة\",\n      time: \"منذ ساعة\"\n    },\n    {\n      id: 2,\n      driver: \"محمد علي\",\n      from: \"جدة\",\n      to: \"مكة\",\n      fare: 80,\n      status: \"جارية\",\n      time: \"منذ 30 دقيقة\"\n    },\n    {\n      id: 3,\n      driver: \"سعد أحمد\",\n      from: \"الرياض\",\n      to: \"القصيم\",\n      fare: 180,\n      status: \"مكتملة\",\n      time: \"منذ ساعتين\"\n    }\n  ]\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الرحلات الأخيرة</h3>\n      <div className=\"space-y-4\">\n        {mockTrips.map((trip) => (\n          <div key={trip.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <div className=\"p-2 bg-blue-100 rounded-full\">\n                <Car size={16} className=\"text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"font-medium text-gray-900\">{trip.driver}</p>\n                <p className=\"text-sm text-gray-600 flex items-center\">\n                  <MapPin size={12} className=\"ml-1\" />\n                  {trip.from} ← {trip.to}\n                </p>\n              </div>\n            </div>\n            <div className=\"text-left\">\n              <p className=\"font-semibold text-green-600\">{trip.fare} ريال</p>\n              <p className=\"text-xs text-gray-500\">{trip.time}</p>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport default function Dashboard() {\n  const [stats, setStats] = useState({\n    driversCount: 500,\n    tripsCount: 1250,\n    totalEarnings: 125000,\n    activeTrips: 25\n  })\n\n  return (\n    <div className=\"min-h-screen bg-gray-100\" dir=\"rtl\">\n      {/* المحتوى الرئيسي */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* الإحصائيات */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <StatCard\n            title=\"إجمالي السائقين\"\n            value={stats.driversCount}\n            icon={Users}\n            color=\"blue\"\n          />\n          <StatCard\n            title=\"إجمالي الرحلات\"\n            value={stats.tripsCount}\n            icon={Car}\n            color=\"green\"\n          />\n          <StatCard\n            title=\"إجمالي الأرباح\"\n            value={`${stats.totalEarnings.toLocaleString()} ريال`}\n            icon={DollarSign}\n            color=\"yellow\"\n          />\n          <StatCard\n            title=\"الرحلات النشطة\"\n            value={stats.activeTrips}\n            icon={Clock}\n            color=\"purple\"\n          />\n        </div>\n\n        {/* الشبكة الرئيسية */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* الرحلات الأخيرة */}\n          <div className=\"lg:col-span-2\">\n            <RecentTrips />\n          </div>\n\n          {/* الإجراءات السريعة */}\n          <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الإجراءات السريعة</h3>\n            <div className=\"space-y-3\">\n              <button className=\"w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                <Users className=\"ml-2\" size={20} />\n                إضافة سائق جديد\n              </button>\n              <button className=\"w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                <Car className=\"ml-2\" size={20} />\n                إضافة رحلة جديدة\n              </button>\n              <button className=\"w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n                <TrendingUp className=\"ml-2\" size={20} />\n                عرض التقارير\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;AAFA;;;AAcA,oBAAoB;AACpB,SAAS,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,QAAQ,MAAM,EAK3D;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;8BAEnD,6LAAC;oBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAmC,CAAC,WAAW,CAAC;8BAC/F,cAAA,6LAAC;wBAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKtB;KA1BS;AA4BT,uBAAuB;AACvB,SAAS;IACP,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wBAAkB,WAAU;;0CAC3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAE3B,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6B,KAAK,MAAM;;;;;;0DACrD,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDAAO,MAAM;wDAAI,WAAU;;;;;;oDAC3B,KAAK,IAAI;oDAAC;oDAAI,KAAK,EAAE;;;;;;;;;;;;;;;;;;;0CAI5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAgC,KAAK,IAAI;4CAAC;;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAyB,KAAK,IAAI;;;;;;;;;;;;;uBAfzC,KAAK,EAAE;;;;;;;;;;;;;;;;AAsB3B;MA1DS;AA4DM,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,cAAc;QACd,YAAY;QACZ,eAAe;QACf,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;QAA2B,KAAI;kBAE5C,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,OAAM;4BACN,OAAO,MAAM,YAAY;4BACzB,MAAM;4BACN,OAAM;;;;;;sCAER,6LAAC;4BACC,OAAM;4BACN,OAAO,MAAM,UAAU;4BACvB,MAAM;4BACN,OAAM;;;;;;sCAER,6LAAC;4BACC,OAAM;4BACN,OAAO,GAAG,MAAM,aAAa,CAAC,cAAc,GAAG,KAAK,CAAC;4BACrD,MAAM;4BACN,OAAM;;;;;;sCAER,6LAAC;4BACC,OAAM;4BACN,OAAO,MAAM,WAAW;4BACxB,MAAM;4BACN,OAAM;;;;;;;;;;;;8BAKV,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;;;;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAM,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;sDAGtC,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAI,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;sDAGpC,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAW,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;GArEwB;MAAA", "debugId": null}}]}