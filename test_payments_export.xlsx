<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-car me-2"></i>
                نظام إدارة السائقين
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/drivers">
                            <i class="fas fa-users me-1"></i>
                            السائقين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/payments">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            المدفوعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/map">
                            <i class="fas fa-map-marked-alt me-1"></i>
                            الخريطة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/notifications">
                            <i class="fas fa-bell me-1"></i>
                            الإشعارات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/export">
                            <i class="fas fa-download"></i>
                            مركز التصدير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            المدير العام
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="fas fa-user-cog me-1"></i>
                                الملف الشخصي
                            </a></li>
                            
                            <li><a class="dropdown-item" href="/settings">
                                <i class="fas fa-cogs me-1"></i>
                                لوحة الإدارة
                            </a></li>
                            
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                    
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    
        
            <div class="container mt-3">
                
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        حدث خطأ أثناء التصدير: خطأ في تصدير تقرير المدفوعات: [Errno 2] No such file or directory: &#39;exports\\payments_report_20250705_085630.xlsx&#39;
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                
            </div>
        
    

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير والإحصائيات
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-file-excel me-2"></i>
                        تصدير Excel
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/export/payments/excel">
                            <i class="fas fa-money-bill-wave text-success me-2"></i>
                            تقرير المدفوعات
                        </a></li>
                        <li><a class="dropdown-item" href="/export/financial/excel">
                            <i class="fas fa-chart-pie text-primary me-2"></i>
                            التقرير المالي الشامل
                        </a></li>
                    </ul>
                    <a href="/export" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>
                        مركز التصدير المتقدم
                    </a>
                </div>
            </div>

            <!-- فلاتر التقارير -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">فلاتر التقارير</h5>
                </div>
                <div class="card-body">
                    <form id="reportFilters">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="start_date">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="end_date">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">نوع التقرير</label>
                                    <select class="form-select" name="report_type">
                                        <option value="all">جميع التقارير</option>
                                        <option value="drivers">تقرير السائقين</option>
                                        <option value="payments">تقرير المدفوعات</option>
                                        <option value="performance">تقرير الأداء</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary d-block" onclick="generateReport()">
                                        <i class="fas fa-search me-2"></i>
                                        إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary">1</h4>
                            <p class="mb-0">إجمالي السائقين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                            <h4 class="text-success">0 ريال</h4>
                            <p class="mb-0">إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-receipt fa-2x text-info mb-2"></i>
                            <h4 class="text-info">0</h4>
                            <p class="mb-0">عدد المدفوعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning">1</h4>
                            <p class="mb-0">السائقين النشطين</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">الإيرادات الشهرية</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyRevenueChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">توزيع السائقين حسب التصنيف</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="driversClassificationChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقارير مفصلة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أفضل السائقين أداءً</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>عدد المدفوعات</th>
                                            <th>إجمالي المبلغ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">لا توجد بيانات</td>
                                            </tr>
                                        
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">السائقين المتأخرين في الدفع</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>أيام التأخير</th>
                                            <th>المبلغ المستحق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">لا توجد مدفوعات متأخرة</td>
                                            </tr>
                                        
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقرير تفصيلي -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">التقرير التفصيلي</h5>
                </div>
                <div class="card-body">
                    <div id="detailedReport" class="text-center text-muted py-5">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <h5>اختر الفلاتر وانقر على "إنشاء التقرير" لعرض البيانات</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إعداد الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني للإيرادات الشهرية
    const monthlyCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات (ر.س)',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });

    // رسم بياني لتوزيع السائقين
    const classificationCtx = document.getElementById('driversClassificationChart').getContext('2d');
    new Chart(classificationCtx, {
        type: 'doughnut',
        data: {
            labels: ['ملتزم', 'متوسط', 'متأخر'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
});

function generateReport() {
    // هنا سيتم إضافة كود إنشاء التقرير
    document.getElementById('detailedReport').innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            سيتم تطوير هذه الميزة قريباً لعرض التقارير التفصيلية
        </div>
    `;
}

function exportReport(format) {
    // هنا سيتم إضافة كود تصدير التقرير
    alert('سيتم تطوير ميزة التصدير إلى ' + format.toUpperCase() + ' قريباً');
}
</script>

    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 نظام إدارة السائقين. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
    
    
</body>
</html>