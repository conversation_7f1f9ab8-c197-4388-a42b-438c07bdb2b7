{% extends "base.html" %}

{% block title %}تذكيرات المدفوعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    تذكيرات المدفوعات
                </h2>
                <a href="{{ url_for('payments_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمدفوعات
                </a>
            </div>

            <!-- الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ total_overdue }}</h4>
                                    <p class="card-text">سائقين متأخرين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ total_upcoming }}</h4>
                                    <p class="card-text">مستحقين قريباً</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ "{:,.0f}".format(total_overdue_amount) }} ر.س</h4>
                                    <p class="card-text">إجمالي المتأخرات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- السائقين المتأخرين -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                السائقين المتأخرين ({{ total_overdue }})
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if overdue_drivers %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>أيام التأخير</th>
                                            <th>المبلغ المستحق</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for driver in overdue_drivers %}
                                        {% set days_overdue = (moment().date() - driver.next_payment_due.date()).days if driver.next_payment_due else 0 %}
                                        {% set outstanding = driver.calculate_outstanding_amount() %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <div class="avatar-title bg-danger rounded-circle">
                                                            {{ driver.name[0] if driver.name else 'س' }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ driver.name }}</h6>
                                                        <small class="text-muted">{{ driver.phone }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-danger">
                                                    {{ driver.next_payment_due.strftime('%Y-%m-%d') if driver.next_payment_due else 'غير محدد' }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">
                                                    {{ ((moment().date() - driver.next_payment_due.date()).days) if driver.next_payment_due else 0 }} يوم
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-danger">{{ "{:,.0f}".format(outstanding) }} ر.س</strong>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="sendReminder({{ driver.id }}, '{{ driver.name }}')" title="إرسال تذكير">
                                                        <i class="fas fa-paper-plane"></i>
                                                    </button>
                                                    <a href="{{ url_for('driver_details', driver_id=driver.id) }}" class="btn btn-outline-info" title="تفاصيل السائق">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h5 class="text-success">ممتاز! لا يوجد سائقين متأخرين</h5>
                                <p class="text-muted">جميع السائقين ملتزمين بمواعيد الدفع</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- السائقين المستحقين قريباً -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                مستحقين خلال الأسبوع القادم ({{ total_upcoming }})
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if upcoming_drivers %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>المتبقي</th>
                                            <th>المبلغ المستحق</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for driver in upcoming_drivers %}
                                        {% set days_remaining = (driver.next_payment_due.date() - moment().date()).days if driver.next_payment_due else 0 %}
                                        {% set outstanding = driver.calculate_outstanding_amount() %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <div class="avatar-title bg-warning rounded-circle">
                                                            {{ driver.name[0] if driver.name else 'س' }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ driver.name }}</h6>
                                                        <small class="text-muted">{{ driver.phone }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-warning">
                                                    {{ driver.next_payment_due.strftime('%Y-%m-%d') if driver.next_payment_due else 'غير محدد' }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">
                                                    {{ days_remaining }} يوم
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-warning">{{ "{:,.0f}".format(outstanding) }} ر.س</strong>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="sendReminder({{ driver.id }}, '{{ driver.name }}')" title="إرسال تذكير">
                                                        <i class="fas fa-paper-plane"></i>
                                                    </button>
                                                    <a href="{{ url_for('driver_details', driver_id=driver.id) }}" class="btn btn-outline-info" title="تفاصيل السائق">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-check fa-3x text-success mb-3"></i>
                                <h5 class="text-success">لا يوجد مدفوعات مستحقة قريباً</h5>
                                <p class="text-muted">جميع المدفوعات القادمة بعد أسبوع أو أكثر</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendReminder(driverId, driverName) {
    if (confirm(`هل تريد إرسال تذكير دفع للسائق ${driverName}؟`)) {
        fetch(`/payments/send-reminder/${driverId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                alert(`تم إرسال التذكير للسائق ${driverName}`);
                location.reload();
            } else {
                alert('حدث خطأ في إرسال التذكير');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إرسال التذكير');
        });
    }
}
</script>
{% endblock %}
