{% extends "base.html" %}

{% block title %}تقرير المدفوعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-money-bill-wave"></i> تقرير المدفوعات المفصل</h2>
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للتقارير
                    </a>
                    <a href="{{ url_for('export_payments_excel') }}" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ "%.2f"|format(total_payments) }} ريال</h4>
                            <p class="mb-0">إجمالي المدفوعات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ completed_payments }}</h4>
                            <p class="mb-0">مدفوعة مكتملة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ pending_payments }}</h4>
                            <p class="mb-0">مدفوعة معلقة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المدفوعات -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table"></i> قائمة المدفوعات التفصيلية</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="paymentsTable">
                    <thead class="table-dark">
                        <tr>
                            <th>السائق</th>
                            <th>المبلغ</th>
                            <th>تاريخ الدفع</th>
                            <th>نوع الدفع</th>
                            <th>الحالة</th>
                            <th>طريقة الدفع</th>
                            <th>ملاحظات</th>
                            <th>تاريخ الإنشاء</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>{{ payment.driver.name if payment.driver else 'غير محدد' }}</td>
                            <td>{{ "%.2f"|format(payment.amount) }} ريال</td>
                            <td>{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '-' }}</td>
                            <td>{{ payment.payment_type or '-' }}</td>
                            <td>
                                {% if payment.status == 'مكتمل' %}
                                    <span class="badge bg-success">{{ payment.status }}</span>
                                {% elif payment.status == 'معلق' %}
                                    <span class="badge bg-warning">{{ payment.status }}</span>
                                {% elif payment.status == 'ملغي' %}
                                    <span class="badge bg-danger">{{ payment.status }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ payment.status or 'غير محدد' }}</span>
                                {% endif %}
                            </td>
                            <td>{{ payment.payment_method or '-' }}</td>
                            <td>{{ payment.notes or '-' }}</td>
                            <td>{{ payment.created_at.strftime('%Y-%m-%d %H:%M') if payment.created_at else '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل DataTable
$(document).ready(function() {
    $('#paymentsTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        order: [[7, 'desc']], // ترتيب حسب تاريخ الإنشاء
        pageLength: 25,
        columnDefs: [
            {
                targets: [1], // عمود المبلغ
                render: function(data, type, row) {
                    if (type === 'display' || type === 'type') {
                        return data;
                    }
                    return parseFloat(data.replace(' ريال', ''));
                }
            }
        ]
    });
});
</script>
{% endblock %}
