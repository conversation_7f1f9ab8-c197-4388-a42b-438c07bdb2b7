{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-users me-2"></i>
                    إدارة المستخدمين والأدوار
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للإعدادات
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>

            <!-- إحصائيات المستخدمين -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ total_users }}</h4>
                                    <p class="mb-0">إجمالي المستخدمين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ active_users }}</h4>
                                    <p class="mb-0">المستخدمين النشطين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ total_roles }}</h4>
                                    <p class="mb-0">الأدوار المتاحة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-tag fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ recent_logins }}</h4>
                                    <p class="mb-0">تسجيلات دخول اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sign-in-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المستخدمين -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المستخدمين
                        </h5>
                        <div class="input-group" style="width: 300px;">
                            <input type="text" class="form-control" placeholder="البحث في المستخدمين..." id="searchUsers">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>آخر تسجيل دخول</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <strong>{{ user.username }}</strong>
                                                {% if user.full_name %}
                                                <br><small class="text-muted">{{ user.full_name }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user.email or 'غير محدد' }}</td>
                                    <td>
                                        {% if user.role %}
                                            <span class="badge bg-info">{{ user.role.name }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">بدون دور</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.last_login %}
                                            {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            <span class="text-muted">لم يسجل دخول</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editUser({{ user.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="viewUserActivity({{ user.id }})" title="النشاط">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            {% if user.id != current_user.id %}
                                            <button class="btn btn-outline-warning" onclick="toggleUserStatus({{ user.id }})" title="تغيير الحالة">
                                                <i class="fas fa-power-off"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteUser({{ user.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- إدارة الأدوار -->
            <div class="card mt-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user-tag me-2"></i>
                            إدارة الأدوار والصلاحيات
                        </h5>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دور جديد
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for role in roles %}
                        <div class="col-md-4 mb-3">
                            <div class="card border">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">{{ role.name }}</h6>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="editRole({{ role.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if role.name not in ['مدير', 'مستخدم'] %}
                                        <button class="btn btn-outline-danger" onclick="deleteRole({{ role.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted small">{{ role.description or 'لا يوجد وصف' }}</p>
                                    <div class="mb-2">
                                        <strong>الصلاحيات:</strong>
                                    </div>
                                    {% set permissions = role.get_permissions() %}
                                    {% if permissions %}
                                        {% for permission in permissions %}
                                        <span class="badge bg-light text-dark me-1 mb-1">{{ permission }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">لا توجد صلاحيات</span>
                                    {% endif %}
                                    <hr>
                                    <small class="text-muted">
                                        المستخدمين: {{ role.users|length }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_user') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم *</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" name="full_name">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور *</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تأكيد كلمة المرور *</label>
                        <input type="password" class="form-control" name="confirm_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الدور</label>
                        <select class="form-select" name="role_id">
                            <option value="">اختر الدور</option>
                            {% for role in roles %}
                            <option value="{{ role.id }}">{{ role.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                مستخدم نشط
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة دور جديد -->
<div class="modal fade" id="addRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-tag me-2"></i>
                    إضافة دور جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_role') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الدور *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">وصف الدور</label>
                        <textarea class="form-control" name="description" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الصلاحيات</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="view_drivers" id="perm_view_drivers">
                                    <label class="form-check-label" for="perm_view_drivers">عرض السائقين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="add_drivers" id="perm_add_drivers">
                                    <label class="form-check-label" for="perm_add_drivers">إضافة السائقين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="edit_drivers" id="perm_edit_drivers">
                                    <label class="form-check-label" for="perm_edit_drivers">تعديل السائقين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="delete_drivers" id="perm_delete_drivers">
                                    <label class="form-check-label" for="perm_delete_drivers">حذف السائقين</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="view_payments" id="perm_view_payments">
                                    <label class="form-check-label" for="perm_view_payments">عرض المدفوعات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="add_payments" id="perm_add_payments">
                                    <label class="form-check-label" for="perm_add_payments">إضافة المدفوعات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="edit_payments" id="perm_edit_payments">
                                    <label class="form-check-label" for="perm_edit_payments">تعديل المدفوعات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="view_reports" id="perm_view_reports">
                                    <label class="form-check-label" for="perm_view_reports">عرض التقارير</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions" value="manage_settings" id="perm_manage_settings">
                                    <label class="form-check-label" for="perm_manage_settings">إدارة الإعدادات</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة الدور</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editUser(userId) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    showAlert('سيتم تطوير ميزة تعديل المستخدم قريباً', 'info');
}

function viewUserActivity(userId) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    showAlert('سيتم تطوير ميزة عرض نشاط المستخدم قريباً', 'info');
}

function toggleUserStatus(userId) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')) {
        fetch(`/toggle_user_status/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم تغيير حالة المستخدم بنجاح', 'success');
                location.reload();
            } else {
                showAlert('حدث خطأ في تغيير حالة المستخدم', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/delete_user/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف المستخدم بنجاح', 'success');
                location.reload();
            } else {
                showAlert('حدث خطأ في حذف المستخدم', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

function editRole(roleId) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    showAlert('سيتم تطوير ميزة تعديل الدور قريباً', 'info');
}

function deleteRole(roleId) {
    if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
        fetch(`/delete_role/${roleId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف الدور بنجاح', 'success');
                location.reload();
            } else {
                showAlert('حدث خطأ في حذف الدور', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

// البحث في المستخدمين
document.getElementById('searchUsers').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
