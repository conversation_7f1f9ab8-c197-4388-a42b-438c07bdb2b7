{% extends "base.html" %}

{% block title %}تحليلات المدفوعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    تحليلات المدفوعات المتقدمة
                </h2>
                <a href="{{ url_for('payments_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمدفوعات
                </a>
            </div>

            <!-- الإحصائيات الأساسية -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ total_payments }}</h4>
                                    <p class="card-text">إجمالي المدفوعات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-receipt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ "{:,.0f}".format(total_amount) }} ر.س</h4>
                                    <p class="card-text">إجمالي المبلغ</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ "{:,.0f}".format(avg_payment) }} ر.س</h4>
                                    <p class="card-text">متوسط الدفعة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calculator fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ overdue_analysis|length }}</h4>
                                    <p class="card-text">سائقين متأخرين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row mb-4">
                <!-- المدفوعات الشهرية -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">المدفوعات الشهرية - {{ monthly_payments[0].month_name.split()[0] if monthly_payments else '' }} {{ "2024" }}</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyPaymentsChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- المدفوعات حسب الطريقة -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">طرق الدفع</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="paymentMethodsChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أفضل السائقين -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أفضل 10 سائقين (المدفوعات)</h5>
                        </div>
                        <div class="card-body">
                            {% if top_drivers %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>عدد المدفوعات</th>
                                            <th>إجمالي المبلغ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for driver in top_drivers %}
                                        <tr>
                                            <td>{{ driver.name }}</td>
                                            <td>{{ driver.payment_count }}</td>
                                            <td>{{ "{:,.0f}".format(driver.total_paid) }} ر.س</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted">لا توجد بيانات</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- السائقين المتأخرين -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">السائقين المتأخرين</h5>
                        </div>
                        <div class="card-body">
                            {% if overdue_analysis %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>أيام التأخير</th>
                                            <th>المبلغ المستحق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in overdue_analysis[:10] %}
                                        <tr>
                                            <td>{{ item.driver.name }}</td>
                                            <td>
                                                <span class="badge bg-danger">{{ item.days_overdue }} يوم</span>
                                            </td>
                                            <td>{{ "{:,.0f}".format(item.outstanding_amount) }} ر.س</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-success">لا يوجد سائقين متأخرين</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- المدفوعات حسب الحالة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">المدفوعات حسب الحالة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% for status in payment_statuses %}
                                <div class="col-md-4 mb-3">
                                    <div class="card border-start border-4 
                                        {% if status.status == 'مكتمل' %}border-success{% elif status.status == 'معلق' %}border-warning{% else %}border-danger{% endif %}">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ status.status }}</h6>
                                            <p class="card-text">
                                                <strong>{{ status.count }}</strong> دفعة<br>
                                                <strong>{{ "{:,.0f}".format(status.total) }}</strong> ر.س
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمدفوعات الشهرية
const monthlyCtx = document.getElementById('monthlyPaymentsChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: [{% for month in monthly_payments %}'{{ month.month_name }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'المدفوعات (ر.س)',
            data: [{% for month in monthly_payments %}{{ month.total }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ر.س';
                    }
                }
            }
        }
    }
});

// رسم بياني لطرق الدفع
const methodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
const methodsChart = new Chart(methodsCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for method in payment_methods %}'{{ method.payment_method }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for method in payment_methods %}{{ method.total }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
