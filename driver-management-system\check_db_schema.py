#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص مخطط قاعدة البيانات
Check database schema
"""

import sqlite3
import os

def check_schema():
    """فحص مخطط قاعدة البيانات"""
    db_path = 'instance/driver_management.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📋 الجداول الموجودة:")
        for table in tables:
            table_name = table[0]
            print(f"\n🔍 جدول: {table_name}")
            
            # الحصول على أعمدة الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for column in columns:
                col_id, col_name, col_type, not_null, default_val, pk = column
                print(f"  - {col_name}: {col_type} {'(NOT NULL)' if not_null else ''} {'(PK)' if pk else ''}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == '__main__':
    check_schema()
