#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة السائقين المتطور
Driver Management System with AI Features
"""

import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime, timedelta
import json
from export_utils import ExcelExporter, PDFExporter

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///driver_management.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))

# إعداد مجلدات الرفع
app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', 'uploads')
app.config['REPORTS_FOLDER'] = os.getenv('REPORTS_FOLDER', 'reports')
app.config['DOCUMENTS_FOLDER'] = os.getenv('DOCUMENTS_FOLDER', 'documents')

# إنشاء المجلدات إذا لم تكن موجودة
for folder in [app.config['UPLOAD_FOLDER'], app.config['REPORTS_FOLDER'], app.config['DOCUMENTS_FOLDER']]:
    os.makedirs(folder, exist_ok=True)

# استيراد النماذج أولاً
from models import db, User, Driver, Payment, Document, AIAnalysis, Settings, Role, ActivityLog, BackupLog

# تهيئة قاعدة البيانات مع التطبيق
db.init_app(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# إعداد مدة الجلسة (30 دقيقة)
app.permanent_session_lifetime = timedelta(minutes=30)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# إضافة middleware للتحقق من انتهاء الجلسة
@app.before_request
def check_session_timeout():
    """التحقق من انتهاء مدة الجلسة"""
    from flask_login import current_user
    from flask import session

    # تجاهل الطلبات للملفات الثابتة وصفحات تسجيل الدخول
    if request.endpoint in ['static', 'login', 'forgot_password', 'reset_password']:
        return

    # إذا كان المستخدم مسجل دخول
    if current_user.is_authenticated:
        # التحقق من آخر نشاط
        last_activity = session.get('last_activity')
        if last_activity:
            from datetime import datetime
            last_activity_time = datetime.fromisoformat(last_activity)
            if datetime.utcnow() - last_activity_time > app.permanent_session_lifetime:
                # انتهت مدة الجلسة
                logout_user()
                flash('انتهت مدة جلستك. يرجى تسجيل الدخول مرة أخرى.', 'warning')
                return redirect(url_for('login'))

        # تحديث آخر نشاط
        session['last_activity'] = datetime.utcnow().isoformat()
        session.permanent = True

# إضافة رؤوس الأمان
@app.after_request
def add_security_headers(response):
    """إضافة رؤوس الأمان للاستجابات"""
    # منع تضمين الصفحة في إطارات خارجية
    response.headers['X-Frame-Options'] = 'DENY'

    # منع تخمين نوع المحتوى
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # تفعيل حماية XSS في المتصفح
    response.headers['X-XSS-Protection'] = '1; mode=block'

    # سياسة أمان المحتوى الأساسية
    response.headers['Content-Security-Policy'] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
        "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; "
        "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; "
        "img-src 'self' data: https:; "
        "connect-src 'self';"
    )

    # منع إرسال معلومات المرجع
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    return response

# الصفحة الرئيسية
@app.route('/')
@login_required
def dashboard():
    """لوحة التحكم الذكية المحسنة"""
    from sqlalchemy import extract, func
    from datetime import datetime, timedelta

    # إحصائيات أساسية
    total_drivers = Driver.query.count()
    active_drivers = Driver.query.filter_by(status='نشط').count()
    inactive_drivers = Driver.query.filter_by(status='معلق').count()
    late_drivers = Driver.query.filter_by(payment_status='متأخر').count()

    # إحصائيات المدفوعات المتقدمة
    total_revenue = db.session.query(func.sum(Payment.amount)).scalar() or 0

    # إيرادات هذا الشهر
    current_month = datetime.now().replace(day=1)
    this_month_revenue = db.session.query(func.sum(Payment.amount)).filter(
        Payment.payment_date >= current_month
    ).scalar() or 0

    # إيرادات الشهر الماضي للمقارنة
    last_month = (current_month - timedelta(days=1)).replace(day=1)
    last_month_revenue = db.session.query(func.sum(Payment.amount)).filter(
        Payment.payment_date >= last_month,
        Payment.payment_date < current_month
    ).scalar() or 0

    # حساب نسبة النمو
    growth_rate = 0
    if last_month_revenue > 0:
        growth_rate = ((this_month_revenue - last_month_revenue) / last_month_revenue) * 100

    # إحصائيات المدفوعات حسب الحالة
    paid_count = Payment.query.filter_by(status='مدفوع').count()
    pending_count = Payment.query.filter_by(status='معلق').count()
    overdue_count = Payment.query.filter_by(status='متأخر').count()

    # إيرادات آخر 6 أشهر للرسم البياني
    monthly_revenue = []
    monthly_labels = []
    for i in range(5, -1, -1):
        month_date = datetime.now().replace(day=1) - timedelta(days=30*i)
        next_month = (month_date + timedelta(days=32)).replace(day=1)

        revenue = db.session.query(func.sum(Payment.amount)).filter(
            Payment.payment_date >= month_date,
            Payment.payment_date < next_month
        ).scalar() or 0

        monthly_revenue.append(float(revenue))
        monthly_labels.append(month_date.strftime('%B'))

    # إحصائيات أنواع المركبات
    vehicle_stats = db.session.query(
        Driver.vehicle_type,
        func.count(Driver.id)
    ).group_by(Driver.vehicle_type).all()

    # إحصائيات طرق الدفع
    payment_method_stats = db.session.query(
        Payment.payment_method,
        func.count(Payment.id),
        func.sum(Payment.amount)
    ).group_by(Payment.payment_method).all()

    # السائقين الأحدث
    recent_drivers = Driver.query.order_by(Driver.created_at.desc()).limit(5).all()

    # المدفوعات الأخيرة
    recent_payments = Payment.query.order_by(Payment.payment_date.desc()).limit(5).all()

    # السائقين مع مواقع
    drivers_with_location = Driver.query.filter(
        Driver.current_latitude.isnot(None),
        Driver.current_longitude.isnot(None)
    ).count()

    # متوسط المدفوعات
    avg_payment = db.session.query(func.avg(Payment.amount)).scalar() or 0

    return render_template('dashboard.html',
                         total_drivers=total_drivers,
                         active_drivers=active_drivers,
                         inactive_drivers=inactive_drivers,
                         late_drivers=late_drivers,
                         total_revenue=total_revenue,
                         this_month_revenue=this_month_revenue,
                         last_month_revenue=last_month_revenue,
                         growth_rate=growth_rate,
                         paid_count=paid_count,
                         pending_count=pending_count,
                         overdue_count=overdue_count,
                         monthly_revenue=monthly_revenue,
                         monthly_labels=monthly_labels,
                         vehicle_stats=vehicle_stats,
                         payment_method_stats=payment_method_stats,
                         recent_drivers=recent_drivers,
                         recent_payments=recent_payments,
                         drivers_with_location=drivers_with_location,
                         avg_payment=avg_payment)

@app.route('/api/dashboard-data')
@login_required
def dashboard_data_api():
    """API لجلب بيانات لوحة التحكم في الوقت الفعلي"""
    from sqlalchemy import extract, func
    from datetime import datetime, timedelta

    try:
        # إحصائيات أساسية
        total_drivers = Driver.query.count()
        active_drivers = Driver.query.filter_by(status='نشط').count()
        inactive_drivers = Driver.query.filter_by(status='معلق').count()
        late_drivers = Driver.query.filter_by(payment_status='متأخر').count()

        # إحصائيات المدفوعات
        total_revenue = db.session.query(func.sum(Payment.amount)).scalar() or 0
        current_month = datetime.now().replace(day=1)
        this_month_revenue = db.session.query(func.sum(Payment.amount)).filter(
            Payment.payment_date >= current_month
        ).scalar() or 0

        # إحصائيات المدفوعات حسب الحالة
        paid_count = Payment.query.filter_by(status='مدفوع').count()
        pending_count = Payment.query.filter_by(status='معلق').count()
        overdue_count = Payment.query.filter_by(status='متأخر').count()

        # السائقين مع مواقع
        drivers_with_location = Driver.query.filter(
            Driver.current_latitude.isnot(None),
            Driver.current_longitude.isnot(None)
        ).count()

        # متوسط المدفوعات
        avg_payment = db.session.query(func.avg(Payment.amount)).scalar() or 0

        # إيرادات آخر 7 أيام للرسم البياني السريع
        daily_revenue = []
        for i in range(6, -1, -1):
            day_date = datetime.now().date() - timedelta(days=i)
            next_day = day_date + timedelta(days=1)

            revenue = db.session.query(func.sum(Payment.amount)).filter(
                Payment.payment_date >= day_date,
                Payment.payment_date < next_day
            ).scalar() or 0

            daily_revenue.append({
                'date': day_date.strftime('%Y-%m-%d'),
                'revenue': float(revenue)
            })

        return jsonify({
            'success': True,
            'data': {
                'total_drivers': total_drivers,
                'active_drivers': active_drivers,
                'inactive_drivers': inactive_drivers,
                'late_drivers': late_drivers,
                'total_revenue': float(total_revenue),
                'this_month_revenue': float(this_month_revenue),
                'paid_count': paid_count,
                'pending_count': pending_count,
                'overdue_count': overdue_count,
                'drivers_with_location': drivers_with_location,
                'avg_payment': float(avg_payment),
                'daily_revenue': daily_revenue,
                'last_updated': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/ai/analyze')
@login_required
def ai_analysis_dashboard():
    """لوحة تحكم تحليلات الذكاء الاصطناعي"""
    try:
        # تشغيل التحليلات للجميع
        analyze_all_drivers()

        # جلب أحدث التحليلات
        recent_analyses = AIAnalysis.query.order_by(AIAnalysis.created_at.desc()).limit(20).all()

        # إحصائيات التحليلات
        analysis_stats = {
            'total_analyses': AIAnalysis.query.count(),
            'high_risk_drivers': AIAnalysis.query.filter(AIAnalysis.prediction_score < 0.3).count(),
            'medium_risk_drivers': AIAnalysis.query.filter(
                AIAnalysis.prediction_score >= 0.3,
                AIAnalysis.prediction_score < 0.7
            ).count(),
            'low_risk_drivers': AIAnalysis.query.filter(AIAnalysis.prediction_score >= 0.7).count(),
            'payment_pattern_analyses': AIAnalysis.query.filter_by(analysis_type='payment_pattern').count(),
            'delay_predictions': AIAnalysis.query.filter_by(analysis_type='delay_prediction').count(),
            'performance_analyses': AIAnalysis.query.filter_by(analysis_type='performance').count()
        }

        # توصيات عامة
        general_recommendations = generate_general_recommendations()

        return render_template('ai_analysis.html',
                             recent_analyses=recent_analyses,
                             analysis_stats=analysis_stats,
                             general_recommendations=general_recommendations)

    except Exception as e:
        flash(f'حدث خطأ في تحميل تحليلات الذكاء الاصطناعي: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

def analyze_all_drivers():
    """تحليل جميع السائقين باستخدام الذكاء الاصطناعي"""
    drivers = Driver.query.all()

    for driver in drivers:
        try:
            # تحليل أنماط الدفع
            payment_analysis = analyze_payment_patterns(driver)
            save_ai_analysis(driver.id, 'payment_pattern', payment_analysis)

            # توقع التأخير
            delay_prediction = predict_payment_delay(driver)
            save_ai_analysis(driver.id, 'delay_prediction', delay_prediction)

            # تحليل الأداء
            performance_analysis = analyze_driver_performance(driver)
            save_ai_analysis(driver.id, 'performance', performance_analysis)

        except Exception as e:
            print(f"خطأ في تحليل السائق {driver.name}: {str(e)}")
            continue

def analyze_payment_patterns(driver):
    """تحليل أنماط الدفع للسائق"""
    from datetime import datetime, timedelta
    import statistics

    # جلب مدفوعات آخر 6 أشهر
    six_months_ago = datetime.now() - timedelta(days=180)
    payments = Payment.query.filter(
        Payment.driver_id == driver.id,
        Payment.payment_date >= six_months_ago,
        Payment.status == 'مكتمل'
    ).order_by(Payment.payment_date).all()

    if len(payments) < 2:
        return {
            'pattern_type': 'insufficient_data',
            'regularity_score': 0.0,
            'average_amount': driver.payment_amount or 0,
            'payment_frequency': 'غير محدد',
            'trend': 'غير محدد',
            'recommendations': ['يحتاج المزيد من البيانات للتحليل']
        }

    # حساب الانتظام في المدفوعات
    payment_intervals = []
    for i in range(1, len(payments)):
        interval = (payments[i].payment_date - payments[i-1].payment_date).days
        payment_intervals.append(interval)

    # حساب درجة الانتظام (0-1)
    if payment_intervals:
        avg_interval = statistics.mean(payment_intervals)
        interval_std = statistics.stdev(payment_intervals) if len(payment_intervals) > 1 else 0
        regularity_score = max(0, 1 - (interval_std / avg_interval)) if avg_interval > 0 else 0
    else:
        regularity_score = 0

    # تحليل الاتجاه
    amounts = [p.amount for p in payments[-6:]]  # آخر 6 مدفوعات
    if len(amounts) >= 3:
        recent_avg = statistics.mean(amounts[-3:])
        older_avg = statistics.mean(amounts[:-3]) if len(amounts) > 3 else amounts[0]

        if recent_avg > older_avg * 1.1:
            trend = 'متزايد'
        elif recent_avg < older_avg * 0.9:
            trend = 'متناقص'
        else:
            trend = 'مستقر'
    else:
        trend = 'غير محدد'

    # تحديد نوع النمط
    if regularity_score >= 0.8:
        pattern_type = 'منتظم جداً'
    elif regularity_score >= 0.6:
        pattern_type = 'منتظم'
    elif regularity_score >= 0.4:
        pattern_type = 'متوسط الانتظام'
    else:
        pattern_type = 'غير منتظم'

    # توليد التوصيات
    recommendations = []
    if regularity_score < 0.5:
        recommendations.append('يحتاج لتحسين انتظام المدفوعات')
    if trend == 'متناقص':
        recommendations.append('مراقبة انخفاض قيمة المدفوعات')
    if len(payments) < 5:
        recommendations.append('تشجيع على المزيد من المدفوعات المنتظمة')

    if not recommendations:
        recommendations.append('أداء جيد في المدفوعات')

    return {
        'pattern_type': pattern_type,
        'regularity_score': round(regularity_score, 2),
        'average_amount': round(statistics.mean([p.amount for p in payments]), 2),
        'payment_frequency': f'{len(payments)} دفعة في 6 أشهر',
        'trend': trend,
        'recommendations': recommendations,
        'total_payments': len(payments),
        'last_payment_date': payments[-1].payment_date.strftime('%Y-%m-%d') if payments else None
    }

def predict_payment_delay(driver):
    """توقع احتمالية تأخير المدفوعات"""
    from datetime import datetime, timedelta
    import statistics

    # جلب تاريخ المدفوعات
    payments = Payment.query.filter(
        Payment.driver_id == driver.id,
        Payment.status == 'مكتمل'
    ).order_by(Payment.payment_date).all()

    if len(payments) < 3:
        return {
            'delay_probability': 0.5,  # احتمالية متوسطة للبيانات غير الكافية
            'risk_level': 'متوسط',
            'predicted_next_payment': None,
            'factors': ['بيانات غير كافية للتنبؤ الدقيق'],
            'recommendations': ['جمع المزيد من بيانات المدفوعات']
        }

    # حساب متوسط الفترة بين المدفوعات
    intervals = []
    for i in range(1, len(payments)):
        interval = (payments[i].payment_date - payments[i-1].payment_date).days
        intervals.append(interval)

    avg_interval = statistics.mean(intervals)
    interval_std = statistics.stdev(intervals) if len(intervals) > 1 else 0

    # تحليل العوامل المؤثرة
    factors = []
    risk_score = 0.5  # نقطة البداية

    # 1. انتظام المدفوعات
    regularity = 1 - (interval_std / avg_interval) if avg_interval > 0 else 0
    if regularity < 0.5:
        risk_score += 0.2
        factors.append('عدم انتظام في المدفوعات')
    else:
        risk_score -= 0.1
        factors.append('انتظام جيد في المدفوعات')

    # 2. اتجاه المبالغ
    recent_amounts = [p.amount for p in payments[-5:]]
    older_amounts = [p.amount for p in payments[:-5]] if len(payments) > 5 else [payments[0].amount]

    recent_avg = statistics.mean(recent_amounts)
    older_avg = statistics.mean(older_amounts)

    if recent_avg < older_avg * 0.8:
        risk_score += 0.15
        factors.append('انخفاض في قيمة المدفوعات')
    elif recent_avg > older_avg * 1.2:
        risk_score -= 0.1
        factors.append('زيادة في قيمة المدفوعات')

    # 3. الفترة منذ آخر دفعة
    last_payment = payments[-1]
    days_since_last = (datetime.now() - last_payment.payment_date).days

    if days_since_last > avg_interval * 1.5:
        risk_score += 0.25
        factors.append('تأخير حالي في المدفوعات')
    elif days_since_last < avg_interval * 0.5:
        risk_score -= 0.1
        factors.append('مدفوعات مبكرة')

    # 4. حالة السائق
    if driver.status != 'نشط':
        risk_score += 0.2
        factors.append('حالة السائق غير نشطة')

    # 5. نوع الدفع
    if driver.payment_type == 'أسبوعي':
        risk_score -= 0.05  # المدفوعات الأسبوعية أكثر انتظاماً

    # تحديد مستوى المخاطر
    risk_score = max(0, min(1, risk_score))  # ضمان القيمة بين 0 و 1

    if risk_score >= 0.7:
        risk_level = 'عالي'
    elif risk_score >= 0.4:
        risk_level = 'متوسط'
    else:
        risk_level = 'منخفض'

    # توقع موعد الدفعة التالية
    predicted_next = last_payment.payment_date + timedelta(days=int(avg_interval))

    # توليد التوصيات
    recommendations = []
    if risk_score >= 0.6:
        recommendations.extend([
            'متابعة مكثفة مع السائق',
            'إرسال تذكيرات مبكرة',
            'مراجعة شروط الدفع'
        ])
    elif risk_score >= 0.4:
        recommendations.extend([
            'متابعة دورية',
            'إرسال تذكيرات قبل موعد الاستحقاق'
        ])
    else:
        recommendations.append('متابعة عادية')

    return {
        'delay_probability': round(risk_score, 2),
        'risk_level': risk_level,
        'predicted_next_payment': predicted_next.strftime('%Y-%m-%d'),
        'factors': factors,
        'recommendations': recommendations,
        'avg_interval_days': round(avg_interval, 1),
        'days_since_last_payment': days_since_last
    }

def analyze_driver_performance(driver):
    """تحليل أداء السائق الشامل"""
    from datetime import datetime, timedelta
    import statistics

    # جلب البيانات
    payments = Payment.query.filter_by(driver_id=driver.id).all()
    completed_payments = [p for p in payments if p.status == 'مكتمل']

    # الإحصائيات الأساسية
    total_payments = len(payments)
    completed_count = len(completed_payments)
    success_rate = (completed_count / total_payments * 100) if total_payments > 0 else 0

    # تحليل الإيرادات
    total_revenue = sum(p.amount for p in completed_payments)
    avg_payment = statistics.mean([p.amount for p in completed_payments]) if completed_payments else 0

    # تحليل الاتجاهات (آخر 3 أشهر مقابل 3 أشهر قبلها)
    three_months_ago = datetime.now() - timedelta(days=90)
    six_months_ago = datetime.now() - timedelta(days=180)

    recent_payments = [p for p in completed_payments if p.payment_date >= three_months_ago]
    older_payments = [p for p in completed_payments if six_months_ago <= p.payment_date < three_months_ago]

    recent_revenue = sum(p.amount for p in recent_payments)
    older_revenue = sum(p.amount for p in older_payments)

    # حساب النمو
    if older_revenue > 0:
        growth_rate = ((recent_revenue - older_revenue) / older_revenue) * 100
    else:
        growth_rate = 0

    # تقييم الأداء العام
    performance_score = 0

    # معايير التقييم
    if success_rate >= 90:
        performance_score += 30
    elif success_rate >= 75:
        performance_score += 20
    elif success_rate >= 60:
        performance_score += 10

    if growth_rate > 10:
        performance_score += 25
    elif growth_rate > 0:
        performance_score += 15
    elif growth_rate > -10:
        performance_score += 5

    if total_revenue > 5000:
        performance_score += 20
    elif total_revenue > 2000:
        performance_score += 15
    elif total_revenue > 500:
        performance_score += 10

    if len(recent_payments) >= 3:
        performance_score += 15
    elif len(recent_payments) >= 1:
        performance_score += 10

    # تحديد مستوى الأداء
    if performance_score >= 80:
        performance_level = 'ممتاز'
    elif performance_score >= 60:
        performance_level = 'جيد'
    elif performance_score >= 40:
        performance_level = 'متوسط'
    else:
        performance_level = 'ضعيف'

    # توليد التوصيات
    recommendations = []
    if performance_score < 40:
        recommendations.extend([
            'يحتاج لمتابعة مكثفة',
            'مراجعة شروط التعاقد',
            'تقديم الدعم والتدريب'
        ])
    elif performance_score < 60:
        recommendations.extend([
            'تشجيع على تحسين الأداء',
            'متابعة دورية'
        ])
    elif performance_score < 80:
        recommendations.extend([
            'أداء جيد، يمكن تحسينه',
            'تقديم حوافز للتطوير'
        ])
    else:
        recommendations.extend([
            'أداء ممتاز، استمرار على نفس المستوى',
            'يمكن اعتباره نموذج للآخرين'
        ])

    return {
        'performance_score': performance_score,
        'performance_level': performance_level,
        'success_rate': round(success_rate, 1),
        'total_revenue': round(total_revenue, 2),
        'average_payment': round(avg_payment, 2),
        'growth_rate': round(growth_rate, 1),
        'recent_payments_count': len(recent_payments),
        'total_payments_count': total_payments,
        'recommendations': recommendations,
        'last_activity': payments[-1].payment_date.strftime('%Y-%m-%d') if payments else None
    }

def save_ai_analysis(driver_id, analysis_type, analysis_data):
    """حفظ نتائج التحليل في قاعدة البيانات"""
    try:
        # حذف التحليل القديم من نفس النوع
        AIAnalysis.query.filter_by(
            driver_id=driver_id,
            analysis_type=analysis_type
        ).delete()

        # حساب درجة التوقع الإجمالية
        prediction_score = 0.5  # القيمة الافتراضية

        if analysis_type == 'payment_pattern':
            prediction_score = analysis_data.get('regularity_score', 0.5)
        elif analysis_type == 'delay_prediction':
            prediction_score = 1 - analysis_data.get('delay_probability', 0.5)
        elif analysis_type == 'performance':
            prediction_score = analysis_data.get('performance_score', 50) / 100

        # إنشاء تحليل جديد
        new_analysis = AIAnalysis(
            driver_id=driver_id,
            analysis_type=analysis_type,
            prediction_score=prediction_score,
            recommendations='; '.join(analysis_data.get('recommendations', []))
        )

        new_analysis.set_analysis_data(analysis_data)

        db.session.add(new_analysis)
        db.session.commit()

    except Exception as e:
        print(f"خطأ في حفظ التحليل: {str(e)}")
        db.session.rollback()

def generate_general_recommendations():
    """توليد توصيات عامة للنظام"""
    try:
        # إحصائيات عامة
        total_drivers = Driver.query.count()
        active_drivers = Driver.query.filter_by(status='نشط').count()
        overdue_drivers = Driver.query.filter(Driver.next_payment_due < datetime.now()).count()

        # تحليل المخاطر العالية
        high_risk_analyses = AIAnalysis.query.filter(
            AIAnalysis.prediction_score < 0.3,
            AIAnalysis.analysis_type == 'delay_prediction'
        ).count()

        # تحليل الأداء المنخفض
        low_performance = AIAnalysis.query.filter(
            AIAnalysis.prediction_score < 0.4,
            AIAnalysis.analysis_type == 'performance'
        ).count()

        recommendations = []

        # توصيات بناءً على النسب
        if total_drivers > 0:
            overdue_percentage = (overdue_drivers / total_drivers) * 100
            high_risk_percentage = (high_risk_analyses / total_drivers) * 100
            low_performance_percentage = (low_performance / total_drivers) * 100

            if overdue_percentage > 20:
                recommendations.append({
                    'type': 'تحذير',
                    'title': 'نسبة عالية من السائقين المتأخرين',
                    'description': f'{overdue_percentage:.1f}% من السائقين لديهم مدفوعات متأخرة',
                    'action': 'تفعيل نظام التذكيرات التلقائية'
                })

            if high_risk_percentage > 15:
                recommendations.append({
                    'type': 'تحذير',
                    'title': 'سائقين عالي المخاطر',
                    'description': f'{high_risk_analyses} سائق معرض لخطر التأخير',
                    'action': 'متابعة مكثفة مع السائقين عالي المخاطر'
                })

            if low_performance_percentage > 25:
                recommendations.append({
                    'type': 'تحسين',
                    'title': 'أداء منخفض',
                    'description': f'{low_performance} سائق يحتاج لتحسين الأداء',
                    'action': 'برنامج تدريب وتطوير للسائقين'
                })

            # توصيات إيجابية
            if overdue_percentage < 10:
                recommendations.append({
                    'type': 'إيجابي',
                    'title': 'أداء ممتاز في المدفوعات',
                    'description': 'نسبة منخفضة من التأخير في المدفوعات',
                    'action': 'الحفاظ على النظام الحالي'
                })

        # توصيات عامة للنظام
        recommendations.extend([
            {
                'type': 'تطوير',
                'title': 'تحسين نظام التحليلات',
                'description': 'تطوير المزيد من خوارزميات التحليل',
                'action': 'إضافة تحليلات جديدة للسلوك والأنماط'
            },
            {
                'type': 'أتمتة',
                'title': 'أتمتة العمليات',
                'description': 'تفعيل المزيد من العمليات التلقائية',
                'action': 'إعداد تقارير وإشعارات تلقائية'
            }
        ])

        return recommendations

    except Exception as e:
        print(f"خطأ في توليد التوصيات العامة: {str(e)}")
        return []

@app.route('/ai/driver/<int:driver_id>')
@login_required
def ai_driver_analysis(driver_id):
    """تحليل ذكي مفصل لسائق محدد"""
    driver = Driver.query.get_or_404(driver_id)

    try:
        # تشغيل التحليلات للسائق
        payment_analysis = analyze_payment_patterns(driver)
        delay_prediction = predict_payment_delay(driver)
        performance_analysis = analyze_driver_performance(driver)

        # حفظ التحليلات
        save_ai_analysis(driver.id, 'payment_pattern', payment_analysis)
        save_ai_analysis(driver.id, 'delay_prediction', delay_prediction)
        save_ai_analysis(driver.id, 'performance', performance_analysis)

        # جلب التحليلات المحفوظة
        analyses = AIAnalysis.query.filter_by(driver_id=driver_id).all()

        # تحليل مقارن مع السائقين الآخرين
        comparative_analysis = generate_comparative_analysis(driver)

        return render_template('ai_driver_detail.html',
                             driver=driver,
                             payment_analysis=payment_analysis,
                             delay_prediction=delay_prediction,
                             performance_analysis=performance_analysis,
                             analyses=analyses,
                             comparative_analysis=comparative_analysis)

    except Exception as e:
        flash(f'حدث خطأ في تحليل السائق: {str(e)}', 'error')
        return redirect(url_for('drivers'))

def generate_comparative_analysis(driver):
    """تحليل مقارن للسائق مع الآخرين"""
    import statistics
    try:
        # جلب إحصائيات السائق
        driver_payments = Payment.query.filter_by(driver_id=driver.id, status='مكتمل').all()
        driver_revenue = sum(p.amount for p in driver_payments)
        driver_payment_count = len(driver_payments)

        # إحصائيات عامة لجميع السائقين
        all_drivers = Driver.query.all()
        all_revenues = []
        all_payment_counts = []

        for d in all_drivers:
            d_payments = Payment.query.filter_by(driver_id=d.id, status='مكتمل').all()
            d_revenue = sum(p.amount for p in d_payments)
            all_revenues.append(d_revenue)
            all_payment_counts.append(len(d_payments))

        # حساب المراتب
        revenue_rank = sum(1 for r in all_revenues if r > driver_revenue) + 1
        payment_count_rank = sum(1 for c in all_payment_counts if c > driver_payment_count) + 1

        # حساب النسب المئوية
        revenue_percentile = ((len(all_revenues) - revenue_rank + 1) / len(all_revenues)) * 100
        payment_percentile = ((len(all_payment_counts) - payment_count_rank + 1) / len(all_payment_counts)) * 100

        # متوسط النظام
        avg_revenue = statistics.mean(all_revenues) if all_revenues else 0
        avg_payment_count = statistics.mean(all_payment_counts) if all_payment_counts else 0

        return {
            'revenue_rank': revenue_rank,
            'payment_count_rank': payment_count_rank,
            'revenue_percentile': round(revenue_percentile, 1),
            'payment_percentile': round(payment_percentile, 1),
            'vs_avg_revenue': round(((driver_revenue - avg_revenue) / avg_revenue * 100) if avg_revenue > 0 else 0, 1),
            'vs_avg_payments': round(((driver_payment_count - avg_payment_count) / avg_payment_count * 100) if avg_payment_count > 0 else 0, 1),
            'total_drivers': len(all_drivers)
        }

    except Exception as e:
        print(f"خطأ في التحليل المقارن: {str(e)}")
        return {}

@app.route('/dashboard/analytics')
@login_required
def dashboard_analytics():
    """صفحة تحليلات لوحة التحكم المتقدمة"""
    from sqlalchemy import extract, func, text
    from datetime import datetime, timedelta

    try:
        # تحليلات الأداء المتقدمة

        # 1. تحليل اتجاهات السائقين (آخر 12 شهر)
        driver_trends = []
        for i in range(11, -1, -1):
            month_date = datetime.now().replace(day=1) - timedelta(days=30*i)
            next_month = (month_date + timedelta(days=32)).replace(day=1)

            new_drivers = Driver.query.filter(
                Driver.created_at >= month_date,
                Driver.created_at < next_month
            ).count()

            driver_trends.append({
                'month': month_date.strftime('%Y-%m'),
                'month_name': month_date.strftime('%B %Y'),
                'new_drivers': new_drivers
            })

        # 2. تحليل الإيرادات حسب نوع المركبة
        vehicle_revenue = db.session.query(
            Driver.vehicle_type,
            func.count(Payment.id).label('payment_count'),
            func.sum(Payment.amount).label('total_revenue'),
            func.avg(Payment.amount).label('avg_payment')
        ).join(Payment, Driver.id == Payment.driver_id)\
         .group_by(Driver.vehicle_type)\
         .all()

        # 3. تحليل أداء المدفوعات الشهرية
        monthly_payment_analysis = []
        for i in range(5, -1, -1):
            month_date = datetime.now().replace(day=1) - timedelta(days=30*i)
            next_month = (month_date + timedelta(days=32)).replace(day=1)

            payments_data = db.session.query(
                func.count(Payment.id).label('total_payments'),
                func.sum(Payment.amount).label('total_amount'),
                func.count(Payment.id).filter(Payment.status == 'مدفوع').label('paid_count'),
                func.count(Payment.id).filter(Payment.status == 'معلق').label('pending_count'),
                func.count(Payment.id).filter(Payment.status == 'متأخر').label('overdue_count')
            ).filter(
                Payment.payment_date >= month_date,
                Payment.payment_date < next_month
            ).first()

            monthly_payment_analysis.append({
                'month': month_date.strftime('%Y-%m'),
                'month_name': month_date.strftime('%B'),
                'total_payments': payments_data.total_payments or 0,
                'total_amount': float(payments_data.total_amount or 0),
                'paid_count': payments_data.paid_count or 0,
                'pending_count': payments_data.pending_count or 0,
                'overdue_count': payments_data.overdue_count or 0,
                'success_rate': (payments_data.paid_count / payments_data.total_payments * 100) if payments_data.total_payments > 0 else 0
            })

        # 4. تحليل التوزيع الجغرافي للسائقين
        location_analysis = db.session.query(
            func.count(Driver.id).label('total_with_location')
        ).filter(
            Driver.current_latitude.isnot(None),
            Driver.current_longitude.isnot(None)
        ).first()

        # 5. تحليل أنماط الدفع
        payment_patterns = db.session.query(
            Payment.payment_method,
            func.count(Payment.id).label('usage_count'),
            func.sum(Payment.amount).label('total_amount'),
            func.avg(Payment.amount).label('avg_amount')
        ).group_by(Payment.payment_method).all()

        # 6. إحصائيات الأداء العامة
        performance_stats = {
            'total_drivers': Driver.query.count(),
            'active_drivers': Driver.query.filter_by(status='نشط').count(),
            'total_payments': Payment.query.count(),
            'total_revenue': db.session.query(func.sum(Payment.amount)).scalar() or 0,
            'avg_revenue_per_driver': 0,
            'payment_success_rate': 0,
            'location_coverage': 0
        }

        if performance_stats['total_drivers'] > 0:
            performance_stats['avg_revenue_per_driver'] = performance_stats['total_revenue'] / performance_stats['total_drivers']
            performance_stats['location_coverage'] = (location_analysis.total_with_location / performance_stats['total_drivers']) * 100

        paid_payments = Payment.query.filter_by(status='مدفوع').count()
        if performance_stats['total_payments'] > 0:
            performance_stats['payment_success_rate'] = (paid_payments / performance_stats['total_payments']) * 100

        return render_template('dashboard_analytics.html',
                             driver_trends=driver_trends,
                             vehicle_revenue=vehicle_revenue,
                             monthly_payment_analysis=monthly_payment_analysis,
                             payment_patterns=payment_patterns,
                             performance_stats=performance_stats)

    except Exception as e:
        flash(f'حدث خطأ في تحميل التحليلات: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول المحسنة مع الأمان"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        remember_me = 'remember_me' in request.form

        user = User.query.filter_by(username=username).first()

        if not user:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'error')
            return render_template('login.html')

        # التحقق من قفل الحساب
        if user.is_account_locked():
            flash('تم قفل حسابك مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة. حاول مرة أخرى لاحقاً.', 'error')
            return render_template('login.html')

        # التحقق من حالة الحساب
        if not user.is_active:
            flash('حسابك غير نشط. يرجى التواصل مع المدير.', 'error')
            return render_template('login.html')

        # التحقق من كلمة المرور
        if user.check_password(password):
            # نجح تسجيل الدخول
            user.reset_failed_attempts()
            user.last_login = datetime.utcnow()
            db.session.commit()

            login_user(user, remember=remember_me)

            # تسجيل النشاط
            ActivityLog.log_activity(
                user_id=user.id,
                action='تسجيل دخول ناجح',
                resource_type='auth',
                details=f'IP: {request.remote_addr}',
                request_obj=request
            )

            flash('تم تسجيل الدخول بنجاح!', 'success')

            # إعادة التوجيه للصفحة المطلوبة أو لوحة التحكم
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            # فشل تسجيل الدخول
            user.increment_failed_attempts()
            db.session.commit()

            # تسجيل النشاط
            ActivityLog.log_activity(
                user_id=user.id,
                action='محاولة تسجيل دخول فاشلة',
                resource_type='auth',
                details=f'IP: {request.remote_addr}',
                request_obj=request
            )

            if user.is_account_locked():
                flash('تم قفل حسابك مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة.', 'error')
            else:
                remaining_attempts = 5 - user.failed_login_attempts
                flash(f'اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: {remaining_attempts}', 'error')

    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    # تسجيل النشاط
    ActivityLog.log_activity(
        user_id=current_user.id,
        action='تسجيل خروج',
        resource_type='auth',
        details=f'IP: {request.remote_addr}',
        request_obj=request
    )

    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('login'))

# طلب إعادة تعيين كلمة المرور
@app.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """طلب إعادة تعيين كلمة المرور"""
    if request.method == 'POST':
        email = request.form['email']
        user = User.query.filter_by(email=email).first()

        if user:
            # إنشاء رمز إعادة التعيين
            import secrets
            token = secrets.token_urlsafe(32)
            user.password_reset_token = token
            user.password_reset_expires = datetime.utcnow() + timedelta(hours=1)
            db.session.commit()

            # في التطبيق الحقيقي، يتم إرسال الرمز عبر البريد الإلكتروني
            # هنا سنعرض الرمز للمستخدم مباشرة للاختبار
            flash(f'تم إنشاء رمز إعادة التعيين: {token}', 'info')
            return redirect(url_for('reset_password'))
        else:
            flash('البريد الإلكتروني غير موجود في النظام.', 'error')

    return render_template('forgot_password.html')

# إعادة تعيين كلمة المرور
@app.route('/reset-password', methods=['GET', 'POST'])
def reset_password():
    """إعادة تعيين كلمة المرور"""
    if request.method == 'POST':
        token = request.form['token']
        new_password = request.form['new_password']
        confirm_password = request.form['confirm_password']

        if new_password != confirm_password:
            flash('كلمة المرور وتأكيدها غير متطابقتان.', 'error')
            return render_template('reset_password.html')

        user = User.query.filter_by(password_reset_token=token).first()

        if not user or not user.password_reset_expires or user.password_reset_expires < datetime.utcnow():
            flash('رمز إعادة التعيين غير صحيح أو منتهي الصلاحية.', 'error')
            return render_template('reset_password.html')

        try:
            user.set_password(new_password)
            user.password_reset_token = None
            user.password_reset_expires = None
            user.unlock_account()  # إلغاء قفل الحساب إذا كان مقفلاً
            db.session.commit()

            # تسجيل النشاط
            ActivityLog.log_activity(
                user_id=user.id,
                action='إعادة تعيين كلمة المرور',
                resource_type='auth',
                details=f'IP: {request.remote_addr}',
                request_obj=request
            )

            flash('تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول.', 'success')
            return redirect(url_for('login'))
        except ValueError as e:
            flash(str(e), 'error')

    return render_template('reset_password.html')

# صفحة السائقين
@app.route('/drivers')
@login_required
def drivers_list():
    """قائمة السائقين"""
    drivers = Driver.query.all()
    return render_template('drivers.html', drivers=drivers)

# إضافة سائق جديد
@app.route('/drivers/add', methods=['POST'])
@login_required
def add_driver():
    """إضافة سائق جديد"""
    try:
        # استلام البيانات من النموذج
        name = request.form.get('full_name')
        phone = request.form.get('phone')
        national_id = request.form.get('national_id')
        license_number = request.form.get('license_number')
        vehicle_type = request.form.get('vehicle_type')
        vehicle_plate = request.form.get('vehicle_plate')
        payment_type = request.form.get('payment_type')
        payment_amount = float(request.form.get('payment_amount', 0))
        status = request.form.get('status', 'نشط')
        rating = request.form.get('rating')
        notes = request.form.get('notes')

        # التحقق من البيانات المطلوبة
        if not name or not phone or not payment_type:
            flash('الاسم ورقم الهاتف ونوع الدفع مطلوبة', 'error')
            return redirect(url_for('drivers_list'))

        # التحقق من عدم تكرار رقم الهاتف
        existing_driver = Driver.query.filter_by(phone=phone).first()
        if existing_driver:
            flash('رقم الهاتف مستخدم بالفعل', 'error')
            return redirect(url_for('drivers_list'))

        # إنشاء سائق جديد
        new_driver = Driver(
            name=name,
            phone=phone,
            national_id=national_id,
            license_number=license_number,
            vehicle_type=vehicle_type,
            vehicle_plate=vehicle_plate,
            payment_type=payment_type,
            payment_amount=payment_amount,
            status=status,
            rating=float(rating) if rating else None,
            notes=notes
        )

        # حساب تاريخ الدفع التالي
        if payment_type == 'شهري':
            new_driver.next_payment_due = datetime.utcnow() + timedelta(days=30)
        elif payment_type == 'أسبوعي':
            new_driver.next_payment_due = datetime.utcnow() + timedelta(days=7)

        db.session.add(new_driver)
        db.session.commit()

        flash(f'تم إضافة السائق {name} بنجاح', 'success')
        return redirect(url_for('drivers_list'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة السائق: {str(e)}', 'error')
        return redirect(url_for('drivers_list'))

# تحديث بيانات السائق
@app.route('/drivers/update/<int:driver_id>', methods=['POST'])
@login_required
def update_driver(driver_id):
    """تحديث بيانات السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)

        # تحديث البيانات
        driver.name = request.form.get('name', driver.name)
        driver.phone = request.form.get('phone', driver.phone)
        driver.national_id = request.form.get('national_id', driver.national_id)
        driver.license_number = request.form.get('license_number', driver.license_number)
        driver.vehicle_type = request.form.get('vehicle_type', driver.vehicle_type)
        driver.vehicle_plate = request.form.get('vehicle_plate', driver.vehicle_plate)
        driver.payment_type = request.form.get('payment_type', driver.payment_type)
        driver.payment_amount = float(request.form.get('payment_amount', driver.payment_amount))
        driver.status = request.form.get('status', driver.status)
        driver.notes = request.form.get('notes', driver.notes)
        driver.updated_at = datetime.utcnow()

        db.session.commit()
        flash(f'تم تحديث بيانات السائق {driver.name} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث البيانات: {str(e)}', 'error')

    return redirect(url_for('drivers_list'))

# حذف السائق
@app.route('/drivers/delete/<int:driver_id>', methods=['POST'])
@login_required
def delete_driver(driver_id):
    """حذف السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)
        driver_name = driver.name

        # حذف المدفوعات المرتبطة بالسائق
        Payment.query.filter_by(driver_id=driver_id).delete()

        # حذف السائق
        db.session.delete(driver)
        db.session.commit()

        flash(f'تم حذف السائق {driver_name} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف السائق: {str(e)}', 'error')

    return redirect(url_for('drivers_list'))

# تحديث موقع السائق
@app.route('/drivers/update-location/<int:driver_id>', methods=['POST'])
@login_required
def update_driver_location(driver_id):
    """تحديث موقع السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)

        latitude = request.form.get('latitude')
        longitude = request.form.get('longitude')
        address = request.form.get('address')

        if latitude and longitude:
            driver.current_latitude = float(latitude)
            driver.current_longitude = float(longitude)
            driver.current_address = address
            driver.updated_at = datetime.utcnow()

            db.session.commit()
            flash(f'تم تحديث موقع السائق {driver.name} بنجاح', 'success')
        else:
            flash('الإحداثيات مطلوبة', 'error')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث الموقع: {str(e)}', 'error')

    return redirect(url_for('drivers_list'))

# صفحة المدفوعات
@app.route('/payments')
@login_required
def payments_list():
    """قائمة المدفوعات"""
    payments = Payment.query.order_by(Payment.payment_date.desc()).all()
    drivers = Driver.query.all()

    # حساب الإحصائيات
    total_payments = sum(p.amount for p in payments)
    monthly_payments = sum(p.amount for p in payments if p.payment_date and p.payment_date.month == datetime.now().month)
    pending_payments = Driver.query.filter(Driver.next_payment_due < datetime.now()).count()

    return render_template('payments.html',
                         payments=payments,
                         drivers=drivers,
                         total_payments=total_payments,
                         monthly_payments=monthly_payments,
                         pending_payments=pending_payments)

# إضافة دفعة جديدة
@app.route('/payments/add', methods=['POST'])
@login_required
def add_payment():
    """إضافة دفعة جديدة"""
    try:
        driver_id = int(request.form.get('driver_id'))
        amount = float(request.form.get('amount'))
        payment_method = request.form.get('payment_method', 'نقدي')
        notes = request.form.get('notes', '')

        # التحقق من وجود السائق
        driver = Driver.query.get_or_404(driver_id)

        # إنشاء دفعة جديدة
        new_payment = Payment(
            driver_id=driver_id,
            amount=amount,
            payment_method=payment_method,
            notes=notes,
            payment_date=datetime.now()
        )

        # تحديث آخر دفعة للسائق
        driver.last_payment_date = datetime.now()

        # حساب تاريخ الدفع التالي
        if driver.payment_type == 'شهري':
            driver.next_payment_due = datetime.now() + timedelta(days=30)
        elif driver.payment_type == 'أسبوعي':
            driver.next_payment_due = datetime.now() + timedelta(days=7)

        # تحديث حالة الدفع
        if driver.next_payment_due > datetime.now():
            driver.payment_status = 'ملتزم'

        db.session.add(new_payment)
        db.session.commit()

        flash(f'تم تسجيل دفعة بمبلغ {amount} ريال للسائق {driver.name}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تسجيل الدفعة: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# تعديل دفعة
@app.route('/payments/edit/<int:payment_id>', methods=['POST'])
@login_required
def edit_payment(payment_id):
    """تعديل دفعة موجودة"""
    try:
        payment = Payment.query.get_or_404(payment_id)

        # تحديث البيانات
        payment.amount = float(request.form.get('amount'))
        payment.payment_method = request.form.get('payment_method')
        payment.reference_number = request.form.get('reference_number', '')
        payment.notes = request.form.get('notes', '')
        payment.status = request.form.get('status', 'مكتمل')

        # إذا تم تغيير التاريخ
        new_date = request.form.get('payment_date')
        if new_date:
            payment.payment_date = datetime.strptime(new_date, '%Y-%m-%d')

        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='تعديل دفعة',
            resource_type='payment',
            resource_id=payment_id,
            details=f'تعديل دفعة بمبلغ {payment.amount} ريال للسائق {payment.driver.name}',
            request_obj=request
        )

        flash(f'تم تعديل الدفعة بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تعديل الدفعة: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# حذف دفعة
@app.route('/payments/delete/<int:payment_id>', methods=['POST'])
@login_required
def delete_payment(payment_id):
    """حذف دفعة"""
    try:
        payment = Payment.query.get_or_404(payment_id)
        driver_name = payment.driver.name if payment.driver else 'غير معروف'
        amount = payment.amount

        db.session.delete(payment)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='حذف دفعة',
            resource_type='payment',
            resource_id=payment_id,
            details=f'حذف دفعة بمبلغ {amount} ريال للسائق {driver_name}',
            request_obj=request
        )

        flash(f'تم حذف دفعة بمبلغ {amount} ريال للسائق {driver_name}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الدفعة: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# عمليات مجمعة للمدفوعات
@app.route('/payments/bulk-action', methods=['POST'])
@login_required
def bulk_payment_action():
    """تنفيذ عمليات مجمعة على المدفوعات"""
    try:
        action = request.form.get('bulk_action')
        payment_ids = request.form.getlist('payment_ids')

        if not payment_ids:
            flash('يرجى اختيار دفعة واحدة على الأقل', 'warning')
            return redirect(url_for('payments_list'))

        payment_ids = [int(pid) for pid in payment_ids]
        payments = Payment.query.filter(Payment.id.in_(payment_ids)).all()

        if action == 'delete':
            count = len(payments)
            for payment in payments:
                db.session.delete(payment)

            db.session.commit()
            flash(f'تم حذف {count} دفعة بنجاح', 'success')

        elif action == 'mark_completed':
            count = 0
            for payment in payments:
                if payment.status != 'مكتمل':
                    payment.status = 'مكتمل'
                    count += 1

            db.session.commit()
            flash(f'تم تحديث حالة {count} دفعة إلى مكتمل', 'success')

        elif action == 'mark_pending':
            count = 0
            for payment in payments:
                if payment.status != 'معلق':
                    payment.status = 'معلق'
                    count += 1

            db.session.commit()
            flash(f'تم تحديث حالة {count} دفعة إلى معلق', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تنفيذ العملية: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# تحديث حالات الدفع للسائقين
@app.route('/payments/update-status', methods=['POST'])
@login_required
def update_payment_status():
    """تحديث حالات الدفع للسائقين"""
    try:
        current_date = datetime.now()
        updated_count = 0

        # البحث عن السائقين المتأخرين
        overdue_drivers = Driver.query.filter(Driver.next_payment_due < current_date).all()

        for driver in overdue_drivers:
            days_overdue = (current_date - driver.next_payment_due).days

            if days_overdue > 7:
                driver.payment_status = 'متأخر'
            elif days_overdue > 3:
                driver.payment_status = 'متوسط'
            else:
                driver.payment_status = 'ملتزم'

            updated_count += 1

        db.session.commit()
        flash(f'تم تحديث حالة {updated_count} سائق', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث الحالات: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# تحليلات المدفوعات المتقدمة
@app.route('/payments/analytics')
@login_required
def payment_analytics():
    """صفحة تحليلات المدفوعات المتقدمة"""
    try:
        # إحصائيات أساسية
        total_payments = Payment.query.count()
        total_amount = db.session.query(db.func.sum(Payment.amount)).scalar() or 0
        avg_payment = total_amount / total_payments if total_payments > 0 else 0

        # المدفوعات حسب الطريقة
        payment_methods = db.session.query(
            Payment.payment_method,
            db.func.count(Payment.id).label('count'),
            db.func.sum(Payment.amount).label('total')
        ).group_by(Payment.payment_method).all()

        # المدفوعات حسب الحالة
        payment_statuses = db.session.query(
            Payment.status,
            db.func.count(Payment.id).label('count'),
            db.func.sum(Payment.amount).label('total')
        ).group_by(Payment.status).all()

        # أفضل 10 سائقين من ناحية المدفوعات
        top_drivers = db.session.query(
            Driver.name,
            db.func.count(Payment.id).label('payment_count'),
            db.func.sum(Payment.amount).label('total_paid')
        ).join(Payment).group_by(Driver.id, Driver.name)\
         .order_by(db.func.sum(Payment.amount).desc()).limit(10).all()

        # المدفوعات الشهرية للعام الحالي
        current_year = datetime.now().year
        monthly_payments = []
        for month in range(1, 13):
            month_total = db.session.query(db.func.sum(Payment.amount))\
                .filter(db.extract('year', Payment.payment_date) == current_year)\
                .filter(db.extract('month', Payment.payment_date) == month)\
                .scalar() or 0
            monthly_payments.append({
                'month': month,
                'month_name': ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                              'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'][month],
                'total': float(month_total)
            })

        # السائقين المتأخرين
        overdue_drivers = Driver.query.filter(
            Driver.next_payment_due < datetime.now()
        ).all()

        overdue_analysis = []
        for driver in overdue_drivers:
            days_overdue = (datetime.now() - driver.next_payment_due).days
            outstanding = driver.calculate_outstanding_amount()
            overdue_analysis.append({
                'driver': driver,
                'days_overdue': days_overdue,
                'outstanding_amount': outstanding
            })

        return render_template('payments_analytics.html',
                             total_payments=total_payments,
                             total_amount=total_amount,
                             avg_payment=avg_payment,
                             payment_methods=payment_methods,
                             payment_statuses=payment_statuses,
                             top_drivers=top_drivers,
                             monthly_payments=monthly_payments,
                             overdue_analysis=overdue_analysis)

    except Exception as e:
        flash(f'حدث خطأ في تحميل التحليلات: {str(e)}', 'error')
        return redirect(url_for('payments_list'))

# تذكيرات المدفوعات
@app.route('/payments/reminders')
@login_required
def payment_reminders():
    """صفحة تذكيرات المدفوعات"""
    try:
        # السائقين المتأخرين
        overdue_drivers = Driver.query.filter(
            Driver.next_payment_due < datetime.now()
        ).all()

        # السائقين المستحقين خلال الأسبوع القادم
        next_week = datetime.now() + timedelta(days=7)
        upcoming_drivers = Driver.query.filter(
            Driver.next_payment_due.between(datetime.now(), next_week)
        ).all()

        # إحصائيات التذكيرات
        total_overdue = len(overdue_drivers)
        total_upcoming = len(upcoming_drivers)
        total_overdue_amount = sum(driver.calculate_outstanding_amount() for driver in overdue_drivers)

        return render_template('payment_reminders.html',
                             overdue_drivers=overdue_drivers,
                             upcoming_drivers=upcoming_drivers,
                             total_overdue=total_overdue,
                             total_upcoming=total_upcoming,
                             total_overdue_amount=total_overdue_amount)

    except Exception as e:
        flash(f'حدث خطأ في تحميل التذكيرات: {str(e)}', 'error')
        return redirect(url_for('payments_list'))

# إرسال تذكير دفع
@app.route('/payments/send-reminder/<int:driver_id>', methods=['POST'])
@login_required
def send_payment_reminder(driver_id):
    """إرسال تذكير دفع للسائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)
        outstanding = driver.calculate_outstanding_amount()

        # هنا يمكن إضافة كود إرسال الرسالة (واتساب، SMS، إلخ)
        # مؤقتاً سنسجل النشاط فقط

        ActivityLog.log_activity(
            user_id=current_user.id,
            action='إرسال تذكير دفع',
            resource_type='driver',
            resource_id=driver_id,
            details=f'تم إرسال تذكير دفع للسائق {driver.name} بمبلغ {outstanding} ريال',
            request_obj=request
        )

        flash(f'تم إرسال تذكير الدفع للسائق {driver.name}', 'success')
        return redirect(url_for('payment_reminders'))

    except Exception as e:
        flash(f'حدث خطأ في إرسال التذكير: {str(e)}', 'error')
        return redirect(url_for('payment_reminders'))

# صفحة الخرائط والمواقع
@app.route('/maps')
@login_required
def maps_view():
    """صفحة عرض مواقع السائقين على الخريطة"""
    try:
        # جلب جميع السائقين مع مواقعهم
        drivers = Driver.query.filter(
            Driver.current_latitude.isnot(None),
            Driver.current_longitude.isnot(None)
        ).all()

        # إحصائيات المواقع
        total_drivers = Driver.query.count()
        drivers_with_location = len(drivers)
        drivers_without_location = total_drivers - drivers_with_location

        # تحويل بيانات السائقين لتنسيق JSON للخريطة
        drivers_data = []
        for driver in drivers:
            drivers_data.append({
                'id': driver.id,
                'name': driver.name,
                'phone': driver.phone,
                'latitude': float(driver.current_latitude),
                'longitude': float(driver.current_longitude),
                'status': driver.status,
                'vehicle_type': driver.vehicle_type,
                'location_updated': driver.updated_at.strftime('%Y-%m-%d %H:%M') if driver.updated_at else 'غير محدد'
            })

        return render_template('maps.html',
                             drivers=drivers,
                             drivers_data=drivers_data,
                             total_drivers=total_drivers,
                             drivers_with_location=drivers_with_location,
                             drivers_without_location=drivers_without_location)

    except Exception as e:
        flash(f'حدث خطأ في تحميل الخريطة: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# تحديث موقع السائق من الخرائط
@app.route('/maps/update-location/<int:driver_id>', methods=['POST'])
@login_required
def update_driver_location_maps(driver_id):
    """تحديث موقع السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)

        latitude = request.form.get('latitude')
        longitude = request.form.get('longitude')
        address = request.form.get('address', '')

        if not latitude or not longitude:
            flash('يرجى إدخال الإحداثيات', 'error')
            return redirect(url_for('maps_view'))

        # تحديث الموقع
        driver.current_latitude = float(latitude)
        driver.current_longitude = float(longitude)
        driver.current_address = address
        driver.updated_at = datetime.now()

        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='تحديث موقع السائق',
            resource_type='driver',
            resource_id=driver_id,
            details=f'تم تحديث موقع السائق {driver.name} إلى ({latitude}, {longitude})',
            request_obj=request
        )

        flash(f'تم تحديث موقع السائق {driver.name} بنجاح', 'success')
        return redirect(url_for('maps_view'))

    except Exception as e:
        flash(f'حدث خطأ في تحديث الموقع: {str(e)}', 'error')
        return redirect(url_for('maps_view'))

# حذف موقع السائق من الخرائط
@app.route('/maps/remove-location/<int:driver_id>', methods=['POST'])
@login_required
def remove_driver_location_maps(driver_id):
    """حذف موقع السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)

        driver.current_latitude = None
        driver.current_longitude = None
        driver.current_address = None
        driver.updated_at = datetime.now()

        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='حذف موقع السائق',
            resource_type='driver',
            resource_id=driver_id,
            details=f'تم حذف موقع السائق {driver.name}',
            request_obj=request
        )

        flash(f'تم حذف موقع السائق {driver.name}', 'success')
        return redirect(url_for('maps_view'))

    except Exception as e:
        flash(f'حدث خطأ في حذف الموقع: {str(e)}', 'error')
        return redirect(url_for('maps_view'))

# صفحة التقارير
@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير والإحصائيات"""
    # إحصائيات عامة
    total_drivers = Driver.query.count()
    active_drivers = Driver.query.filter_by(status='نشط').count()
    inactive_drivers = Driver.query.filter_by(status='غير نشط').count()

    # إحصائيات المدفوعات
    total_payments = Payment.query.count()
    total_amount = db.session.query(db.func.sum(Payment.amount)).scalar() or 0

    # إحصائيات شهرية
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_payments = Payment.query.filter(
        db.extract('month', Payment.payment_date) == current_month,
        db.extract('year', Payment.payment_date) == current_year
    ).count()
    monthly_amount = db.session.query(db.func.sum(Payment.amount)).filter(
        db.extract('month', Payment.payment_date) == current_month,
        db.extract('year', Payment.payment_date) == current_year
    ).scalar() or 0

    # السائقين المتأخرين
    overdue_drivers = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()

    # أفضل السائقين (حسب عدد المدفوعات)
    top_drivers = db.session.query(
        Driver.name,
        db.func.count(Payment.id).label('payment_count'),
        db.func.sum(Payment.amount).label('total_amount')
    ).join(Payment).group_by(Driver.id).order_by(db.desc('payment_count')).limit(5).all()

    # إحصائيات طرق الدفع
    payment_methods = db.session.query(
        Payment.payment_method,
        db.func.count(Payment.id).label('count'),
        db.func.sum(Payment.amount).label('total')
    ).group_by(Payment.payment_method).all()

    # إحصائيات شهرية للرسم البياني (آخر 6 أشهر)
    monthly_stats = []
    for i in range(6):
        month_date = datetime.now() - timedelta(days=30*i)
        month_payments = Payment.query.filter(
            db.extract('month', Payment.payment_date) == month_date.month,
            db.extract('year', Payment.payment_date) == month_date.year
        ).count()
        month_amount = db.session.query(db.func.sum(Payment.amount)).filter(
            db.extract('month', Payment.payment_date) == month_date.month,
            db.extract('year', Payment.payment_date) == month_date.year
        ).scalar() or 0

        monthly_stats.append({
            'month': month_date.strftime('%Y-%m'),
            'month_name': month_date.strftime('%B %Y'),
            'payments': month_payments,
            'amount': float(month_amount)
        })

    monthly_stats.reverse()  # ترتيب من الأقدم للأحدث

    return render_template('reports.html',
                         total_drivers=total_drivers,
                         active_drivers=active_drivers,
                         inactive_drivers=inactive_drivers,
                         total_payments=total_payments,
                         total_amount=total_amount,
                         monthly_payments=monthly_payments,
                         monthly_amount=monthly_amount,
                         overdue_drivers=overdue_drivers,
                         top_drivers=top_drivers,
                         payment_methods=payment_methods,
                         monthly_stats=monthly_stats)

# تقارير فرعية
@app.route('/reports/drivers')
@login_required
def drivers_report():
    """تقرير السائقين المفصل"""
    drivers = Driver.query.all()

    # إحصائيات السائقين
    active_drivers = Driver.query.filter_by(status='نشط').count()
    inactive_drivers = Driver.query.filter_by(status='غير نشط').count()
    suspended_drivers = Driver.query.filter_by(status='معلق').count()

    # إحصائيات المركبات
    vehicle_types = db.session.query(Driver.vehicle_type, db.func.count(Driver.id)).group_by(Driver.vehicle_type).all()

    return render_template('reports/drivers_report.html',
                         drivers=drivers,
                         active_drivers=active_drivers,
                         inactive_drivers=inactive_drivers,
                         suspended_drivers=suspended_drivers,
                         vehicle_types=vehicle_types)

@app.route('/reports/payments')
@login_required
def payments_report():
    """تقرير المدفوعات المفصل"""
    payments = Payment.query.all()

    # إحصائيات المدفوعات
    total_payments = db.session.query(db.func.sum(Payment.amount)).scalar() or 0
    pending_payments = Payment.query.filter_by(status='معلق').count()
    completed_payments = Payment.query.filter_by(status='مكتمل').count()

    return render_template('reports/payments_report.html',
                         payments=payments,
                         total_payments=total_payments,
                         pending_payments=pending_payments,
                         completed_payments=completed_payments)

@app.route('/reports/financial')
@login_required
def financial_report():
    """التقرير المالي المفصل"""
    # إحصائيات مالية
    total_revenue = db.session.query(db.func.sum(Payment.amount)).filter_by(status='مكتمل').scalar() or 0
    pending_revenue = db.session.query(db.func.sum(Payment.amount)).filter_by(status='معلق').scalar() or 0

    # المدفوعات الشهرية - استخدام طريقة Python بدلاً من SQL
    all_payments = Payment.query.filter_by(status='مكتمل').all()
    monthly_data = {}

    for payment in all_payments:
        month_key = payment.payment_date.strftime('%Y-%m')
        if month_key not in monthly_data:
            monthly_data[month_key] = 0
        monthly_data[month_key] += payment.amount

    # تحويل إلى قائمة مرتبة
    monthly_payments = []
    for month, total in sorted(monthly_data.items()):
        monthly_payments.append(type('obj', (object,), {'month': month, 'total': total}))

    return render_template('reports/financial_report.html',
                         total_revenue=total_revenue,
                         pending_revenue=pending_revenue,
                         monthly_payments=monthly_payments)

@app.route('/reports/performance')
@login_required
def performance_report():
    """تقرير الأداء"""
    # أفضل السائقين
    top_drivers = Driver.query.filter(Driver.rating.isnot(None)).order_by(Driver.rating.desc()).limit(10).all()

    # إحصائيات الأداء
    avg_rating = db.session.query(db.func.avg(Driver.rating)).scalar() or 0

    return render_template('reports/performance_report.html',
                         top_drivers=top_drivers,
                         avg_rating=round(avg_rating, 2))

@app.route('/reports/custom', methods=['GET', 'POST'])
@login_required
def custom_report():
    """تقرير مخصص"""
    if request.method == 'POST':
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        report_type = request.form.get('report_type')
        status = request.form.get('status')

        # بناء الاستعلام حسب المعايير
        if report_type == 'drivers':
            query = Driver.query
            if status:
                query = query.filter_by(status=status)
            results = query.all()
        else:
            results = []

        return render_template('reports/custom_report.html',
                             results=results,
                             report_type=report_type,
                             start_date=start_date,
                             end_date=end_date)

    return render_template('reports/custom_report.html')

# تصدير التقارير
@app.route('/reports/export/<report_type>')
@login_required
def export_report(report_type):
    """تصدير التقارير بصيغ مختلفة"""
    try:
        if report_type == 'drivers':
            # تصدير قائمة السائقين
            drivers = Driver.query.all()
            # هنا يمكن إضافة كود تصدير CSV أو Excel
            flash('سيتم تطوير ميزة التصدير قريباً', 'info')

        elif report_type == 'payments':
            # تصدير المدفوعات
            payments = Payment.query.all()
            flash('سيتم تطوير ميزة التصدير قريباً', 'info')

        elif report_type == 'overdue':
            # تصدير المتأخرين
            overdue = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()
            flash('سيتم تطوير ميزة التصدير قريباً', 'info')

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')

    return redirect(url_for('reports'))

# صفحة الخريطة
@app.route('/map')
@login_required
def map_view():
    """صفحة الخريطة مع مواقع السائقين"""
    # جلب السائقين الذين لديهم مواقع
    drivers_with_location = Driver.query.filter(
        Driver.current_latitude.isnot(None),
        Driver.current_longitude.isnot(None)
    ).all()

    # جلب جميع السائقين للإحصائيات
    all_drivers = Driver.query.all()
    active_drivers = Driver.query.filter_by(status='نشط').all()

    # إحصائيات المواقع
    drivers_with_gps = len(drivers_with_location)
    drivers_without_gps = len(all_drivers) - drivers_with_gps

    return render_template('map.html',
                         drivers=drivers_with_location,
                         all_drivers=all_drivers,
                         active_drivers=active_drivers,
                         drivers_with_gps=drivers_with_gps,
                         drivers_without_gps=drivers_without_gps)

# إضافة موقع جديد
@app.route('/map/add-location', methods=['POST'])
@login_required
def add_location():
    """إضافة موقع جديد للسائق"""
    try:
        driver_id = int(request.form.get('driver_id'))
        latitude = float(request.form.get('latitude'))
        longitude = float(request.form.get('longitude'))
        address = request.form.get('address', '')

        driver = Driver.query.get_or_404(driver_id)

        # تحديث موقع السائق
        driver.current_latitude = latitude
        driver.current_longitude = longitude
        driver.current_address = address
        driver.updated_at = datetime.now()

        db.session.commit()

        flash(f'تم إضافة موقع جديد للسائق {driver.name}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة الموقع: {str(e)}', 'error')

    return redirect(url_for('map_view'))

# البحث عن مواقع قريبة
@app.route('/map/nearby', methods=['POST'])
@login_required
def find_nearby_drivers():
    """البحث عن السائقين القريبين من موقع معين"""
    try:
        center_lat = float(request.form.get('latitude'))
        center_lng = float(request.form.get('longitude'))
        radius = float(request.form.get('radius', 5))  # نصف القطر بالكيلومتر

        # استعلام للبحث عن السائقين القريبين
        nearby_drivers = Driver.query.filter(
            Driver.current_latitude.isnot(None),
            Driver.current_longitude.isnot(None),
            Driver.status == 'نشط'
        ).all()

        # فلترة السائقين حسب المسافة (حساب مبسط)
        filtered_drivers = []
        for driver in nearby_drivers:
            if driver.current_latitude and driver.current_longitude:
                # حساب المسافة التقريبية
                lat_diff = abs(driver.current_latitude - center_lat)
                lng_diff = abs(driver.current_longitude - center_lng)
                distance = ((lat_diff ** 2) + (lng_diff ** 2)) ** 0.5

                # تحويل تقريبي للكيلومترات (1 درجة ≈ 111 كم)
                distance_km = distance * 111

                if distance_km <= radius:
                    filtered_drivers.append({
                        'id': driver.id,
                        'name': driver.name,
                        'phone': driver.phone,
                        'latitude': driver.current_latitude,
                        'longitude': driver.current_longitude,
                        'distance': round(distance_km, 2)
                    })

        return render_template('map.html',
                             drivers=Driver.query.filter(
                                 Driver.current_latitude.isnot(None),
                                 Driver.current_longitude.isnot(None)
                             ).all(),
                             nearby_drivers=filtered_drivers,
                             search_center={'lat': center_lat, 'lng': center_lng, 'radius': radius})

    except Exception as e:
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'error')
        return redirect(url_for('map_view'))

# صفحة الإشعارات
@app.route('/notifications')
@login_required
def notifications():
    """صفحة إدارة الإشعارات"""
    # جلب السائقين المتأخرين في الدفع
    overdue_drivers = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()

    # جلب السائقين الذين يستحقون دفعة قريباً (خلال 3 أيام)
    upcoming_due = Driver.query.filter(
        Driver.next_payment_due > datetime.now(),
        Driver.next_payment_due <= datetime.now() + timedelta(days=3)
    ).all()

    # إحصائيات الإشعارات
    total_overdue = len(overdue_drivers)
    total_upcoming = len(upcoming_due)

    return render_template('notifications.html',
                         overdue_drivers=overdue_drivers,
                         upcoming_due=upcoming_due,
                         total_overdue=total_overdue,
                         total_upcoming=total_upcoming)

# إرسال إشعار WhatsApp
@app.route('/notifications/whatsapp', methods=['POST'])
@login_required
def send_whatsapp_notification():
    """إرسال إشعار عبر WhatsApp"""
    try:
        driver_id = request.form.get('driver_id')
        message = request.form.get('message')

        driver = Driver.query.get_or_404(driver_id)

        # هنا يمكن إضافة تكامل مع WhatsApp Business API
        # مثال: استخدام Twilio أو WhatsApp Business API

        # محاكاة إرسال الرسالة
        whatsapp_url = f"https://wa.me/{driver.phone}?text={message}"

        flash(f'تم إنشاء رابط WhatsApp للسائق {driver.name}', 'success')

        # يمكن إضافة سجل للإشعارات المرسلة هنا

    except Exception as e:
        flash(f'حدث خطأ أثناء إرسال الإشعار: {str(e)}', 'error')

    return redirect(url_for('notifications'))

# إرسال إشعار SMS
@app.route('/notifications/sms', methods=['POST'])
@login_required
def send_sms_notification():
    """إرسال إشعار عبر SMS"""
    try:
        driver_id = request.form.get('driver_id')
        message = request.form.get('message')

        driver = Driver.query.get_or_404(driver_id)

        # هنا يمكن إضافة تكامل مع خدمة SMS
        # مثال: استخدام Twilio أو خدمة SMS محلية

        flash(f'سيتم تطوير إرسال SMS للسائق {driver.name} قريباً', 'info')

    except Exception as e:
        flash(f'حدث خطأ أثناء إرسال SMS: {str(e)}', 'error')

    return redirect(url_for('notifications'))

# إرسال إشعارات جماعية
@app.route('/notifications/bulk', methods=['POST'])
@login_required
def send_bulk_notifications():
    """إرسال إشعارات جماعية للسائقين المتأخرين"""
    try:
        notification_type = request.form.get('type')  # whatsapp, sms, email
        message_template = request.form.get('message')

        overdue_drivers = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()

        sent_count = 0
        for driver in overdue_drivers:
            if driver.phone:
                # تخصيص الرسالة لكل سائق
                personalized_message = message_template.replace('{name}', driver.name)
                personalized_message = personalized_message.replace('{amount}', str(driver.payment_amount))

                # هنا يتم إرسال الإشعار حسب النوع المحدد
                if notification_type == 'whatsapp':
                    # إرسال WhatsApp
                    pass
                elif notification_type == 'sms':
                    # إرسال SMS
                    pass

                sent_count += 1

        flash(f'تم إرسال {sent_count} إشعار بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء الإرسال الجماعي: {str(e)}', 'error')

    return redirect(url_for('notifications'))

# صفحة الإعدادات
@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات الرئيسية"""
    # جلب الإعدادات حسب الفئة
    general_settings = Settings.query.filter_by(category='general').all()
    notification_settings = Settings.query.filter_by(category='notifications').all()
    payment_settings = Settings.query.filter_by(category='payments').all()

    # إنشاء الإعدادات الافتراضية إذا لم تكن موجودة
    default_settings = [
        # الإعدادات العامة
        ('company_name', 'شركة النقل المتطورة', 'general', 'اسم الشركة', 'string'),
        ('company_logo', '', 'general', 'شعار الشركة', 'string'),
        ('currency', 'ريال سعودي', 'general', 'العملة المستخدمة', 'string'),
        ('timezone', 'Asia/Riyadh', 'general', 'المنطقة الزمنية', 'string'),
        ('language', 'ar', 'general', 'لغة النظام', 'string'),

        # إعدادات الإشعارات
        ('whatsapp_enabled', 'true', 'notifications', 'تفعيل إشعارات WhatsApp', 'boolean'),
        ('sms_enabled', 'false', 'notifications', 'تفعيل إشعارات SMS', 'boolean'),
        ('email_enabled', 'false', 'notifications', 'تفعيل إشعارات البريد الإلكتروني', 'boolean'),
        ('notification_reminder_days', '3', 'notifications', 'أيام التذكير قبل الاستحقاق', 'integer'),

        # إعدادات المدفوعات
        ('default_payment_type', 'شهري', 'payments', 'نوع الدفع الافتراضي', 'string'),
        ('late_fee_enabled', 'false', 'payments', 'تفعيل رسوم التأخير', 'boolean'),
        ('late_fee_amount', '50', 'payments', 'مبلغ رسوم التأخير', 'float'),
        ('grace_period_days', '7', 'payments', 'فترة السماح بالأيام', 'integer'),
    ]

    for key, value, category, description, data_type in default_settings:
        if not Settings.query.filter_by(key=key).first():
            Settings.set_setting(key, value, category, description, data_type)

    # إعادة جلب الإعدادات بعد إنشاء الافتراضية
    general_settings = Settings.query.filter_by(category='general').all()
    notification_settings = Settings.query.filter_by(category='notifications').all()
    payment_settings = Settings.query.filter_by(category='payments').all()

    return render_template('settings.html',
                         general_settings=general_settings,
                         notification_settings=notification_settings,
                         payment_settings=payment_settings)

# تحديث الإعدادات
@app.route('/settings/update', methods=['POST'])
@login_required
def update_settings():
    """تحديث الإعدادات"""
    try:
        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='update_settings',
            resource_type='settings',
            details={'updated_by': current_user.username},
            request_obj=request
        )

        # تحديث كل إعداد
        for key, value in request.form.items():
            if key.startswith('setting_'):
                setting_key = key.replace('setting_', '')
                setting = Settings.query.filter_by(key=setting_key).first()
                if setting:
                    setting.set_value(value)
                    setting.updated_at = datetime.utcnow()

        db.session.commit()
        flash('تم تحديث الإعدادات بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث الإعدادات: {str(e)}', 'error')

    return redirect(url_for('settings'))

# إدارة المستخدمين
@app.route('/settings/users')
@login_required
def manage_users():
    """إدارة المستخدمين والصلاحيات"""
    if not current_user.has_permission('admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    users = User.query.all()
    roles = Role.query.all()

    # إنشاء الأدوار الافتراضية إذا لم تكن موجودة
    default_roles = [
        ('admin', 'مدير النظام', 'صلاحيات كاملة لإدارة النظام', ['create', 'read', 'update', 'delete', 'admin', 'backup', 'settings']),
        ('supervisor', 'مشرف', 'إدارة السائقين والمدفوعات', ['create', 'read', 'update', 'reports']),
        ('viewer', 'مشاهد', 'عرض البيانات فقط', ['read'])
    ]

    for name, display_name, description, permissions in default_roles:
        if not Role.query.filter_by(name=name).first():
            role = Role(name=name, display_name=display_name, description=description, is_system=True)
            role.set_permissions(permissions)
            db.session.add(role)

    db.session.commit()

    # إعادة جلب الأدوار
    roles = Role.query.all()

    return render_template('settings/users.html', users=users, roles=roles)

# إضافة مستخدم جديد
@app.route('/add_user', methods=['POST'])
@login_required
def add_user():
    """إضافة مستخدم جديد"""
    try:
        username = request.form.get('username')
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role_id = request.form.get('role_id')
        is_active = 'is_active' in request.form

        # التحقق من صحة البيانات
        if not username or not password:
            flash('اسم المستخدم وكلمة المرور مطلوبان', 'danger')
            return redirect(url_for('manage_users'))

        if password != confirm_password:
            flash('كلمة المرور وتأكيدها غير متطابقتان', 'danger')
            return redirect(url_for('manage_users'))

        # التحقق من عدم وجود المستخدم
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return redirect(url_for('manage_users'))

        # إنشاء المستخدم الجديد
        new_user = User(
            username=username,
            full_name=full_name,
            email=email,
            is_active=is_active,
            role_id=int(role_id) if role_id else None,
            created_at=datetime.utcnow()
        )
        new_user.set_password(password)

        db.session.add(new_user)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='إضافة مستخدم جديد',
            resource_type='user',
            resource_id=new_user.id,
            details=f'اسم المستخدم: {username}',
            request_obj=request
        )

        flash(f'تم إضافة المستخدم {username} بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في إضافة المستخدم: {str(e)}', 'danger')

    return redirect(url_for('manage_users'))

# إضافة دور جديد
@app.route('/add_role', methods=['POST'])
@login_required
def add_role():
    """إضافة دور جديد"""
    try:
        name = request.form.get('name')
        description = request.form.get('description')
        permissions = request.form.getlist('permissions')

        if not name:
            flash('اسم الدور مطلوب', 'danger')
            return redirect(url_for('manage_users'))

        # التحقق من عدم وجود الدور
        if Role.query.filter_by(name=name).first():
            flash('اسم الدور موجود بالفعل', 'danger')
            return redirect(url_for('manage_users'))

        # إنشاء الدور الجديد
        new_role = Role(
            name=name,
            description=description,
            permissions=json.dumps(permissions) if permissions else None,
            created_at=datetime.utcnow()
        )

        db.session.add(new_role)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='إضافة دور جديد',
            resource_type='role',
            resource_id=new_role.id,
            details=f'اسم الدور: {name}',
            request_obj=request
        )

        flash(f'تم إضافة الدور {name} بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في إضافة الدور: {str(e)}', 'danger')

    return redirect(url_for('manage_users'))

# تغيير حالة المستخدم
@app.route('/toggle_user_status/<int:user_id>', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    """تغيير حالة المستخدم (نشط/غير نشط)"""
    try:
        user = User.query.get_or_404(user_id)

        if user.id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك تغيير حالة حسابك الخاص'})

        user.is_active = not user.is_active
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action=f'تغيير حالة المستخدم إلى {"نشط" if user.is_active else "غير نشط"}',
            resource_type='user',
            resource_id=user.id,
            details=f'المستخدم: {user.username}',
            request_obj=request
        )

        return jsonify({'success': True, 'message': 'تم تغيير حالة المستخدم بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# حذف مستخدم
@app.route('/delete_user/<int:user_id>', methods=['DELETE'])
@login_required
def delete_user(user_id):
    """حذف مستخدم"""
    try:
        user = User.query.get_or_404(user_id)

        if user.id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك حذف حسابك الخاص'})

        username = user.username
        db.session.delete(user)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='حذف مستخدم',
            resource_type='user',
            details=f'المستخدم المحذوف: {username}',
            request_obj=request
        )

        return jsonify({'success': True, 'message': f'تم حذف المستخدم {username} بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# حذف دور
@app.route('/delete_role/<int:role_id>', methods=['DELETE'])
@login_required
def delete_role(role_id):
    """حذف دور"""
    try:
        role = Role.query.get_or_404(role_id)

        # التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
        if role.users:
            return jsonify({'success': False, 'message': 'لا يمكن حذف دور مرتبط بمستخدمين'})

        role_name = role.name
        db.session.delete(role)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='حذف دور',
            resource_type='role',
            details=f'الدور المحذوف: {role_name}',
            request_obj=request
        )

        return jsonify({'success': True, 'message': f'تم حذف الدور {role_name} بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# إنشاء نسخة احتياطية محسنة
@app.route('/create_backup', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية شاملة من النظام"""
    if not current_user.has_permission('backup'):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية لإنشاء النسخ الاحتياطية'})

    try:
        import shutil
        import os
        import zipfile
        import json
        from datetime import datetime

        # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)

        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_type = request.json.get('backup_type', 'full') if request.is_json else 'full'
        backup_filename = f'backup_{backup_type}_{timestamp}.zip'
        backup_path = os.path.join(backup_dir, backup_filename)

        # إنشاء النسخة الاحتياطية
        backup_log = BackupLog(
            backup_type=backup_type,
            file_path=backup_path,
            status='pending',
            created_by=current_user.id
        )
        db.session.add(backup_log)
        db.session.commit()

        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                db_path = 'driver_management.db'
                if os.path.exists(db_path):
                    zipf.write(db_path, 'database/driver_management.db')

                # نسخ الملفات المرفوعة
                if backup_type == 'full':
                    for folder in ['uploads', 'documents', 'reports']:
                        if os.path.exists(folder):
                            for root, dirs, files in os.walk(folder):
                                for file in files:
                                    file_path = os.path.join(root, file)
                                    arcname = os.path.relpath(file_path)
                                    zipf.write(file_path, arcname)

                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'created_at': datetime.now().isoformat(),
                    'created_by': current_user.username,
                    'backup_type': backup_type,
                    'version': '1.0',
                    'system_info': {
                        'total_drivers': Driver.query.count(),
                        'total_payments': Payment.query.count(),
                        'total_documents': Document.query.count(),
                        'total_users': User.query.count()
                    }
                }
                zipf.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))

            # تحديث سجل النسخة الاحتياطية
            backup_log.file_size = os.path.getsize(backup_path)
            backup_log.status = 'completed'
            backup_log.completed_at = datetime.utcnow()
            db.session.commit()

            # تسجيل النشاط
            ActivityLog.log_activity(
                user_id=current_user.id,
                action='create_backup',
                resource_type='backup',
                resource_id=backup_log.id,
                details={'backup_type': backup_type, 'filename': backup_filename, 'size': backup_log.file_size},
                request_obj=request
            )

            return jsonify({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_id': backup_log.id,
                'filename': backup_filename,
                'size': backup_log.file_size
            })

        except Exception as backup_error:
            backup_log.status = 'failed'
            backup_log.error_message = str(backup_error)
            backup_log.completed_at = datetime.utcnow()
            db.session.commit()
            raise backup_error

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}'})

# استعادة نسخة احتياطية
@app.route('/restore_backup', methods=['POST'])
@login_required
def restore_backup():
    """استعادة نسخة احتياطية"""
    if not current_user.has_permission('backup'):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية لاستعادة النسخ الاحتياطية'})

    try:
        if 'backup_file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف النسخة الاحتياطية'})

        backup_file = request.files['backup_file']
        if backup_file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف صحيح'})

        import tempfile
        import zipfile
        import json
        import shutil

        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            backup_file.save(temp_file.name)

            # التحقق من صحة الملف
            try:
                with zipfile.ZipFile(temp_file.name, 'r') as zipf:
                    # التحقق من وجود معلومات النسخة الاحتياطية
                    if 'backup_info.json' in zipf.namelist():
                        backup_info = json.loads(zipf.read('backup_info.json').decode('utf-8'))
                    else:
                        return jsonify({'success': False, 'message': 'ملف النسخة الاحتياطية غير صحيح'})

                    # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
                    current_backup_result = create_backup_internal('pre_restore')

                    # استعادة قاعدة البيانات
                    if 'database/driver_management.db' in zipf.namelist():
                        # إغلاق الاتصالات الحالية
                        db.session.close()

                        # استعادة قاعدة البيانات
                        zipf.extract('database/driver_management.db', '.')
                        shutil.move('database/driver_management.db', 'driver_management.db')
                        os.rmdir('database')

                    # استعادة الملفات
                    for file_info in zipf.infolist():
                        if file_info.filename.startswith(('uploads/', 'documents/', 'reports/')):
                            zipf.extract(file_info, '.')

                    # تسجيل النشاط
                    ActivityLog.log_activity(
                        user_id=current_user.id,
                        action='restore_backup',
                        resource_type='backup',
                        details={
                            'backup_info': backup_info,
                            'pre_restore_backup': current_backup_result.get('filename') if current_backup_result else None
                        },
                        request_obj=request
                    )

                    return jsonify({
                        'success': True,
                        'message': 'تم استعادة النسخة الاحتياطية بنجاح',
                        'backup_info': backup_info
                    })

            except zipfile.BadZipFile:
                return jsonify({'success': False, 'message': 'ملف النسخة الاحتياطية تالف أو غير صحيح'})
            finally:
                os.unlink(temp_file.name)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}'})

def create_backup_internal(backup_type='auto'):
    """إنشاء نسخة احتياطية داخلية"""
    try:
        import shutil
        import os
        import zipfile
        import json
        from datetime import datetime

        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{backup_type}_{timestamp}.zip'
        backup_path = os.path.join(backup_dir, backup_filename)

        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # نسخ قاعدة البيانات
            db_path = 'driver_management.db'
            if os.path.exists(db_path):
                zipf.write(db_path, 'database/driver_management.db')

            # إضافة معلومات النسخة الاحتياطية
            backup_info = {
                'created_at': datetime.now().isoformat(),
                'backup_type': backup_type,
                'version': '1.0'
            }
            zipf.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))

        return {'success': True, 'filename': backup_filename, 'path': backup_path}
    except Exception as e:
        return {'success': False, 'error': str(e)}

# إدارة النسخ الاحتياطية
@app.route('/settings/backups')
@login_required
def manage_backups():
    """إدارة النسخ الاحتياطية"""
    if not current_user.has_permission('backup'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    per_page = 20

    backups = BackupLog.query.order_by(BackupLog.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('settings/backups.html', backups=backups)

# تحميل نسخة احتياطية
@app.route('/download_backup/<int:backup_id>')
@login_required
def download_backup(backup_id):
    """تحميل نسخة احتياطية"""
    if not current_user.has_permission('backup'):
        flash('ليس لديك صلاحية لتحميل النسخ الاحتياطية', 'error')
        return redirect(url_for('dashboard'))

    backup = BackupLog.query.get_or_404(backup_id)

    if not os.path.exists(backup.file_path):
        flash('ملف النسخة الاحتياطية غير موجود', 'error')
        return redirect(url_for('manage_backups'))

    # تسجيل النشاط
    ActivityLog.log_activity(
        user_id=current_user.id,
        action='download_backup',
        resource_type='backup',
        resource_id=backup_id,
        details={'filename': os.path.basename(backup.file_path)},
        request_obj=request
    )

    return send_file(backup.file_path, as_attachment=True,
                    download_name=os.path.basename(backup.file_path))

# حذف نسخة احتياطية
@app.route('/delete_backup/<int:backup_id>', methods=['POST'])
@login_required
def delete_backup(backup_id):
    """حذف نسخة احتياطية"""
    if not current_user.has_permission('admin'):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية لحذف النسخ الاحتياطية'})

    try:
        backup = BackupLog.query.get_or_404(backup_id)

        # حذف الملف من النظام
        if os.path.exists(backup.file_path):
            os.remove(backup.file_path)

        # حذف السجل من قاعدة البيانات
        db.session.delete(backup)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='delete_backup',
            resource_type='backup',
            resource_id=backup_id,
            details={'filename': os.path.basename(backup.file_path)},
            request_obj=request
        )

        return jsonify({'success': True, 'message': 'تم حذف النسخة الاحتياطية بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ في حذف النسخة الاحتياطية: {str(e)}'})

# مراقبة النظام
@app.route('/settings/system-monitor')
@login_required
def system_monitor():
    """مراقبة النظام والأداء"""
    if not current_user.has_permission('admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        import psutil
        import os
        from datetime import datetime, timedelta

        # معلومات النظام
        system_info = {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory': psutil.virtual_memory(),
            'disk': psutil.disk_usage('.'),
            'boot_time': datetime.fromtimestamp(psutil.boot_time()),
            'python_version': f"{psutil.version_info[0]}.{psutil.version_info[1]}.{psutil.version_info[2]}"
        }

        # معلومات قاعدة البيانات
        db_info = {
            'total_drivers': Driver.query.count(),
            'active_drivers': Driver.query.filter_by(status='نشط').count(),
            'total_payments': Payment.query.count(),
            'total_documents': Document.query.count(),
            'total_users': User.query.count(),
            'db_size': os.path.getsize('driver_management.db') if os.path.exists('driver_management.db') else 0
        }

        # إحصائيات النشاط (آخر 24 ساعة)
        yesterday = datetime.utcnow() - timedelta(days=1)
        activity_stats = {
            'total_activities': ActivityLog.query.filter(ActivityLog.created_at >= yesterday).count(),
            'login_attempts': ActivityLog.query.filter(
                ActivityLog.created_at >= yesterday,
                ActivityLog.action == 'login'
            ).count(),
            'failed_logins': ActivityLog.query.filter(
                ActivityLog.created_at >= yesterday,
                ActivityLog.action == 'failed_login'
            ).count(),
            'recent_activities': ActivityLog.query.order_by(ActivityLog.created_at.desc()).limit(10).all()
        }

        # حالة النسخ الاحتياطية
        backup_stats = {
            'total_backups': BackupLog.query.count(),
            'successful_backups': BackupLog.query.filter_by(status='completed').count(),
            'failed_backups': BackupLog.query.filter_by(status='failed').count(),
            'last_backup': BackupLog.query.filter_by(status='completed').order_by(BackupLog.created_at.desc()).first()
        }

        return render_template('settings/system_monitor.html',
                             system_info=system_info,
                             db_info=db_info,
                             activity_stats=activity_stats,
                             backup_stats=backup_stats)

    except ImportError:
        # إذا لم تكن مكتبة psutil مثبتة
        flash('مكتبة مراقبة النظام غير مثبتة. يرجى تثبيت psutil', 'warning')
        return render_template('settings/system_monitor_basic.html')

# API لمعلومات النظام
@app.route('/api/system-status')
@login_required
def api_system_status():
    """API للحصول على حالة النظام"""
    if not current_user.has_permission('admin'):
        return jsonify({'success': False, 'message': 'غير مصرح'})

    try:
        # فحص قاعدة البيانات
        db_status = 'healthy'
        try:
            db.session.execute('SELECT 1')
        except:
            db_status = 'error'

        # فحص المجلدات المطلوبة
        required_folders = ['uploads', 'documents', 'reports', 'backups']
        folders_status = {}
        for folder in required_folders:
            folders_status[folder] = os.path.exists(folder) and os.path.isdir(folder)

        # إحصائيات سريعة
        quick_stats = {
            'total_drivers': Driver.query.count(),
            'active_sessions': 1,  # يمكن تحسينها لاحقاً
            'pending_payments': Payment.query.filter_by(status='معلق').count(),
            'system_uptime': 'متاح'  # يمكن تحسينها لاحقاً
        }

        return jsonify({
            'success': True,
            'status': 'healthy' if db_status == 'healthy' and all(folders_status.values()) else 'warning',
            'database': db_status,
            'folders': folders_status,
            'stats': quick_stats,
            'timestamp': datetime.utcnow().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        })

# صفحة الملف الشخصي
@app.route('/profile')
@login_required
def profile():
    """صفحة الملف الشخصي"""
    return render_template('settings.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='مدير',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
# ==================== Routes التصدير ====================

@app.route('/export/drivers/excel')
@login_required
def export_drivers_excel():
    """تصدير قائمة السائقين إلى Excel"""
    try:
        # الحصول على المعاملات
        status_filter = request.args.get('status', '')
        payment_type_filter = request.args.get('payment_type', '')

        # بناء الاستعلام
        query = Driver.query
        if status_filter:
            query = query.filter(Driver.status == status_filter)
        if payment_type_filter:
            query = query.filter(Driver.payment_type == payment_type_filter)

        drivers = query.all()

        # إنشاء مصدر Excel
        exporter = ExcelExporter()
        filepath = exporter.export_drivers_list(drivers, status_filter, payment_type_filter)

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير قائمة السائقين إلى Excel - عدد السائقين: {len(drivers)}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('drivers_list'))

@app.route('/export/payments/excel')
@login_required
def export_payments_excel():
    """تصدير تقرير المدفوعات إلى Excel"""
    try:
        # الحصول على المعاملات
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        start_date = None
        end_date = None

        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')

        # استعلام المدفوعات
        query = Payment.query
        if start_date:
            query = query.filter(Payment.payment_date >= start_date)
        if end_date:
            query = query.filter(Payment.payment_date <= end_date)

        payments = query.all()

        # إنشاء مصدر Excel
        exporter = ExcelExporter()
        filepath = exporter.export_payments_report(payments, start_date, end_date)

        # تسجيل النشاط
        period_text = ""
        if start_date and end_date:
            period_text = f" من {start_date.strftime('%Y/%m/%d')} إلى {end_date.strftime('%Y/%m/%d')}"
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير تقرير المدفوعات إلى Excel{period_text}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('reports'))

@app.route('/export/driver/<int:driver_id>/pdf')
@login_required
def export_driver_pdf(driver_id):
    """تصدير تقرير سائق فردي إلى PDF"""
    try:
        # إنشاء مصدر PDF
        exporter = PDFExporter()
        filepath = exporter.export_driver_report(driver_id)

        driver = Driver.query.get_or_404(driver_id)

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير تقرير السائق {driver.name} إلى PDF',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('drivers_list'))

@app.route('/export/financial/excel')
@login_required
def export_financial_excel():
    """تصدير التقرير المالي الشامل إلى Excel"""
    try:
        # الحصول على المعاملات
        year = request.args.get('year', datetime.now().year, type=int)
        month = request.args.get('month', type=int)

        # استعلام البيانات
        drivers = Driver.query.all()

        # استعلام المدفوعات حسب السنة والشهر
        payments_query = Payment.query
        if year:
            payments_query = payments_query.filter(db.extract('year', Payment.payment_date) == year)
        if month:
            payments_query = payments_query.filter(db.extract('month', Payment.payment_date) == month)

        payments = payments_query.all()

        # إنشاء مصدر Excel
        exporter = ExcelExporter()
        filepath = exporter.export_financial_report(drivers, payments, year, month)

        # تسجيل النشاط
        period_text = f"سنة {year}"
        if month:
            period_text += f" شهر {month}"
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير التقرير المالي الشامل إلى Excel - {period_text}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('reports'))

@app.route('/export')
@login_required
def export_center():
    """مركز التصدير - صفحة اختيار نوع التصدير"""
    drivers_count = Driver.query.count()
    payments_count = Payment.query.count()

    # إحصائيات سريعة
    stats = {
        'total_drivers': drivers_count,
        'active_drivers': Driver.query.filter_by(status='نشط').count(),
        'total_payments': payments_count,
        'this_month_payments': Payment.query.filter(
            Payment.payment_date >= datetime.now().replace(day=1)
        ).count()
    }

    return render_template('export_center.html', stats=stats)

# ===== API ROUTES FOR REPORTS =====

@app.route('/api/generate-report')
@login_required
def api_generate_report():
    """API لإنشاء التقارير المخصصة"""
    try:
        report_type = request.args.get('report_type')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')

        # تحويل التواريخ
        start_dt = None
        end_dt = None
        if start_date:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        if report_type == 'drivers':
            data = generate_drivers_report_data(status, start_dt, end_dt)
        elif report_type == 'payments':
            data = generate_payments_report_data(start_dt, end_dt)
        elif report_type == 'financial':
            data = generate_financial_report_data(start_dt, end_dt)
        elif report_type == 'overdue':
            data = generate_overdue_report_data()
        else:
            return jsonify({'success': False, 'error': 'نوع تقرير غير مدعوم'})

        return jsonify({'success': True, 'data': data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def generate_drivers_report_data(status_filter=None, start_date=None, end_date=None):
    """إنشاء بيانات تقرير السائقين"""
    query = Driver.query

    if status_filter:
        query = query.filter_by(status=status_filter)

    if start_date:
        query = query.filter(Driver.created_at >= start_date)

    if end_date:
        query = query.filter(Driver.created_at <= end_date)

    drivers = query.all()

    # إحصائيات السائقين
    total_drivers = len(drivers)
    active_drivers = len([d for d in drivers if d.status == 'نشط'])
    inactive_drivers = len([d for d in drivers if d.status == 'غير نشط'])
    suspended_drivers = len([d for d in drivers if d.status == 'معلق'])

    # تحضير بيانات السائقين
    drivers_data = []
    for driver in drivers:
        # آخر دفعة للسائق
        last_payment = Payment.query.filter_by(driver_id=driver.id).order_by(Payment.payment_date.desc()).first()

        drivers_data.append({
            'id': driver.id,
            'name': driver.name,
            'phone': driver.phone,
            'vehicle_type': driver.vehicle_type or 'غير محدد',
            'status': driver.status,
            'payment_type': driver.payment_type,
            'payment_amount': driver.payment_amount,
            'last_payment_date': last_payment.payment_date.strftime('%Y-%m-%d') if last_payment else None
        })

    return {
        'total_drivers': total_drivers,
        'active_drivers': active_drivers,
        'inactive_drivers': inactive_drivers,
        'suspended_drivers': suspended_drivers,
        'drivers': drivers_data
    }

def generate_payments_report_data(start_date=None, end_date=None):
    """إنشاء بيانات تقرير المدفوعات"""
    query = Payment.query

    if start_date:
        query = query.filter(Payment.payment_date >= start_date)

    if end_date:
        query = query.filter(Payment.payment_date <= end_date)

    payments = query.order_by(Payment.payment_date.desc()).all()

    # حساب الإحصائيات
    total_amount = sum(p.amount for p in payments)
    total_payments = len(payments)
    avg_payment = total_amount / total_payments if total_payments > 0 else 0

    # تحضير بيانات المدفوعات
    payments_data = []
    for payment in payments:
        driver = Driver.query.get(payment.driver_id)
        payments_data.append({
            'id': payment.id,
            'driver_name': driver.name if driver else 'غير معروف',
            'amount': payment.amount,
            'payment_method': payment.payment_method,
            'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
            'status': payment.status,
            'notes': payment.notes
        })

    return {
        'total_amount': total_amount,
        'total_payments': total_payments,
        'avg_payment': avg_payment,
        'payments': payments_data
    }

def generate_financial_report_data(start_date=None, end_date=None):
    """إنشاء بيانات التقرير المالي"""
    query = Payment.query

    if start_date:
        query = query.filter(Payment.payment_date >= start_date)

    if end_date:
        query = query.filter(Payment.payment_date <= end_date)

    payments = query.all()

    # حساب الإيرادات
    total_revenue = sum(p.amount for p in payments if p.status == 'مكتمل')
    pending_revenue = sum(p.amount for p in payments if p.status == 'معلق')

    # إيرادات هذا الشهر
    current_month = datetime.now().replace(day=1)
    this_month_payments = Payment.query.filter(
        Payment.payment_date >= current_month,
        Payment.status == 'مكتمل'
    ).all()
    this_month_revenue = sum(p.amount for p in this_month_payments)

    # إيرادات الشهر الماضي
    last_month = (current_month - timedelta(days=1)).replace(day=1)
    last_month_end = current_month - timedelta(days=1)
    last_month_payments = Payment.query.filter(
        Payment.payment_date >= last_month,
        Payment.payment_date <= last_month_end,
        Payment.status == 'مكتمل'
    ).all()
    last_month_revenue = sum(p.amount for p in last_month_payments)

    # حساب معدل النمو
    growth_rate = 0
    if last_month_revenue > 0:
        growth_rate = ((this_month_revenue - last_month_revenue) / last_month_revenue) * 100

    return {
        'total_revenue': total_revenue,
        'pending_revenue': pending_revenue,
        'this_month_revenue': this_month_revenue,
        'last_month_revenue': last_month_revenue,
        'growth_rate': growth_rate
    }

def generate_overdue_report_data():
    """إنشاء بيانات تقرير المتأخرين"""
    current_date = datetime.now().date()
    overdue_drivers = Driver.query.filter(Driver.next_payment_due < current_date).all()

    overdue_data = []
    for driver in overdue_drivers:
        days_overdue = (current_date - driver.next_payment_due).days
        overdue_data.append({
            'id': driver.id,
            'name': driver.name,
            'phone': driver.phone,
            'outstanding_amount': driver.payment_amount,
            'next_payment_due': driver.next_payment_due.strftime('%Y-%m-%d'),
            'days_overdue': days_overdue
        })

    return {
        'overdue_drivers': overdue_data
    }

@app.route('/api/export-report')
@login_required
def api_export_report():
    """API لتصدير التقارير"""
    try:
        report_type = request.args.get('report_type')
        format_type = request.args.get('format', 'excel')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')

        # تحويل التواريخ
        start_dt = None
        end_dt = None
        if start_date:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if format_type == 'excel':
            return export_report_excel(report_type, start_dt, end_dt, status, timestamp)
        elif format_type == 'pdf':
            return export_report_pdf(report_type, start_dt, end_dt, status, timestamp)
        elif format_type == 'csv':
            return export_report_csv(report_type, start_dt, end_dt, status, timestamp)
        else:
            flash('صيغة تصدير غير مدعومة', 'error')
            return redirect(url_for('reports'))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('reports'))

def export_report_excel(report_type, start_date, end_date, status, timestamp):
    """تصدير التقرير إلى Excel"""
    exporter = ExcelExporter()

    if report_type == 'drivers':
        data = generate_drivers_report_data(status, start_date, end_date)
        filename = f'drivers_report_{timestamp}.xlsx'
        filepath = exporter.export_custom_drivers_report(data, filename)
    elif report_type == 'payments':
        data = generate_payments_report_data(start_date, end_date)
        filename = f'payments_report_{timestamp}.xlsx'
        filepath = exporter.export_custom_payments_report(data, filename)
    elif report_type == 'financial':
        data = generate_financial_report_data(start_date, end_date)
        filename = f'financial_report_{timestamp}.xlsx'
        filepath = exporter.export_custom_financial_report(data, filename)
    elif report_type == 'overdue':
        data = generate_overdue_report_data()
        filename = f'overdue_report_{timestamp}.xlsx'
        filepath = exporter.export_custom_overdue_report(data, filename)
    else:
        raise ValueError('نوع تقرير غير مدعوم')

    return send_file(filepath, as_attachment=True, download_name=filename)

def export_report_pdf(report_type, start_date, end_date, status, timestamp):
    """تصدير التقرير إلى PDF"""
    exporter = PDFExporter()

    if report_type == 'drivers':
        data = generate_drivers_report_data(status, start_date, end_date)
        filename = f'drivers_report_{timestamp}.pdf'
        filepath = exporter.export_custom_drivers_report(data, filename)
    elif report_type == 'payments':
        data = generate_payments_report_data(start_date, end_date)
        filename = f'payments_report_{timestamp}.pdf'
        filepath = exporter.export_custom_payments_report(data, filename)
    elif report_type == 'financial':
        data = generate_financial_report_data(start_date, end_date)
        filename = f'financial_report_{timestamp}.pdf'
        filepath = exporter.export_custom_financial_report(data, filename)
    elif report_type == 'overdue':
        data = generate_overdue_report_data()
        filename = f'overdue_report_{timestamp}.pdf'
        filepath = exporter.export_custom_overdue_report(data, filename)
    else:
        raise ValueError('نوع تقرير غير مدعوم')

    return send_file(filepath, as_attachment=True, download_name=filename)

def export_report_csv(report_type, start_date, end_date, status, timestamp):
    """تصدير التقرير إلى CSV"""
    import csv
    import io

    if report_type == 'drivers':
        data = generate_drivers_report_data(status, start_date, end_date)
        output = io.StringIO()
        writer = csv.writer(output)

        # كتابة العناوين
        writer.writerow(['الاسم', 'الهاتف', 'نوع المركبة', 'الحالة', 'نوع الدفع', 'المبلغ', 'آخر دفعة'])

        # كتابة البيانات
        for driver in data['drivers']:
            writer.writerow([
                driver['name'], driver['phone'], driver['vehicle_type'],
                driver['status'], driver['payment_type'], driver['payment_amount'],
                driver['last_payment_date'] or 'لا توجد'
            ])

        output.seek(0)
        filename = f'drivers_report_{timestamp}.csv'

    elif report_type == 'payments':
        data = generate_payments_report_data(start_date, end_date)
        output = io.StringIO()
        writer = csv.writer(output)

        # كتابة العناوين
        writer.writerow(['السائق', 'المبلغ', 'طريقة الدفع', 'التاريخ', 'الحالة', 'الملاحظات'])

        # كتابة البيانات
        for payment in data['payments']:
            writer.writerow([
                payment['driver_name'], payment['amount'], payment['payment_method'],
                payment['payment_date'], payment['status'], payment['notes'] or '-'
            ])

        output.seek(0)
        filename = f'payments_report_{timestamp}.csv'

    else:
        raise ValueError('نوع تقرير غير مدعوم للتصدير CSV')

    # إنشاء استجابة CSV
    response = app.response_class(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment; filename={filename}'}
    )

    return response

@app.route('/api/send-reminder/<int:driver_id>', methods=['POST'])
@login_required
def api_send_reminder(driver_id):
    """API لإرسال تذكير للسائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)

        # هنا يمكن إضافة كود إرسال الرسالة عبر WhatsApp أو SMS
        # للآن سنقوم بتسجيل النشاط فقط

        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'إرسال تذكير للسائق {driver.name}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إرسال التذكير بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/upload-csv', methods=['POST'])
@login_required
def api_upload_csv():
    """API لرفع ملفات CSV"""
    try:
        if 'csvFile' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        file = request.files['csvFile']
        upload_type = request.form.get('uploadType')
        update_existing = request.form.get('updateExisting') == 'true'

        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        if not file.filename.lower().endswith('.csv'):
            return jsonify({'success': False, 'message': 'يجب أن يكون الملف بصيغة CSV'})

        # حفظ الملف مؤقتاً
        import tempfile
        import csv

        with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.csv') as temp_file:
            file.save(temp_file.name)

            # معالجة الملف
            if upload_type == 'drivers':
                result = process_drivers_csv(temp_file.name, update_existing)
            elif upload_type == 'payments':
                result = process_payments_csv(temp_file.name, update_existing)
            else:
                return jsonify({'success': False, 'message': 'نوع بيانات غير مدعوم'})

        # حذف الملف المؤقت
        os.unlink(temp_file.name)

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'رفع ملف CSV - {upload_type}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

def process_drivers_csv(filepath, update_existing=False):
    """معالجة ملف CSV للسائقين"""
    import csv

    processed = 0
    added = 0
    updated = 0
    errors = []

    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)

            for row_num, row in enumerate(reader, 2):  # البداية من الصف 2 (بعد العناوين)
                try:
                    # التحقق من الحقول المطلوبة
                    if not row.get('name') or not row.get('phone'):
                        errors.append(f"الصف {row_num}: الاسم والهاتف مطلوبان")
                        continue

                    # البحث عن السائق الموجود
                    existing_driver = Driver.query.filter_by(phone=row['phone']).first()

                    if existing_driver and not update_existing:
                        errors.append(f"الصف {row_num}: السائق موجود بالفعل - {row['phone']}")
                        continue

                    # إنشاء أو تحديث السائق
                    if existing_driver and update_existing:
                        # تحديث البيانات الموجودة
                        existing_driver.name = row.get('name', existing_driver.name)
                        existing_driver.national_id = row.get('national_id', existing_driver.national_id)
                        existing_driver.license_number = row.get('license_number', existing_driver.license_number)
                        existing_driver.vehicle_type = row.get('vehicle_type', existing_driver.vehicle_type)
                        existing_driver.vehicle_plate = row.get('vehicle_plate', existing_driver.vehicle_plate)
                        existing_driver.payment_type = row.get('payment_type', existing_driver.payment_type)
                        existing_driver.payment_amount = float(row.get('payment_amount', 0)) if row.get('payment_amount') else existing_driver.payment_amount
                        existing_driver.status = row.get('status', existing_driver.status)
                        updated += 1
                    else:
                        # إضافة سائق جديد
                        new_driver = Driver(
                            name=row['name'],
                            phone=row['phone'],
                            national_id=row.get('national_id'),
                            license_number=row.get('license_number'),
                            vehicle_type=row.get('vehicle_type'),
                            vehicle_plate=row.get('vehicle_plate'),
                            payment_type=row.get('payment_type', 'شهري'),
                            payment_amount=float(row.get('payment_amount', 0)) if row.get('payment_amount') else 0,
                            status=row.get('status', 'نشط')
                        )
                        db.session.add(new_driver)
                        added += 1

                    processed += 1

                except Exception as e:
                    errors.append(f"الصف {row_num}: {str(e)}")

        db.session.commit()

        return {
            'success': True,
            'message': f'تم معالجة {processed} سجل بنجاح',
            'details': {
                'processed': processed,
                'added': added,
                'updated': updated,
                'errors': errors[:5]  # أول 5 أخطاء فقط
            }
        }

    except Exception as e:
        db.session.rollback()
        return {
            'success': False,
            'message': f'خطأ في معالجة الملف: {str(e)}'
        }

def process_payments_csv(filepath, update_existing=False):
    """معالجة ملف CSV للمدفوعات"""
    import csv
    from datetime import datetime

    processed = 0
    added = 0
    updated = 0
    errors = []

    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)

            for row_num, row in enumerate(reader, 2):
                try:
                    # التحقق من الحقول المطلوبة
                    if not row.get('driver_phone') or not row.get('amount'):
                        errors.append(f"الصف {row_num}: هاتف السائق والمبلغ مطلوبان")
                        continue

                    # البحث عن السائق
                    driver = Driver.query.filter_by(phone=row['driver_phone']).first()
                    if not driver:
                        errors.append(f"الصف {row_num}: السائق غير موجود - {row['driver_phone']}")
                        continue

                    # تحويل التاريخ
                    payment_date = datetime.now()
                    if row.get('payment_date'):
                        try:
                            payment_date = datetime.strptime(row['payment_date'], '%Y-%m-%d')
                        except:
                            try:
                                payment_date = datetime.strptime(row['payment_date'], '%d/%m/%Y')
                            except:
                                errors.append(f"الصف {row_num}: تاريخ غير صحيح - {row['payment_date']}")
                                continue

                    # إنشاء دفعة جديدة
                    new_payment = Payment(
                        driver_id=driver.id,
                        amount=float(row['amount']),
                        payment_method=row.get('payment_method', 'نقدي'),
                        payment_date=payment_date,
                        status=row.get('status', 'مكتمل'),
                        notes=row.get('notes')
                    )

                    db.session.add(new_payment)
                    added += 1
                    processed += 1

                except Exception as e:
                    errors.append(f"الصف {row_num}: {str(e)}")

        db.session.commit()

        return {
            'success': True,
            'message': f'تم معالجة {processed} دفعة بنجاح',
            'details': {
                'processed': processed,
                'added': added,
                'updated': updated,
                'errors': errors[:5]
            }
        }

    except Exception as e:
        db.session.rollback()
        return {
            'success': False,
            'message': f'خطأ في معالجة الملف: {str(e)}'
        }

@app.route('/api/download-template/<template_type>')
@login_required
def api_download_template(template_type):
    """API لتحميل نماذج CSV"""
    try:
        import csv
        import io

        if template_type == 'drivers':
            # نموذج السائقين
            headers = ['name', 'phone', 'national_id', 'license_number', 'vehicle_type',
                      'vehicle_plate', 'payment_type', 'payment_amount', 'status']
            sample_data = [
                ['أحمد محمد', '**********', '**********', 'L123456', 'سيارة', 'أ ب ج 123', 'شهري', '2000', 'نشط'],
                ['محمد أحمد', '**********', '**********', 'L654321', 'دراجة نارية', 'د هـ و 456', 'أسبوعي', '500', 'نشط']
            ]
            filename = 'drivers_template.csv'

        elif template_type == 'payments':
            # نموذج المدفوعات
            headers = ['driver_phone', 'amount', 'payment_method', 'payment_date', 'status', 'notes']
            sample_data = [
                ['**********', '2000', 'نقدي', '2024-01-15', 'مكتمل', 'دفعة شهر يناير'],
                ['**********', '500', 'تحويل بنكي', '2024-01-15', 'مكتمل', 'دفعة أسبوعية']
            ]
            filename = 'payments_template.csv'

        else:
            return jsonify({'success': False, 'message': 'نوع نموذج غير مدعوم'})

        # إنشاء ملف CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # كتابة العناوين
        writer.writerow(headers)

        # كتابة البيانات النموذجية
        for row in sample_data:
            writer.writerow(row)

        output.seek(0)

        # إنشاء استجابة CSV
        response = app.response_class(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )

        return response

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تحميل النموذج: {str(e)}'})

# نظام إدارة المستندات
@app.route('/documents')
@login_required
def documents_list():
    """عرض قائمة المستندات"""
    try:
        # الحصول على المعاملات
        driver_id = request.args.get('driver_id', type=int)
        document_type = request.args.get('document_type', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # بناء الاستعلام
        query = Document.query

        if driver_id:
            query = query.filter_by(driver_id=driver_id)

        if document_type:
            query = query.filter_by(document_type=document_type)

        # ترتيب وتقسيم الصفحات
        documents = query.order_by(Document.upload_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # الحصول على قائمة السائقين لفلترة
        drivers = Driver.query.all()

        # أنواع المستندات المتاحة
        document_types = [
            'رخصة القيادة',
            'الهوية الوطنية',
            'استمارة المركبة',
            'تأمين المركبة',
            'فحص دوري',
            'إيصال دفع',
            'عقد العمل',
            'أخرى'
        ]

        return render_template('documents.html',
                             documents=documents,
                             drivers=drivers,
                             document_types=document_types,
                             current_driver_id=driver_id,
                             current_document_type=document_type)

    except Exception as e:
        flash(f'حدث خطأ في تحميل المستندات: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/upload_document', methods=['POST'])
@login_required
def upload_document():
    """رفع مستند جديد"""
    try:
        if not current_user.has_permission('create'):
            flash('ليس لديك صلاحية لرفع المستندات', 'error')
            return redirect(url_for('documents_list'))

        driver_id = request.form.get('driver_id', type=int)
        document_type = request.form.get('document_type')
        file = request.files.get('document_file')

        if not driver_id or not document_type or not file:
            flash('جميع الحقول مطلوبة', 'error')
            return redirect(url_for('documents_list'))

        # التحقق من وجود السائق
        driver = Driver.query.get_or_404(driver_id)

        # التحقق من نوع الملف
        if not allowed_file(file.filename):
            flash('نوع الملف غير مدعوم', 'error')
            return redirect(url_for('documents_list'))

        # إنشاء اسم ملف آمن
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{driver_id}_{document_type}_{timestamp}_{filename}"

        # حفظ الملف
        file_path = os.path.join(app.config['DOCUMENTS_FOLDER'], filename)
        file.save(file_path)

        # حفظ معلومات المستند في قاعدة البيانات
        document = Document(
            driver_id=driver_id,
            document_type=document_type,
            file_name=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path),
            uploaded_by=current_user.id
        )

        db.session.add(document)
        db.session.commit()

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'رفع مستند - {document_type} للسائق {driver.name}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        flash(f'تم رفع المستند بنجاح', 'success')
        return redirect(url_for('documents_list', driver_id=driver_id))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في رفع المستند: {str(e)}', 'error')
        return redirect(url_for('documents_list'))

def allowed_file(filename):
    """التحقق من أنواع الملفات المسموحة"""
    ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xlsx', 'xls'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/download_document/<int:document_id>')
@login_required
def download_document(document_id):
    """تحميل مستند"""
    try:
        document = Document.query.get_or_404(document_id)

        # التحقق من وجود الملف
        if not os.path.exists(document.file_path):
            flash('الملف غير موجود', 'error')
            return redirect(url_for('documents_list'))

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تحميل مستند - {document.document_type}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(document.file_path, as_attachment=True, download_name=document.file_name)

    except Exception as e:
        flash(f'حدث خطأ في تحميل المستند: {str(e)}', 'error')
        return redirect(url_for('documents_list'))

@app.route('/delete_document/<int:document_id>', methods=['POST'])
@login_required
def delete_document(document_id):
    """حذف مستند"""
    try:
        if not current_user.has_permission('delete'):
            flash('ليس لديك صلاحية لحذف المستندات', 'error')
            return redirect(url_for('documents_list'))

        document = Document.query.get_or_404(document_id)

        # حذف الملف من النظام
        if os.path.exists(document.file_path):
            os.remove(document.file_path)

        # حذف السجل من قاعدة البيانات
        db.session.delete(document)
        db.session.commit()

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'حذف مستند - {document.document_type}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        flash('تم حذف المستند بنجاح', 'success')
        return redirect(url_for('documents_list'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في حذف المستند: {str(e)}', 'error')
        return redirect(url_for('documents_list'))

# البحث الذكي المتقدم
@app.route('/api/search')
@login_required
def api_smart_search():
    """API للبحث الذكي المتقدم"""
    try:
        query = request.args.get('q', '').strip()
        search_type = request.args.get('type', 'all')  # all, drivers, payments, documents
        limit = request.args.get('limit', 10, type=int)

        if not query or len(query) < 2:
            return jsonify({'success': False, 'message': 'يجب أن يكون البحث أكثر من حرفين'})

        results = {
            'drivers': [],
            'payments': [],
            'documents': [],
            'total': 0
        }

        # البحث في السائقين
        if search_type in ['all', 'drivers']:
            drivers_query = Driver.query.filter(
                db.or_(
                    Driver.name.contains(query),
                    Driver.phone.contains(query),
                    Driver.national_id.contains(query),
                    Driver.license_number.contains(query),
                    Driver.vehicle_plate.contains(query),
                    Driver.vehicle_type.contains(query)
                )
            ).limit(limit)

            for driver in drivers_query:
                results['drivers'].append({
                    'id': driver.id,
                    'name': driver.name,
                    'phone': driver.phone,
                    'vehicle_type': driver.vehicle_type,
                    'vehicle_plate': driver.vehicle_plate,
                    'status': driver.status,
                    'type': 'driver'
                })

        # البحث في المدفوعات
        if search_type in ['all', 'payments']:
            payments_query = Payment.query.join(Driver).filter(
                db.or_(
                    Driver.name.contains(query),
                    Driver.phone.contains(query),
                    Payment.payment_method.contains(query),
                    Payment.notes.contains(query)
                )
            ).order_by(Payment.payment_date.desc()).limit(limit)

            for payment in payments_query:
                results['payments'].append({
                    'id': payment.id,
                    'driver_name': payment.driver.name,
                    'driver_phone': payment.driver.phone,
                    'amount': float(payment.amount),
                    'payment_method': payment.payment_method,
                    'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
                    'status': payment.status,
                    'type': 'payment'
                })

        # البحث في المستندات
        if search_type in ['all', 'documents']:
            documents_query = Document.query.join(Driver).filter(
                db.or_(
                    Driver.name.contains(query),
                    Driver.phone.contains(query),
                    Document.document_type.contains(query),
                    Document.file_name.contains(query)
                )
            ).order_by(Document.upload_date.desc()).limit(limit)

            for document in documents_query:
                results['documents'].append({
                    'id': document.id,
                    'driver_name': document.driver.name,
                    'driver_phone': document.driver.phone,
                    'document_type': document.document_type,
                    'file_name': document.file_name,
                    'upload_date': document.upload_date.strftime('%Y-%m-%d'),
                    'type': 'document'
                })

        # حساب العدد الإجمالي
        results['total'] = len(results['drivers']) + len(results['payments']) + len(results['documents'])

        return jsonify({
            'success': True,
            'results': results,
            'query': query
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في البحث: {str(e)}'})

# البحث المتقدم مع فلاتر
@app.route('/api/advanced-search')
@login_required
def api_advanced_search():
    """API للبحث المتقدم مع فلاتر متعددة"""
    try:
        # معاملات البحث
        query = request.args.get('q', '').strip()
        search_type = request.args.get('type', 'drivers')
        status_filter = request.args.get('status', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        amount_min = request.args.get('amount_min', type=float)
        amount_max = request.args.get('amount_max', type=float)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        results = []
        total = 0

        if search_type == 'drivers':
            # البحث المتقدم في السائقين
            query_obj = Driver.query

            if query:
                query_obj = query_obj.filter(
                    db.or_(
                        Driver.name.contains(query),
                        Driver.phone.contains(query),
                        Driver.national_id.contains(query),
                        Driver.license_number.contains(query),
                        Driver.vehicle_plate.contains(query)
                    )
                )

            if status_filter:
                query_obj = query_obj.filter(Driver.status == status_filter)

            # تطبيق التقسيم
            paginated = query_obj.paginate(page=page, per_page=per_page, error_out=False)

            for driver in paginated.items:
                results.append({
                    'id': driver.id,
                    'name': driver.name,
                    'phone': driver.phone,
                    'vehicle_type': driver.vehicle_type,
                    'vehicle_plate': driver.vehicle_plate,
                    'status': driver.status,
                    'payment_amount': float(driver.payment_amount) if driver.payment_amount else 0,
                    'next_payment_due': driver.next_payment_due.strftime('%Y-%m-%d') if driver.next_payment_due else None
                })

            total = paginated.total

        elif search_type == 'payments':
            # البحث المتقدم في المدفوعات
            query_obj = Payment.query.join(Driver)

            if query:
                query_obj = query_obj.filter(
                    db.or_(
                        Driver.name.contains(query),
                        Driver.phone.contains(query),
                        Payment.payment_method.contains(query)
                    )
                )

            if status_filter:
                query_obj = query_obj.filter(Payment.status == status_filter)

            if date_from:
                from datetime import datetime
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query_obj = query_obj.filter(Payment.payment_date >= date_from_obj)

            if date_to:
                from datetime import datetime
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                query_obj = query_obj.filter(Payment.payment_date <= date_to_obj)

            if amount_min is not None:
                query_obj = query_obj.filter(Payment.amount >= amount_min)

            if amount_max is not None:
                query_obj = query_obj.filter(Payment.amount <= amount_max)

            # تطبيق التقسيم
            paginated = query_obj.order_by(Payment.payment_date.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )

            for payment in paginated.items:
                results.append({
                    'id': payment.id,
                    'driver_name': payment.driver.name,
                    'driver_phone': payment.driver.phone,
                    'amount': float(payment.amount),
                    'payment_method': payment.payment_method,
                    'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
                    'status': payment.status,
                    'notes': payment.notes
                })

            total = paginated.total

        return jsonify({
            'success': True,
            'results': results,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في البحث المتقدم: {str(e)}'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
