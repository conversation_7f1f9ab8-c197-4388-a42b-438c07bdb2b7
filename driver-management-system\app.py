#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة السائقين المتطور
Driver Management System with AI Features
"""

import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime, timedelta
import json
from export_utils import ExcelExporter, PDFExporter

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///driver_management.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))

# إعداد مجلدات الرفع
app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', 'uploads')
app.config['REPORTS_FOLDER'] = os.getenv('REPORTS_FOLDER', 'reports')
app.config['DOCUMENTS_FOLDER'] = os.getenv('DOCUMENTS_FOLDER', 'documents')

# إنشاء المجلدات إذا لم تكن موجودة
for folder in [app.config['UPLOAD_FOLDER'], app.config['REPORTS_FOLDER'], app.config['DOCUMENTS_FOLDER']]:
    os.makedirs(folder, exist_ok=True)

# استيراد النماذج أولاً
from models import db, User, Driver, Payment, Document, AIAnalysis, Settings, Role, ActivityLog, BackupLog

# تهيئة قاعدة البيانات مع التطبيق
db.init_app(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    # إحصائيات أساسية
    total_drivers = Driver.query.count()
    active_drivers = Driver.query.filter_by(status='نشط').count()
    late_drivers = Driver.query.filter_by(payment_status='متأخر').count()

    # إحصائيات المدفوعات
    total_revenue = db.session.query(db.func.sum(Payment.amount)).scalar() or 0
    this_month_revenue = db.session.query(db.func.sum(Payment.amount)).filter(
        Payment.payment_date >= datetime.now().replace(day=1)
    ).scalar() or 0

    # السائقين الأحدث
    recent_drivers = Driver.query.order_by(Driver.created_at.desc()).limit(5).all()

    # المدفوعات الأخيرة
    recent_payments = Payment.query.order_by(Payment.payment_date.desc()).limit(5).all()
    
    return render_template('dashboard.html',
                         total_drivers=total_drivers,
                         active_drivers=active_drivers,
                         late_drivers=late_drivers,
                         total_revenue=total_revenue,
                         this_month_revenue=this_month_revenue,
                         recent_drivers=recent_drivers,
                         recent_payments=recent_payments)

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('login'))

# صفحة السائقين
@app.route('/drivers')
@login_required
def drivers_list():
    """قائمة السائقين"""
    drivers = Driver.query.all()
    return render_template('drivers.html', drivers=drivers)

# إضافة سائق جديد
@app.route('/drivers/add', methods=['POST'])
@login_required
def add_driver():
    """إضافة سائق جديد"""
    try:
        # استلام البيانات من النموذج
        name = request.form.get('name')
        phone = request.form.get('phone')
        national_id = request.form.get('national_id')
        license_number = request.form.get('license_number')
        vehicle_type = request.form.get('vehicle_type')
        vehicle_plate = request.form.get('vehicle_plate')
        payment_type = request.form.get('payment_type')
        payment_amount = float(request.form.get('payment_amount', 0))

        # التحقق من البيانات المطلوبة
        if not name or not phone or not payment_type:
            flash('الاسم ورقم الهاتف ونوع الدفع مطلوبة', 'error')
            return redirect(url_for('drivers_list'))

        # التحقق من عدم تكرار رقم الهاتف
        existing_driver = Driver.query.filter_by(phone=phone).first()
        if existing_driver:
            flash('رقم الهاتف مستخدم بالفعل', 'error')
            return redirect(url_for('drivers_list'))

        # إنشاء سائق جديد
        new_driver = Driver(
            name=name,
            phone=phone,
            national_id=national_id,
            license_number=license_number,
            vehicle_type=vehicle_type,
            vehicle_plate=vehicle_plate,
            payment_type=payment_type,
            payment_amount=payment_amount,
            status='نشط'
        )

        # حساب تاريخ الدفع التالي
        if payment_type == 'شهري':
            new_driver.next_payment_due = datetime.utcnow() + timedelta(days=30)
        elif payment_type == 'أسبوعي':
            new_driver.next_payment_due = datetime.utcnow() + timedelta(days=7)

        db.session.add(new_driver)
        db.session.commit()

        flash(f'تم إضافة السائق {name} بنجاح', 'success')
        return redirect(url_for('drivers_list'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة السائق: {str(e)}', 'error')
        return redirect(url_for('drivers_list'))

# تحديث بيانات السائق
@app.route('/drivers/update/<int:driver_id>', methods=['POST'])
@login_required
def update_driver(driver_id):
    """تحديث بيانات السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)

        # تحديث البيانات
        driver.name = request.form.get('name', driver.name)
        driver.phone = request.form.get('phone', driver.phone)
        driver.national_id = request.form.get('national_id', driver.national_id)
        driver.license_number = request.form.get('license_number', driver.license_number)
        driver.vehicle_type = request.form.get('vehicle_type', driver.vehicle_type)
        driver.vehicle_plate = request.form.get('vehicle_plate', driver.vehicle_plate)
        driver.payment_type = request.form.get('payment_type', driver.payment_type)
        driver.payment_amount = float(request.form.get('payment_amount', driver.payment_amount))
        driver.status = request.form.get('status', driver.status)
        driver.notes = request.form.get('notes', driver.notes)
        driver.updated_at = datetime.utcnow()

        db.session.commit()
        flash(f'تم تحديث بيانات السائق {driver.name} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث البيانات: {str(e)}', 'error')

    return redirect(url_for('drivers_list'))

# حذف السائق
@app.route('/drivers/delete/<int:driver_id>', methods=['POST'])
@login_required
def delete_driver(driver_id):
    """حذف السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)
        driver_name = driver.name

        # حذف المدفوعات المرتبطة بالسائق
        Payment.query.filter_by(driver_id=driver_id).delete()

        # حذف السائق
        db.session.delete(driver)
        db.session.commit()

        flash(f'تم حذف السائق {driver_name} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف السائق: {str(e)}', 'error')

    return redirect(url_for('drivers_list'))

# تحديث موقع السائق
@app.route('/drivers/update-location/<int:driver_id>', methods=['POST'])
@login_required
def update_driver_location(driver_id):
    """تحديث موقع السائق"""
    try:
        driver = Driver.query.get_or_404(driver_id)

        latitude = request.form.get('latitude')
        longitude = request.form.get('longitude')
        address = request.form.get('address')

        if latitude and longitude:
            driver.current_latitude = float(latitude)
            driver.current_longitude = float(longitude)
            driver.current_address = address
            driver.updated_at = datetime.utcnow()

            db.session.commit()
            flash(f'تم تحديث موقع السائق {driver.name} بنجاح', 'success')
        else:
            flash('الإحداثيات مطلوبة', 'error')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث الموقع: {str(e)}', 'error')

    return redirect(url_for('drivers_list'))

# صفحة المدفوعات
@app.route('/payments')
@login_required
def payments_list():
    """قائمة المدفوعات"""
    payments = Payment.query.order_by(Payment.payment_date.desc()).all()
    drivers = Driver.query.all()

    # حساب الإحصائيات
    total_payments = sum(p.amount for p in payments)
    monthly_payments = sum(p.amount for p in payments if p.payment_date and p.payment_date.month == datetime.now().month)
    pending_payments = Driver.query.filter(Driver.next_payment_due < datetime.now()).count()

    return render_template('payments.html',
                         payments=payments,
                         drivers=drivers,
                         total_payments=total_payments,
                         monthly_payments=monthly_payments,
                         pending_payments=pending_payments)

# إضافة دفعة جديدة
@app.route('/payments/add', methods=['POST'])
@login_required
def add_payment():
    """إضافة دفعة جديدة"""
    try:
        driver_id = int(request.form.get('driver_id'))
        amount = float(request.form.get('amount'))
        payment_method = request.form.get('payment_method', 'نقدي')
        notes = request.form.get('notes', '')

        # التحقق من وجود السائق
        driver = Driver.query.get_or_404(driver_id)

        # إنشاء دفعة جديدة
        new_payment = Payment(
            driver_id=driver_id,
            amount=amount,
            payment_method=payment_method,
            notes=notes,
            payment_date=datetime.now()
        )

        # تحديث آخر دفعة للسائق
        driver.last_payment_date = datetime.now()

        # حساب تاريخ الدفع التالي
        if driver.payment_type == 'شهري':
            driver.next_payment_due = datetime.now() + timedelta(days=30)
        elif driver.payment_type == 'أسبوعي':
            driver.next_payment_due = datetime.now() + timedelta(days=7)

        # تحديث حالة الدفع
        if driver.next_payment_due > datetime.now():
            driver.payment_status = 'ملتزم'

        db.session.add(new_payment)
        db.session.commit()

        flash(f'تم تسجيل دفعة بمبلغ {amount} ريال للسائق {driver.name}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تسجيل الدفعة: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# حذف دفعة
@app.route('/payments/delete/<int:payment_id>', methods=['POST'])
@login_required
def delete_payment(payment_id):
    """حذف دفعة"""
    try:
        payment = Payment.query.get_or_404(payment_id)
        driver_name = payment.driver.name if payment.driver else 'غير معروف'
        amount = payment.amount

        db.session.delete(payment)
        db.session.commit()

        flash(f'تم حذف دفعة بمبلغ {amount} ريال للسائق {driver_name}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الدفعة: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# تحديث حالات الدفع للسائقين
@app.route('/payments/update-status', methods=['POST'])
@login_required
def update_payment_status():
    """تحديث حالات الدفع للسائقين"""
    try:
        current_date = datetime.now()
        updated_count = 0

        # البحث عن السائقين المتأخرين
        overdue_drivers = Driver.query.filter(Driver.next_payment_due < current_date).all()

        for driver in overdue_drivers:
            days_overdue = (current_date - driver.next_payment_due).days

            if days_overdue > 7:
                driver.payment_status = 'متأخر'
            elif days_overdue > 3:
                driver.payment_status = 'متوسط'
            else:
                driver.payment_status = 'ملتزم'

            updated_count += 1

        db.session.commit()
        flash(f'تم تحديث حالة {updated_count} سائق', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث الحالات: {str(e)}', 'error')

    return redirect(url_for('payments_list'))

# صفحة التقارير
@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير والإحصائيات"""
    # إحصائيات عامة
    total_drivers = Driver.query.count()
    active_drivers = Driver.query.filter_by(status='نشط').count()
    inactive_drivers = Driver.query.filter_by(status='غير نشط').count()

    # إحصائيات المدفوعات
    total_payments = Payment.query.count()
    total_amount = db.session.query(db.func.sum(Payment.amount)).scalar() or 0

    # إحصائيات شهرية
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_payments = Payment.query.filter(
        db.extract('month', Payment.payment_date) == current_month,
        db.extract('year', Payment.payment_date) == current_year
    ).count()
    monthly_amount = db.session.query(db.func.sum(Payment.amount)).filter(
        db.extract('month', Payment.payment_date) == current_month,
        db.extract('year', Payment.payment_date) == current_year
    ).scalar() or 0

    # السائقين المتأخرين
    overdue_drivers = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()

    # أفضل السائقين (حسب عدد المدفوعات)
    top_drivers = db.session.query(
        Driver.name,
        db.func.count(Payment.id).label('payment_count'),
        db.func.sum(Payment.amount).label('total_amount')
    ).join(Payment).group_by(Driver.id).order_by(db.desc('payment_count')).limit(5).all()

    # إحصائيات طرق الدفع
    payment_methods = db.session.query(
        Payment.payment_method,
        db.func.count(Payment.id).label('count'),
        db.func.sum(Payment.amount).label('total')
    ).group_by(Payment.payment_method).all()

    # إحصائيات شهرية للرسم البياني (آخر 6 أشهر)
    monthly_stats = []
    for i in range(6):
        month_date = datetime.now() - timedelta(days=30*i)
        month_payments = Payment.query.filter(
            db.extract('month', Payment.payment_date) == month_date.month,
            db.extract('year', Payment.payment_date) == month_date.year
        ).count()
        month_amount = db.session.query(db.func.sum(Payment.amount)).filter(
            db.extract('month', Payment.payment_date) == month_date.month,
            db.extract('year', Payment.payment_date) == month_date.year
        ).scalar() or 0

        monthly_stats.append({
            'month': month_date.strftime('%Y-%m'),
            'month_name': month_date.strftime('%B %Y'),
            'payments': month_payments,
            'amount': float(month_amount)
        })

    monthly_stats.reverse()  # ترتيب من الأقدم للأحدث

    return render_template('reports.html',
                         total_drivers=total_drivers,
                         active_drivers=active_drivers,
                         inactive_drivers=inactive_drivers,
                         total_payments=total_payments,
                         total_amount=total_amount,
                         monthly_payments=monthly_payments,
                         monthly_amount=monthly_amount,
                         overdue_drivers=overdue_drivers,
                         top_drivers=top_drivers,
                         payment_methods=payment_methods,
                         monthly_stats=monthly_stats)

# تصدير التقارير
@app.route('/reports/export/<report_type>')
@login_required
def export_report(report_type):
    """تصدير التقارير بصيغ مختلفة"""
    try:
        if report_type == 'drivers':
            # تصدير قائمة السائقين
            drivers = Driver.query.all()
            # هنا يمكن إضافة كود تصدير CSV أو Excel
            flash('سيتم تطوير ميزة التصدير قريباً', 'info')

        elif report_type == 'payments':
            # تصدير المدفوعات
            payments = Payment.query.all()
            flash('سيتم تطوير ميزة التصدير قريباً', 'info')

        elif report_type == 'overdue':
            # تصدير المتأخرين
            overdue = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()
            flash('سيتم تطوير ميزة التصدير قريباً', 'info')

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')

    return redirect(url_for('reports'))

# صفحة الخريطة
@app.route('/map')
@login_required
def map_view():
    """صفحة الخريطة مع مواقع السائقين"""
    # جلب السائقين الذين لديهم مواقع
    drivers_with_location = Driver.query.filter(
        Driver.current_latitude.isnot(None),
        Driver.current_longitude.isnot(None)
    ).all()

    # جلب جميع السائقين للإحصائيات
    all_drivers = Driver.query.all()
    active_drivers = Driver.query.filter_by(status='نشط').all()

    # إحصائيات المواقع
    drivers_with_gps = len(drivers_with_location)
    drivers_without_gps = len(all_drivers) - drivers_with_gps

    return render_template('map.html',
                         drivers=drivers_with_location,
                         all_drivers=all_drivers,
                         active_drivers=active_drivers,
                         drivers_with_gps=drivers_with_gps,
                         drivers_without_gps=drivers_without_gps)

# إضافة موقع جديد
@app.route('/map/add-location', methods=['POST'])
@login_required
def add_location():
    """إضافة موقع جديد للسائق"""
    try:
        driver_id = int(request.form.get('driver_id'))
        latitude = float(request.form.get('latitude'))
        longitude = float(request.form.get('longitude'))
        address = request.form.get('address', '')

        driver = Driver.query.get_or_404(driver_id)

        # تحديث موقع السائق
        driver.current_latitude = latitude
        driver.current_longitude = longitude
        driver.current_address = address
        driver.updated_at = datetime.now()

        db.session.commit()

        flash(f'تم إضافة موقع جديد للسائق {driver.name}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة الموقع: {str(e)}', 'error')

    return redirect(url_for('map_view'))

# البحث عن مواقع قريبة
@app.route('/map/nearby', methods=['POST'])
@login_required
def find_nearby_drivers():
    """البحث عن السائقين القريبين من موقع معين"""
    try:
        center_lat = float(request.form.get('latitude'))
        center_lng = float(request.form.get('longitude'))
        radius = float(request.form.get('radius', 5))  # نصف القطر بالكيلومتر

        # استعلام للبحث عن السائقين القريبين
        nearby_drivers = Driver.query.filter(
            Driver.current_latitude.isnot(None),
            Driver.current_longitude.isnot(None),
            Driver.status == 'نشط'
        ).all()

        # فلترة السائقين حسب المسافة (حساب مبسط)
        filtered_drivers = []
        for driver in nearby_drivers:
            if driver.current_latitude and driver.current_longitude:
                # حساب المسافة التقريبية
                lat_diff = abs(driver.current_latitude - center_lat)
                lng_diff = abs(driver.current_longitude - center_lng)
                distance = ((lat_diff ** 2) + (lng_diff ** 2)) ** 0.5

                # تحويل تقريبي للكيلومترات (1 درجة ≈ 111 كم)
                distance_km = distance * 111

                if distance_km <= radius:
                    filtered_drivers.append({
                        'id': driver.id,
                        'name': driver.name,
                        'phone': driver.phone,
                        'latitude': driver.current_latitude,
                        'longitude': driver.current_longitude,
                        'distance': round(distance_km, 2)
                    })

        return render_template('map.html',
                             drivers=Driver.query.filter(
                                 Driver.current_latitude.isnot(None),
                                 Driver.current_longitude.isnot(None)
                             ).all(),
                             nearby_drivers=filtered_drivers,
                             search_center={'lat': center_lat, 'lng': center_lng, 'radius': radius})

    except Exception as e:
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'error')
        return redirect(url_for('map_view'))

# صفحة الإشعارات
@app.route('/notifications')
@login_required
def notifications():
    """صفحة إدارة الإشعارات"""
    # جلب السائقين المتأخرين في الدفع
    overdue_drivers = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()

    # جلب السائقين الذين يستحقون دفعة قريباً (خلال 3 أيام)
    upcoming_due = Driver.query.filter(
        Driver.next_payment_due > datetime.now(),
        Driver.next_payment_due <= datetime.now() + timedelta(days=3)
    ).all()

    # إحصائيات الإشعارات
    total_overdue = len(overdue_drivers)
    total_upcoming = len(upcoming_due)

    return render_template('notifications.html',
                         overdue_drivers=overdue_drivers,
                         upcoming_due=upcoming_due,
                         total_overdue=total_overdue,
                         total_upcoming=total_upcoming)

# إرسال إشعار WhatsApp
@app.route('/notifications/whatsapp', methods=['POST'])
@login_required
def send_whatsapp_notification():
    """إرسال إشعار عبر WhatsApp"""
    try:
        driver_id = request.form.get('driver_id')
        message = request.form.get('message')

        driver = Driver.query.get_or_404(driver_id)

        # هنا يمكن إضافة تكامل مع WhatsApp Business API
        # مثال: استخدام Twilio أو WhatsApp Business API

        # محاكاة إرسال الرسالة
        whatsapp_url = f"https://wa.me/{driver.phone}?text={message}"

        flash(f'تم إنشاء رابط WhatsApp للسائق {driver.name}', 'success')

        # يمكن إضافة سجل للإشعارات المرسلة هنا

    except Exception as e:
        flash(f'حدث خطأ أثناء إرسال الإشعار: {str(e)}', 'error')

    return redirect(url_for('notifications'))

# إرسال إشعار SMS
@app.route('/notifications/sms', methods=['POST'])
@login_required
def send_sms_notification():
    """إرسال إشعار عبر SMS"""
    try:
        driver_id = request.form.get('driver_id')
        message = request.form.get('message')

        driver = Driver.query.get_or_404(driver_id)

        # هنا يمكن إضافة تكامل مع خدمة SMS
        # مثال: استخدام Twilio أو خدمة SMS محلية

        flash(f'سيتم تطوير إرسال SMS للسائق {driver.name} قريباً', 'info')

    except Exception as e:
        flash(f'حدث خطأ أثناء إرسال SMS: {str(e)}', 'error')

    return redirect(url_for('notifications'))

# إرسال إشعارات جماعية
@app.route('/notifications/bulk', methods=['POST'])
@login_required
def send_bulk_notifications():
    """إرسال إشعارات جماعية للسائقين المتأخرين"""
    try:
        notification_type = request.form.get('type')  # whatsapp, sms, email
        message_template = request.form.get('message')

        overdue_drivers = Driver.query.filter(Driver.next_payment_due < datetime.now()).all()

        sent_count = 0
        for driver in overdue_drivers:
            if driver.phone:
                # تخصيص الرسالة لكل سائق
                personalized_message = message_template.replace('{name}', driver.name)
                personalized_message = personalized_message.replace('{amount}', str(driver.payment_amount))

                # هنا يتم إرسال الإشعار حسب النوع المحدد
                if notification_type == 'whatsapp':
                    # إرسال WhatsApp
                    pass
                elif notification_type == 'sms':
                    # إرسال SMS
                    pass

                sent_count += 1

        flash(f'تم إرسال {sent_count} إشعار بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ أثناء الإرسال الجماعي: {str(e)}', 'error')

    return redirect(url_for('notifications'))

# صفحة الإعدادات
@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات الرئيسية"""
    # جلب الإعدادات حسب الفئة
    general_settings = Settings.query.filter_by(category='general').all()
    notification_settings = Settings.query.filter_by(category='notifications').all()
    payment_settings = Settings.query.filter_by(category='payments').all()

    # إنشاء الإعدادات الافتراضية إذا لم تكن موجودة
    default_settings = [
        # الإعدادات العامة
        ('company_name', 'شركة النقل المتطورة', 'general', 'اسم الشركة', 'string'),
        ('company_logo', '', 'general', 'شعار الشركة', 'string'),
        ('currency', 'ريال سعودي', 'general', 'العملة المستخدمة', 'string'),
        ('timezone', 'Asia/Riyadh', 'general', 'المنطقة الزمنية', 'string'),
        ('language', 'ar', 'general', 'لغة النظام', 'string'),

        # إعدادات الإشعارات
        ('whatsapp_enabled', 'true', 'notifications', 'تفعيل إشعارات WhatsApp', 'boolean'),
        ('sms_enabled', 'false', 'notifications', 'تفعيل إشعارات SMS', 'boolean'),
        ('email_enabled', 'false', 'notifications', 'تفعيل إشعارات البريد الإلكتروني', 'boolean'),
        ('notification_reminder_days', '3', 'notifications', 'أيام التذكير قبل الاستحقاق', 'integer'),

        # إعدادات المدفوعات
        ('default_payment_type', 'شهري', 'payments', 'نوع الدفع الافتراضي', 'string'),
        ('late_fee_enabled', 'false', 'payments', 'تفعيل رسوم التأخير', 'boolean'),
        ('late_fee_amount', '50', 'payments', 'مبلغ رسوم التأخير', 'float'),
        ('grace_period_days', '7', 'payments', 'فترة السماح بالأيام', 'integer'),
    ]

    for key, value, category, description, data_type in default_settings:
        if not Settings.query.filter_by(key=key).first():
            Settings.set_setting(key, value, category, description, data_type)

    # إعادة جلب الإعدادات بعد إنشاء الافتراضية
    general_settings = Settings.query.filter_by(category='general').all()
    notification_settings = Settings.query.filter_by(category='notifications').all()
    payment_settings = Settings.query.filter_by(category='payments').all()

    return render_template('settings.html',
                         general_settings=general_settings,
                         notification_settings=notification_settings,
                         payment_settings=payment_settings)

# تحديث الإعدادات
@app.route('/settings/update', methods=['POST'])
@login_required
def update_settings():
    """تحديث الإعدادات"""
    try:
        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='update_settings',
            resource_type='settings',
            details={'updated_by': current_user.username},
            request_obj=request
        )

        # تحديث كل إعداد
        for key, value in request.form.items():
            if key.startswith('setting_'):
                setting_key = key.replace('setting_', '')
                setting = Settings.query.filter_by(key=setting_key).first()
                if setting:
                    setting.set_value(value)
                    setting.updated_at = datetime.utcnow()

        db.session.commit()
        flash('تم تحديث الإعدادات بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث الإعدادات: {str(e)}', 'error')

    return redirect(url_for('settings'))

# إدارة المستخدمين
@app.route('/settings/users')
@login_required
def manage_users():
    """إدارة المستخدمين والصلاحيات"""
    if not current_user.has_permission('admin'):
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    users = User.query.all()
    roles = Role.query.all()

    # إنشاء الأدوار الافتراضية إذا لم تكن موجودة
    default_roles = [
        ('admin', 'مدير النظام', 'صلاحيات كاملة لإدارة النظام', ['create', 'read', 'update', 'delete', 'admin', 'backup', 'settings']),
        ('supervisor', 'مشرف', 'إدارة السائقين والمدفوعات', ['create', 'read', 'update', 'reports']),
        ('viewer', 'مشاهد', 'عرض البيانات فقط', ['read'])
    ]

    for name, display_name, description, permissions in default_roles:
        if not Role.query.filter_by(name=name).first():
            role = Role(name=name, display_name=display_name, description=description, is_system=True)
            role.set_permissions(permissions)
            db.session.add(role)

    db.session.commit()

    # إعادة جلب الأدوار
    roles = Role.query.all()

    return render_template('settings/users.html', users=users, roles=roles)

# إضافة مستخدم جديد
@app.route('/add_user', methods=['POST'])
@login_required
def add_user():
    """إضافة مستخدم جديد"""
    try:
        username = request.form.get('username')
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role_id = request.form.get('role_id')
        is_active = 'is_active' in request.form

        # التحقق من صحة البيانات
        if not username or not password:
            flash('اسم المستخدم وكلمة المرور مطلوبان', 'danger')
            return redirect(url_for('manage_users'))

        if password != confirm_password:
            flash('كلمة المرور وتأكيدها غير متطابقتان', 'danger')
            return redirect(url_for('manage_users'))

        # التحقق من عدم وجود المستخدم
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return redirect(url_for('manage_users'))

        # إنشاء المستخدم الجديد
        new_user = User(
            username=username,
            full_name=full_name,
            email=email,
            is_active=is_active,
            role_id=int(role_id) if role_id else None,
            created_at=datetime.utcnow()
        )
        new_user.set_password(password)

        db.session.add(new_user)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='إضافة مستخدم جديد',
            resource_type='user',
            resource_id=new_user.id,
            details=f'اسم المستخدم: {username}',
            request_obj=request
        )

        flash(f'تم إضافة المستخدم {username} بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في إضافة المستخدم: {str(e)}', 'danger')

    return redirect(url_for('manage_users'))

# إضافة دور جديد
@app.route('/add_role', methods=['POST'])
@login_required
def add_role():
    """إضافة دور جديد"""
    try:
        name = request.form.get('name')
        description = request.form.get('description')
        permissions = request.form.getlist('permissions')

        if not name:
            flash('اسم الدور مطلوب', 'danger')
            return redirect(url_for('manage_users'))

        # التحقق من عدم وجود الدور
        if Role.query.filter_by(name=name).first():
            flash('اسم الدور موجود بالفعل', 'danger')
            return redirect(url_for('manage_users'))

        # إنشاء الدور الجديد
        new_role = Role(
            name=name,
            description=description,
            permissions=json.dumps(permissions) if permissions else None,
            created_at=datetime.utcnow()
        )

        db.session.add(new_role)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='إضافة دور جديد',
            resource_type='role',
            resource_id=new_role.id,
            details=f'اسم الدور: {name}',
            request_obj=request
        )

        flash(f'تم إضافة الدور {name} بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في إضافة الدور: {str(e)}', 'danger')

    return redirect(url_for('manage_users'))

# تغيير حالة المستخدم
@app.route('/toggle_user_status/<int:user_id>', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    """تغيير حالة المستخدم (نشط/غير نشط)"""
    try:
        user = User.query.get_or_404(user_id)

        if user.id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك تغيير حالة حسابك الخاص'})

        user.is_active = not user.is_active
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action=f'تغيير حالة المستخدم إلى {"نشط" if user.is_active else "غير نشط"}',
            resource_type='user',
            resource_id=user.id,
            details=f'المستخدم: {user.username}',
            request_obj=request
        )

        return jsonify({'success': True, 'message': 'تم تغيير حالة المستخدم بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# حذف مستخدم
@app.route('/delete_user/<int:user_id>', methods=['DELETE'])
@login_required
def delete_user(user_id):
    """حذف مستخدم"""
    try:
        user = User.query.get_or_404(user_id)

        if user.id == current_user.id:
            return jsonify({'success': False, 'message': 'لا يمكنك حذف حسابك الخاص'})

        username = user.username
        db.session.delete(user)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='حذف مستخدم',
            resource_type='user',
            details=f'المستخدم المحذوف: {username}',
            request_obj=request
        )

        return jsonify({'success': True, 'message': f'تم حذف المستخدم {username} بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# حذف دور
@app.route('/delete_role/<int:role_id>', methods=['DELETE'])
@login_required
def delete_role(role_id):
    """حذف دور"""
    try:
        role = Role.query.get_or_404(role_id)

        # التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
        if role.users:
            return jsonify({'success': False, 'message': 'لا يمكن حذف دور مرتبط بمستخدمين'})

        role_name = role.name
        db.session.delete(role)
        db.session.commit()

        # تسجيل النشاط
        ActivityLog.log_activity(
            user_id=current_user.id,
            action='حذف دور',
            resource_type='role',
            details=f'الدور المحذوف: {role_name}',
            request_obj=request
        )

        return jsonify({'success': True, 'message': f'تم حذف الدور {role_name} بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# إنشاء نسخة احتياطية
@app.route('/create_backup', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        import shutil
        import os
        from datetime import datetime

        # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)

        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)

        # نسخ قاعدة البيانات
        db_path = 'driver_management.db'
        if os.path.exists(db_path):
            shutil.copy2(db_path, backup_path)

            # تسجيل النسخة الاحتياطية في قاعدة البيانات
            backup_log = BackupLog(
                filename=backup_filename,
                file_path=backup_path,
                file_size=os.path.getsize(backup_path),
                created_by=current_user.id,
                created_at=datetime.utcnow()
            )
            db.session.add(backup_log)
            db.session.commit()

            # تسجيل النشاط
            ActivityLog.log_activity(
                user_id=current_user.id,
                action='إنشاء نسخة احتياطية',
                resource_type='backup',
                resource_id=backup_log.id,
                details=f'ملف النسخة: {backup_filename}',
                request_obj=request
            )

            return jsonify({'success': True, 'message': 'تم إنشاء النسخة الاحتياطية بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'لم يتم العثور على قاعدة البيانات'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# صفحة الملف الشخصي
@app.route('/profile')
@login_required
def profile():
    """صفحة الملف الشخصي"""
    return render_template('settings.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='مدير',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
# ==================== Routes التصدير ====================

@app.route('/export/drivers/excel')
@login_required
def export_drivers_excel():
    """تصدير قائمة السائقين إلى Excel"""
    try:
        # الحصول على المعاملات
        status_filter = request.args.get('status', '')
        payment_type_filter = request.args.get('payment_type', '')

        # بناء الاستعلام
        query = Driver.query
        if status_filter:
            query = query.filter(Driver.status == status_filter)
        if payment_type_filter:
            query = query.filter(Driver.payment_type == payment_type_filter)

        drivers = query.all()

        # إنشاء مصدر Excel
        exporter = ExcelExporter()
        filepath = exporter.export_drivers_list(drivers, status_filter, payment_type_filter)

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير قائمة السائقين إلى Excel - عدد السائقين: {len(drivers)}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('drivers_list'))

@app.route('/export/payments/excel')
@login_required
def export_payments_excel():
    """تصدير تقرير المدفوعات إلى Excel"""
    try:
        # الحصول على المعاملات
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        start_date = None
        end_date = None

        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')

        # استعلام المدفوعات
        query = Payment.query
        if start_date:
            query = query.filter(Payment.payment_date >= start_date)
        if end_date:
            query = query.filter(Payment.payment_date <= end_date)

        payments = query.all()

        # إنشاء مصدر Excel
        exporter = ExcelExporter()
        filepath = exporter.export_payments_report(payments, start_date, end_date)

        # تسجيل النشاط
        period_text = ""
        if start_date and end_date:
            period_text = f" من {start_date.strftime('%Y/%m/%d')} إلى {end_date.strftime('%Y/%m/%d')}"
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير تقرير المدفوعات إلى Excel{period_text}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('reports'))

@app.route('/export/driver/<int:driver_id>/pdf')
@login_required
def export_driver_pdf(driver_id):
    """تصدير تقرير سائق فردي إلى PDF"""
    try:
        # إنشاء مصدر PDF
        exporter = PDFExporter()
        filepath = exporter.export_driver_report(driver_id)

        driver = Driver.query.get_or_404(driver_id)

        # تسجيل النشاط
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير تقرير السائق {driver.name} إلى PDF',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('drivers_list'))

@app.route('/export/financial/excel')
@login_required
def export_financial_excel():
    """تصدير التقرير المالي الشامل إلى Excel"""
    try:
        # الحصول على المعاملات
        year = request.args.get('year', datetime.now().year, type=int)
        month = request.args.get('month', type=int)

        # استعلام البيانات
        drivers = Driver.query.all()

        # استعلام المدفوعات حسب السنة والشهر
        payments_query = Payment.query
        if year:
            payments_query = payments_query.filter(db.extract('year', Payment.payment_date) == year)
        if month:
            payments_query = payments_query.filter(db.extract('month', Payment.payment_date) == month)

        payments = payments_query.all()

        # إنشاء مصدر Excel
        exporter = ExcelExporter()
        filepath = exporter.export_financial_report(drivers, payments, year, month)

        # تسجيل النشاط
        period_text = f"سنة {year}"
        if month:
            period_text += f" شهر {month}"
        activity_log = ActivityLog(
            user_id=current_user.id,
            action=f'تصدير التقرير المالي الشامل إلى Excel - {period_text}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity_log)
        db.session.commit()

        return send_file(filepath, as_attachment=True,
                        download_name=os.path.basename(filepath))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('reports'))

@app.route('/export')
@login_required
def export_center():
    """مركز التصدير - صفحة اختيار نوع التصدير"""
    drivers_count = Driver.query.count()
    payments_count = Payment.query.count()

    # إحصائيات سريعة
    stats = {
        'total_drivers': drivers_count,
        'active_drivers': Driver.query.filter_by(status='نشط').count(),
        'total_payments': payments_count,
        'this_month_payments': Payment.query.filter(
            Payment.payment_date >= datetime.now().replace(day=1)
        ).count()
    }

    return render_template('export_center.html', stats=stats)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
