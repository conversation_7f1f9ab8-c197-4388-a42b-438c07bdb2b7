#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة السائقين المتطور
Driver Management System with AI Features
"""

import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime, timedelta
import json

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///driver_management.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))

# إعداد مجلدات الرفع
app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', 'uploads')
app.config['REPORTS_FOLDER'] = os.getenv('REPORTS_FOLDER', 'reports')
app.config['DOCUMENTS_FOLDER'] = os.getenv('DOCUMENTS_FOLDER', 'documents')

# إنشاء المجلدات إذا لم تكن موجودة
for folder in [app.config['UPLOAD_FOLDER'], app.config['REPORTS_FOLDER'], app.config['DOCUMENTS_FOLDER']]:
    os.makedirs(folder, exist_ok=True)

# استيراد النماذج أولاً
from models import db, User, Driver, Payment, Document, AIAnalysis

# تهيئة قاعدة البيانات مع التطبيق
db.init_app(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    # إحصائيات أساسية
    total_drivers = Driver.query.count()
    active_drivers = Driver.query.filter_by(status='نشط').count()
    late_drivers = Driver.query.filter_by(payment_status='متأخر').count()

    # إحصائيات المدفوعات
    total_revenue = db.session.query(db.func.sum(Payment.amount)).scalar() or 0
    this_month_revenue = db.session.query(db.func.sum(Payment.amount)).filter(
        Payment.payment_date >= datetime.now().replace(day=1)
    ).scalar() or 0

    # السائقين الأحدث
    recent_drivers = Driver.query.order_by(Driver.created_at.desc()).limit(5).all()

    # المدفوعات الأخيرة
    recent_payments = Payment.query.order_by(Payment.payment_date.desc()).limit(5).all()
    
    return render_template('dashboard.html',
                         total_drivers=total_drivers,
                         active_drivers=active_drivers,
                         late_drivers=late_drivers,
                         total_revenue=total_revenue,
                         this_month_revenue=this_month_revenue,
                         recent_drivers=recent_drivers,
                         recent_payments=recent_payments)

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('login'))

# صفحة السائقين
@app.route('/drivers')
@login_required
def drivers_list():
    """قائمة السائقين"""
    drivers = Driver.query.all()
    return render_template('drivers.html', drivers=drivers)

# صفحة المدفوعات
@app.route('/payments')
@login_required
def payments_list():
    """قائمة المدفوعات"""
    payments = Payment.query.order_by(Payment.payment_date.desc()).all()
    return render_template('payments.html', payments=payments)

# صفحة التقارير
@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير"""
    return render_template('reports.html')

# صفحة الخريطة
@app.route('/map')
@login_required
def map_view():
    """عرض الخريطة"""
    drivers = Driver.query.filter(Driver.latitude.isnot(None), Driver.longitude.isnot(None)).all()
    return render_template('map.html', drivers=drivers)

# صفحة الإعدادات
@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات"""
    return render_template('settings.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='مدير',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
