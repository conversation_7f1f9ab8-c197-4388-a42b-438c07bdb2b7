{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/%D9%85%D9%84%D9%81%20%D8%B4%D9%87%D9%8A%D8%AF%20%D9%85%D9%88%D8%B3%D9%89%20%D8%B3%D9%84%D8%B7%D8%A7%D9%86/%D9%85%D9%88%D9%82%D8%B9%20%D8%AC%D8%AF%D9%8A%D8%AF/transport-management/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { \n  Home, \n  Users, \n  Car, \n  TrendingUp, \n  Settings,\n  Menu,\n  X\n} from 'lucide-react'\nimport { useState } from 'react'\n\nconst navigation = [\n  { name: 'الرئيسية', href: '/', icon: Home },\n  { name: 'السائقين', href: '/drivers', icon: Users },\n  { name: 'الرحلات', href: '/trips', icon: Car },\n  { name: 'التقارير', href: '/reports', icon: TrendingUp },\n  { name: 'الإعدادات', href: '/settings', icon: Settings },\n]\n\nexport default function Navigation() {\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  return (\n    <>\n      {/* شريط التنقل للشاشات الكبيرة */}\n      <nav className=\"hidden md:flex bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Car className=\"h-8 w-8 text-blue-600 ml-3\" />\n              <h1 className=\"text-xl font-bold text-gray-900\">نظام إدارة النقل</h1>\n            </div>\n            \n            <div className=\"flex space-x-8 space-x-reverse\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                    }`}\n                  >\n                    <item.icon className=\"ml-2\" size={18} />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* شريط التنقل للشاشات الصغيرة */}\n      <nav className=\"md:hidden bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Car className=\"h-8 w-8 text-blue-600 ml-3\" />\n              <h1 className=\"text-lg font-bold text-gray-900\">نظام إدارة النقل</h1>\n            </div>\n            \n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50\"\n            >\n              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </button>\n          </div>\n        </div>\n\n        {/* القائمة المنسدلة للشاشات الصغيرة */}\n        {isMobileMenuOpen && (\n          <div className=\"border-t border-gray-200\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                    }`}\n                  >\n                    <item.icon className=\"ml-3\" size={20} />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n        )}\n      </nav>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAbA;;;;;;AAeA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IAC1C;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAW,MAAM;QAAU,MAAM,gMAAA,CAAA,MAAG;IAAC;IAC7C;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,kNAAA,CAAA,aAAU;IAAC;IACvD;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACxD;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,sDACJ;;0DAEF,8OAAC,KAAK,IAAI;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CACjC,KAAK,IAAI;;uCATL,KAAK,IAAI;;;;;gCAYpB;;;;;;;;;;;;;;;;;;;;;;0BAOR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;;8CAGlD,8OAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;8CAET,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;oBAMvD,kCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAW,CAAC,+EAA+E,EACzF,WACI,8BACA,sDACJ;;sDAEF,8OAAC,KAAK,IAAI;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCACjC,KAAK,IAAI;;mCAVL,KAAK,IAAI;;;;;4BAapB;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}]}