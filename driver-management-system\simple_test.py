#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للنظام
"""

import requests
import sys

def test_system():
    """اختبار النظام"""
    base_url = "http://localhost:5000"
    
    print("🔍 اختبار النظام...")
    
    # اختبار الصفحات الأساسية
    pages = [
        ("/", "الصفحة الرئيسية"),
        ("/login", "صفحة تسجيل الدخول"),
        ("/drivers", "صفحة السائقين"),
        ("/payments", "صفحة المدفوعات"),
        ("/reports", "صفحة التقارير"),
        ("/export", "مركز التصدير"),
        ("/settings", "صفحة الإعدادات")
    ]
    
    for url, name in pages:
        try:
            response = requests.get(f"{base_url}{url}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: يعمل")
            elif response.status_code == 302:
                print(f"🔄 {name}: إعادة توجيه (طبيعي للصفحات المحمية)")
            else:
                print(f"❌ {name}: خطأ {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: لا يمكن الاتصال بالخادم")
            return False
        except Exception as e:
            print(f"❌ {name}: خطأ - {str(e)}")
    
    # اختبار تسجيل الدخول
    print("\n🔍 اختبار تسجيل الدخول...")
    session = requests.Session()
    
    try:
        # الحصول على صفحة تسجيل الدخول
        login_page = session.get(f"{base_url}/login")
        if login_page.status_code != 200:
            print(f"❌ لا يمكن الوصول لصفحة تسجيل الدخول: {login_page.status_code}")
            return False
        
        # تسجيل الدخول
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            if "dashboard" in login_response.url or "drivers" in login_response.url:
                print("✅ تسجيل الدخول: نجح")
            else:
                print("⚠️ تسجيل الدخول: استجابة غير متوقعة")
        elif login_response.status_code == 302:
            print("✅ تسجيل الدخول: نجح (إعادة توجيه)")
        else:
            print(f"❌ تسجيل الدخول: فشل {login_response.status_code}")
            
        # اختبار صفحة محمية
        protected_page = session.get(f"{base_url}/drivers")
        if protected_page.status_code == 200:
            print("✅ الوصول للصفحات المحمية: يعمل")
        else:
            print(f"❌ الوصول للصفحات المحمية: خطأ {protected_page.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {str(e)}")
    
    print("\n✅ انتهى الاختبار")
    return True

if __name__ == "__main__":
    test_system()
