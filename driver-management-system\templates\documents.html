{% extends "base.html" %}

{% block title %}إدارة المستندات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="text-primary">
                    <i class="fas fa-file-alt me-2"></i>إدارة المستندات
                </h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="fas fa-upload me-2"></i>رفع مستند جديد
                </button>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('documents_list') }}">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="driver_id" class="form-label">السائق</label>
                                <select class="form-select" id="driver_id" name="driver_id">
                                    <option value="">جميع السائقين</option>
                                    {% for driver in drivers %}
                                    <option value="{{ driver.id }}" 
                                            {% if current_driver_id == driver.id %}selected{% endif %}>
                                        {{ driver.name }} - {{ driver.phone }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="document_type" class="form-label">نوع المستند</label>
                                <select class="form-select" id="document_type" name="document_type">
                                    <option value="">جميع الأنواع</option>
                                    {% for doc_type in document_types %}
                                    <option value="{{ doc_type }}" 
                                            {% if current_document_type == doc_type %}selected{% endif %}>
                                        {{ doc_type }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                                <a href="{{ url_for('documents_list') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المستندات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة المستندات</h5>
                </div>
                <div class="card-body">
                    {% if documents.items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>السائق</th>
                                    <th>نوع المستند</th>
                                    <th>اسم الملف</th>
                                    <th>حجم الملف</th>
                                    <th>تاريخ الرفع</th>
                                    <th>رفع بواسطة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for document in documents.items %}
                                <tr>
                                    <td>
                                        <strong>{{ document.driver.name }}</strong><br>
                                        <small class="text-muted">{{ document.driver.phone }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ document.document_type }}</span>
                                    </td>
                                    <td>{{ document.file_name }}</td>
                                    <td>{{ "%.1f"|format(document.file_size / 1024) }} KB</td>
                                    <td>{{ document.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if document.uploader %}
                                            {{ document.uploader.full_name }}
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('download_document', document_id=document.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% if current_user.has_permission('delete') %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete({{ document.id }}, '{{ document.file_name }}')" 
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التنقل بين الصفحات -->
                    {% if documents.pages > 1 %}
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if documents.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('documents_list', page=documents.prev_num, driver_id=current_driver_id, document_type=current_document_type) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in documents.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != documents.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('documents_list', page=page_num, driver_id=current_driver_id, document_type=current_document_type) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if documents.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('documents_list', page=documents.next_num, driver_id=current_driver_id, document_type=current_document_type) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مستندات</h5>
                        <p class="text-muted">لم يتم العثور على أي مستندات بالمعايير المحددة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة رفع مستند جديد -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">رفع مستند جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form action="{{ url_for('upload_document') }}" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="upload_driver_id" class="form-label">السائق <span class="text-danger">*</span></label>
                        <select class="form-select" id="upload_driver_id" name="driver_id" required>
                            <option value="">اختر السائق</option>
                            {% for driver in drivers %}
                            <option value="{{ driver.id }}">{{ driver.name }} - {{ driver.phone }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="upload_document_type" class="form-label">نوع المستند <span class="text-danger">*</span></label>
                        <select class="form-select" id="upload_document_type" name="document_type" required>
                            <option value="">اختر نوع المستند</option>
                            {% for doc_type in document_types %}
                            <option value="{{ doc_type }}">{{ doc_type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="document_file" class="form-label">الملف <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="document_file" name="document_file" 
                               accept=".pdf,.png,.jpg,.jpeg,.gif,.doc,.docx,.xlsx,.xls" required>
                        <div class="form-text">الأنواع المدعومة: PDF, صور, Word, Excel (حد أقصى 16 ميجابايت)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">رفع المستند</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستند <strong id="deleteFileName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(documentId, fileName) {
    document.getElementById('deleteFileName').textContent = fileName;
    document.getElementById('deleteForm').action = '/delete_document/' + documentId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// تحديد السائق المحدد مسبقاً في نافذة الرفع
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const driverId = urlParams.get('driver_id');
    if (driverId) {
        document.getElementById('upload_driver_id').value = driverId;
    }
});
</script>
{% endblock %}
