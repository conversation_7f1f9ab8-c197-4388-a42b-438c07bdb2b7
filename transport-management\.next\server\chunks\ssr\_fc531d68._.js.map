{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/%D9%85%D9%84%D9%81%20%D8%B4%D9%87%D9%8A%D8%AF%20%D9%85%D9%88%D8%B3%D9%89%20%D8%B3%D9%84%D8%B7%D8%A7%D9%86/%D9%85%D9%88%D9%82%D8%B9%20%D8%AC%D8%AF%D9%8A%D8%AF/transport-management/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  Users,\n  Car,\n  TrendingUp,\n  Clock,\n  DollarSign,\n  MapPin,\n  Phone,\n  Calendar\n} from 'lucide-react'\n\n// مكونات الإحصائيات\nfunction StatCard({ title, value, icon: Icon, color = \"blue\" }: {\n  title: string\n  value: string | number\n  icon: any\n  color?: string\n}) {\n  const colorClasses = {\n    blue: \"bg-blue-500\",\n    green: \"bg-green-500\",\n    yellow: \"bg-yellow-500\",\n    purple: \"bg-purple-500\"\n  }\n\n  // تنسيق الأرقام بشكل ثابت لتجنب مشاكل الـ hydration\n  const formatValue = (val: string | number) => {\n    if (typeof val === 'number') {\n      return val.toLocaleString('en-US')\n    }\n    return val\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900\">{formatValue(value)}</p>\n        </div>\n        <div className={`p-3 rounded-full ${colorClasses[color as keyof typeof colorClasses]} text-white`}>\n          <Icon size={24} />\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون الرحلات الأخيرة\nfunction RecentTrips() {\n  const mockTrips = [\n    {\n      id: 1,\n      driver: \"أحمد محمد\",\n      from: \"الرياض\",\n      to: \"الدمام\",\n      fare: 250,\n      status: \"مكتملة\",\n      time: \"منذ ساعة\"\n    },\n    {\n      id: 2,\n      driver: \"محمد علي\",\n      from: \"جدة\",\n      to: \"مكة\",\n      fare: 80,\n      status: \"جارية\",\n      time: \"منذ 30 دقيقة\"\n    },\n    {\n      id: 3,\n      driver: \"سعد أحمد\",\n      from: \"الرياض\",\n      to: \"القصيم\",\n      fare: 180,\n      status: \"مكتملة\",\n      time: \"منذ ساعتين\"\n    }\n  ]\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الرحلات الأخيرة</h3>\n      <div className=\"space-y-4\">\n        {mockTrips.map((trip) => (\n          <div key={trip.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <div className=\"p-2 bg-blue-100 rounded-full\">\n                <Car size={16} className=\"text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"font-medium text-gray-900\">{trip.driver}</p>\n                <p className=\"text-sm text-gray-600 flex items-center\">\n                  <MapPin size={12} className=\"ml-1\" />\n                  {trip.from} ← {trip.to}\n                </p>\n              </div>\n            </div>\n            <div className=\"text-left\">\n              <p className=\"font-semibold text-green-600\">{trip.fare} ريال</p>\n              <p className=\"text-xs text-gray-500\">{trip.time}</p>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport default function Dashboard() {\n  const [stats, setStats] = useState({\n    driversCount: \"500\",\n    tripsCount: \"1,250\",\n    totalEarnings: \"125,000 ريال\",\n    activeTrips: \"25\"\n  })\n\n  return (\n    <div className=\"min-h-screen bg-gray-100\" dir=\"rtl\">\n      {/* المحتوى الرئيسي */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* الإحصائيات */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <StatCard\n            title=\"إجمالي السائقين\"\n            value={stats.driversCount}\n            icon={Users}\n            color=\"blue\"\n          />\n          <StatCard\n            title=\"إجمالي الرحلات\"\n            value={stats.tripsCount}\n            icon={Car}\n            color=\"green\"\n          />\n          <StatCard\n            title=\"إجمالي الأرباح\"\n            value={stats.totalEarnings}\n            icon={DollarSign}\n            color=\"yellow\"\n          />\n          <StatCard\n            title=\"الرحلات النشطة\"\n            value={stats.activeTrips}\n            icon={Clock}\n            color=\"purple\"\n          />\n        </div>\n\n        {/* الشبكة الرئيسية */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* الرحلات الأخيرة */}\n          <div className=\"lg:col-span-2\">\n            <RecentTrips />\n          </div>\n\n          {/* الإجراءات السريعة */}\n          <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الإجراءات السريعة</h3>\n            <div className=\"space-y-3\">\n              <button className=\"w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                <Users className=\"ml-2\" size={20} />\n                إضافة سائق جديد\n              </button>\n              <button className=\"w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                <Car className=\"ml-2\" size={20} />\n                إضافة رحلة جديدة\n              </button>\n              <button className=\"w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n                <TrendingUp className=\"ml-2\" size={20} />\n                عرض التقارير\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAcA,oBAAoB;AACpB,SAAS,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,QAAQ,MAAM,EAK3D;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,oDAAoD;IACpD,MAAM,cAAc,CAAC;QACnB,IAAI,OAAO,QAAQ,UAAU;YAC3B,OAAO,IAAI,cAAc,CAAC;QAC5B;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAoC,YAAY;;;;;;;;;;;;8BAE/D,8OAAC;oBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAmC,CAAC,WAAW,CAAC;8BAC/F,cAAA,8OAAC;wBAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKtB;AAEA,uBAAuB;AACvB,SAAS;IACP,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAE3B,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA6B,KAAK,MAAM;;;;;;0DACrD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC,0MAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAC3B,KAAK,IAAI;oDAAC;oDAAI,KAAK,EAAE;;;;;;;;;;;;;;;;;;;0CAI5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAgC,KAAK,IAAI;4CAAC;;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,IAAI;;;;;;;;;;;;;uBAfzC,KAAK,EAAE;;;;;;;;;;;;;;;;AAsB3B;AAEe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,cAAc;QACd,YAAY;QACZ,eAAe;QACf,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA2B,KAAI;kBAE5C,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAM;4BACN,OAAO,MAAM,YAAY;4BACzB,MAAM,oMAAA,CAAA,QAAK;4BACX,OAAM;;;;;;sCAER,8OAAC;4BACC,OAAM;4BACN,OAAO,MAAM,UAAU;4BACvB,MAAM,gMAAA,CAAA,MAAG;4BACT,OAAM;;;;;;sCAER,8OAAC;4BACC,OAAM;4BACN,OAAO,MAAM,aAAa;4BAC1B,MAAM,kNAAA,CAAA,aAAU;4BAChB,OAAM;;;;;;sCAER,8OAAC;4BACC,OAAM;4BACN,OAAO,MAAM,WAAW;4BACxB,MAAM,oMAAA,CAAA,QAAK;4BACX,OAAM;;;;;;;;;;;;8BAKV,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;;;;;;;;;sCAIH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;sDAGtC,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;sDAGpC,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///C:/Users/<USER>/Documents/%D9%85%D9%84%D9%81%20%D8%B4%D9%87%D9%8A%D8%AF%20%D9%85%D9%88%D8%B3%D9%89%20%D8%B3%D9%84%D8%B7%D8%A7%D9%86/%D9%85%D9%88%D9%82%D8%B9%20%D8%AC%D8%AF%D9%8A%D8%AF/transport-management/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "file": "dollar-sign.js", "sources": ["file:///C:/Users/<USER>/Documents/%D9%85%D9%84%D9%81%20%D8%B4%D9%87%D9%8A%D8%AF%20%D9%85%D9%88%D8%B3%D9%89%20%D8%B3%D9%84%D8%B7%D8%A7%D9%86/%D9%85%D9%88%D9%82%D8%B9%20%D8%AC%D8%AF%D9%8A%D8%AF/transport-management/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "file": "map-pin.js", "sources": ["file:///C:/Users/<USER>/Documents/%D9%85%D9%84%D9%81%20%D8%B4%D9%87%D9%8A%D8%AF%20%D9%85%D9%88%D8%B3%D9%89%20%D8%B3%D9%84%D8%B7%D8%A7%D9%86/%D9%85%D9%88%D9%82%D8%B9%20%D8%AC%D8%AF%D9%8A%D8%AF/transport-management/node_modules/lucide-react/src/icons/map-pin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0',\n      key: '1r0f0z',\n    },\n  ],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n];\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA0Ljk5My01LjUzOSAxMC4xOTMtNy4zOTkgMTEuNzk5YTEgMSAwIDAgMS0xLjIwMiAwQzkuNTM5IDIwLjE5MyA0IDE0Ljk5MyA0IDEwYTggOCAwIDAgMSAxNiAwIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('map-pin', __iconNode);\n\nexport default MapPin;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}