#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة سائقين تجريبيين
Add test drivers
"""

from app import app, db
from models import Driver
from datetime import datetime, timed<PERSON><PERSON>

def add_test_drivers():
    """إضافة سائقين تجريبيين"""
    
    with app.app_context():
        # التحقق من وجود سائقين
        existing_count = Driver.query.count()
        if existing_count > 0:
            print(f"يوجد {existing_count} سائق في قاعدة البيانات")
            return
        
        # بيانات السائقين التجريبية
        test_drivers = [
            {
                "name": "أحمد محمد السعد",
                "phone": "**********",
                "national_id": "**********",
                "license_number": "LIC001",
                "vehicle_type": "سيدان",
                "vehicle_plate": "أ ب ج 123",
                "payment_type": "شهري",
                "payment_amount": 2000.0,
                "status": "نشط",
                "current_latitude": 24.7136,
                "current_longitude": 46.6753,
                "current_address": "وسط الرياض"
            },
            {
                "name": "محمد عبدالله الأحمد",
                "phone": "**********",
                "national_id": "**********",
                "license_number": "LIC002",
                "vehicle_type": "SUV",
                "vehicle_plate": "د هـ و 456",
                "payment_type": "أسبوعي",
                "payment_amount": 500.0,
                "status": "نشط",
                "current_latitude": 24.7500,
                "current_longitude": 46.6900,
                "current_address": "شمال الرياض"
            },
            {
                "name": "عبدالرحمن صالح القحطاني",
                "phone": "**********",
                "national_id": "**********",
                "license_number": "LIC003",
                "vehicle_type": "شاحنة صغيرة",
                "vehicle_plate": "ز ح ط 789",
                "payment_type": "شهري",
                "payment_amount": 2500.0,
                "status": "نشط",
                "current_latitude": 24.6800,
                "current_longitude": 46.6600,
                "current_address": "جنوب الرياض"
            },
            {
                "name": "خالد فهد العتيبي",
                "phone": "**********",
                "national_id": "**********",
                "license_number": "LIC004",
                "vehicle_type": "سيدان",
                "vehicle_plate": "ي ك ل 012",
                "payment_type": "شهري",
                "payment_amount": 1800.0,
                "status": "معلق",
                "current_latitude": 24.7200,
                "current_longitude": 46.7200,
                "current_address": "شرق الرياض"
            },
            {
                "name": "سعد عبدالعزيز الدوسري",
                "phone": "**********",
                "national_id": "**********",
                "license_number": "LIC005",
                "vehicle_type": "باص صغير",
                "vehicle_plate": "م ن س 345",
                "payment_type": "أسبوعي",
                "payment_amount": 600.0,
                "status": "نشط",
                "current_latitude": 24.7000,
                "current_longitude": 46.6400,
                "current_address": "غرب الرياض"
            }
        ]
        
        # إضافة السائقين
        for driver_data in test_drivers:
            # حساب تاريخ الدفع القادم
            if driver_data["payment_type"] == "شهري":
                next_payment = datetime.now() + timedelta(days=30)
            else:  # أسبوعي
                next_payment = datetime.now() + timedelta(days=7)
            
            driver = Driver(
                name=driver_data["name"],
                phone=driver_data["phone"],
                national_id=driver_data["national_id"],
                license_number=driver_data["license_number"],
                vehicle_type=driver_data["vehicle_type"],
                vehicle_plate=driver_data["vehicle_plate"],
                payment_type=driver_data["payment_type"],
                payment_amount=driver_data["payment_amount"],
                status=driver_data["status"],
                current_latitude=driver_data["current_latitude"],
                current_longitude=driver_data["current_longitude"],
                current_address=driver_data["current_address"],
                next_payment_due=next_payment,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.session.add(driver)
            print(f"تم إضافة السائق: {driver_data['name']}")
        
        # حفظ التغييرات
        db.session.commit()
        print(f"تم إضافة {len(test_drivers)} سائق تجريبي بنجاح")

if __name__ == "__main__":
    add_test_drivers()
