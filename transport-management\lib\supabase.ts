import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// أنواع البيانات
export interface Driver {
  id: string
  name: string
  phone: string
  license_number: string
  vehicle_type: string
  vehicle_plate: string
  status: 'active' | 'inactive' | 'suspended'
  commission_rate: number
  total_earnings: number
  created_at: string
  updated_at: string
}

export interface Customer {
  id: string
  name: string
  phone: string
  email?: string
  address?: string
  total_trips: number
  created_at: string
  updated_at: string
}

export interface Trip {
  id: string
  driver_id: string
  customer_id?: string
  pickup_location: string
  destination: string
  distance?: number
  fare: number
  commission: number
  driver_earnings: number
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  trip_date: string
  completed_at?: string
  notes?: string
  created_at: string
  updated_at: string
  driver?: Driver
  customer?: Customer
}

export interface Expense {
  id: string
  driver_id: string
  type: string
  amount: number
  description?: string
  expense_date: string
  created_at: string
  driver?: Driver
}

export interface Setting {
  id: string
  key: string
  value: string
  description?: string
  updated_at: string
}

// دوال قاعدة البيانات للسائقين
export const driversAPI = {
  // جلب جميع السائقين
  async getAll() {
    const { data, error } = await supabase
      .from('drivers')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data as Driver[]
  },

  // جلب سائق بالمعرف
  async getById(id: string) {
    const { data, error } = await supabase
      .from('drivers')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data as Driver
  },

  // إضافة سائق جديد
  async create(driver: Omit<Driver, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('drivers')
      .insert([driver])
      .select()
      .single()
    
    if (error) throw error
    return data as Driver
  },

  // تحديث سائق
  async update(id: string, updates: Partial<Driver>) {
    const { data, error } = await supabase
      .from('drivers')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data as Driver
  },

  // حذف سائق
  async delete(id: string) {
    const { error } = await supabase
      .from('drivers')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

// دوال قاعدة البيانات للرحلات
export const tripsAPI = {
  // جلب جميع الرحلات مع بيانات السائق والعميل
  async getAll() {
    const { data, error } = await supabase
      .from('trips')
      .select(`
        *,
        driver:drivers(*),
        customer:customers(*)
      `)
      .order('trip_date', { ascending: false })
    
    if (error) throw error
    return data as Trip[]
  },

  // جلب رحلات سائق معين
  async getByDriverId(driverId: string) {
    const { data, error } = await supabase
      .from('trips')
      .select(`
        *,
        customer:customers(*)
      `)
      .eq('driver_id', driverId)
      .order('trip_date', { ascending: false })
    
    if (error) throw error
    return data as Trip[]
  },

  // إضافة رحلة جديدة
  async create(trip: Omit<Trip, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('trips')
      .insert([trip])
      .select()
      .single()
    
    if (error) throw error
    return data as Trip
  },

  // تحديث رحلة
  async update(id: string, updates: Partial<Trip>) {
    const { data, error } = await supabase
      .from('trips')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data as Trip
  }
}

// دوال الإحصائيات
export const statsAPI = {
  // إحصائيات عامة
  async getDashboardStats() {
    const [driversCount, tripsCount, totalEarnings, activeTrips] = await Promise.all([
      supabase.from('drivers').select('id', { count: 'exact' }),
      supabase.from('trips').select('id', { count: 'exact' }),
      supabase.from('trips').select('fare').eq('status', 'completed'),
      supabase.from('trips').select('id', { count: 'exact' }).eq('status', 'in_progress')
    ])

    const earnings = totalEarnings.data?.reduce((sum, trip) => sum + trip.fare, 0) || 0

    return {
      driversCount: driversCount.count || 0,
      tripsCount: tripsCount.count || 0,
      totalEarnings: earnings,
      activeTrips: activeTrips.count || 0
    }
  }
}
