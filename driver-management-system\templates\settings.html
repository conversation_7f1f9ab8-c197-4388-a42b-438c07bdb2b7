{% extends "base.html" %}

{% block title %}الإعدادات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات النظام
                </h2>
                <button class="btn btn-success" onclick="saveAllSettings()">
                    <i class="fas fa-save me-2"></i>
                    حفظ جميع الإعدادات
                </button>
            </div>

            <div class="row">
                <!-- الإعدادات العامة -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sliders-h me-2"></i>
                                الإعدادات العامة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="generalSettings">
                                <div class="mb-3">
                                    <label class="form-label">اسم الشركة</label>
                                    <input type="text" class="form-control" name="company_name" value="شركة النقل المتميز">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عنوان الشركة</label>
                                    <textarea class="form-control" name="company_address" rows="2">الرياض، المملكة العربية السعودية</textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">هاتف الشركة</label>
                                    <input type="tel" class="form-control" name="company_phone" value="+966501234567">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">إيميل الشركة</label>
                                    <input type="email" class="form-control" name="company_email" value="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select class="form-select" name="currency">
                                        <option value="SAR" selected>ريال سعودي (ر.س)</option>
                                        <option value="USD">دولار أمريكي ($)</option>
                                        <option value="EUR">يورو (€)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select" name="timezone">
                                        <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                        <option value="UTC">UTC (GMT+0)</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الإشعارات -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bell me-2"></i>
                                إعدادات الإشعارات
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="notificationSettings">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                        <label class="form-check-label" for="emailNotifications">
                                            إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="smsNotifications" checked>
                                        <label class="form-check-label" for="smsNotifications">
                                            إشعارات الرسائل النصية
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="whatsappNotifications" checked>
                                        <label class="form-check-label" for="whatsappNotifications">
                                            إشعارات واتساب
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="paymentReminders" checked>
                                        <label class="form-check-label" for="paymentReminders">
                                            تذكير المدفوعات المتأخرة
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تكرار التذكير (بالأيام)</label>
                                    <input type="number" class="form-control" name="reminder_frequency" value="7" min="1" max="30">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الذكاء الاصطناعي -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-robot me-2"></i>
                                إعدادات الذكاء الاصطناعي
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="aiSettings">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableAI" checked>
                                        <label class="form-check-label" for="enableAI">
                                            تفعيل ميزات الذكاء الاصطناعي
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="predictiveAnalysis" checked>
                                        <label class="form-check-label" for="predictiveAnalysis">
                                            التحليل التنبؤي للمدفوعات
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoClassification" checked>
                                        <label class="form-check-label" for="autoClassification">
                                            التصنيف التلقائي للسائقين
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">حد التصنيف "ملتزم" (نسبة مئوية)</label>
                                    <input type="number" class="form-control" name="committed_threshold" value="90" min="0" max="100">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">حد التصنيف "متأخر" (أيام)</label>
                                    <input type="number" class="form-control" name="late_threshold" value="7" min="1" max="30">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الأمان -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                إعدادات الأمان
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="securitySettings">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                                        <label class="form-check-label" for="twoFactorAuth">
                                            المصادقة الثنائية
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="sessionTimeout" checked>
                                        <label class="form-check-label" for="sessionTimeout">
                                            انتهاء الجلسة التلقائي
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">مدة الجلسة (بالدقائق)</label>
                                    <input type="number" class="form-control" name="session_duration" value="60" min="15" max="480">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                    <input type="number" class="form-control" name="min_password_length" value="8" min="6" max="20">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="loginAttempts" checked>
                                        <label class="form-check-label" for="loginAttempts">
                                            تحديد محاولات تسجيل الدخول
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عدد المحاولات المسموحة</label>
                                    <input type="number" class="form-control" name="max_login_attempts" value="5" min="3" max="10">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات النسخ الاحتياطي -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                النسخ الاحتياطي
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                                    <label class="form-check-label" for="autoBackup">
                                        النسخ الاحتياطي التلقائي
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تكرار النسخ الاحتياطي</label>
                                <select class="form-select" name="backup_frequency">
                                    <option value="daily" selected>يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-primary" onclick="createBackup()">
                                    <i class="fas fa-download me-2"></i>
                                    إنشاء نسخة احتياطية الآن
                                </button>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">استعادة من نسخة احتياطية</label>
                                <input type="file" class="form-control" accept=".sql,.db">
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-warning" onclick="restoreBackup()">
                                    <i class="fas fa-upload me-2"></i>
                                    استعادة النسخة الاحتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <strong>إصدار النظام:</strong>
                                </div>
                                <div class="col-6">
                                    v1.0.0
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>قاعدة البيانات:</strong>
                                </div>
                                <div class="col-6">
                                    SQLite
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>آخر تحديث:</strong>
                                </div>
                                <div class="col-6">
                                    {{ moment().format('YYYY-MM-DD') }}
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>حجم قاعدة البيانات:</strong>
                                </div>
                                <div class="col-6">
                                    <span id="dbSize">حساب...</span>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <button type="button" class="btn btn-info" onclick="checkForUpdates()">
                                    <i class="fas fa-sync-alt me-2"></i>
                                    فحص التحديثات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function saveAllSettings() {
    // جمع جميع الإعدادات
    const settings = {
        general: getFormData('generalSettings'),
        notifications: getFormData('notificationSettings'),
        ai: getFormData('aiSettings'),
        security: getFormData('securitySettings')
    };
    
    // هنا سيتم إرسال الإعدادات إلى الخادم
    console.log('Settings to save:', settings);
    
    // إظهار رسالة نجاح
    showAlert('تم حفظ الإعدادات بنجاح!', 'success');
}

function getFormData(formId) {
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // إضافة قيم الـ checkboxes
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        data[checkbox.id] = checkbox.checked;
    });
    
    return data;
}

function createBackup() {
    // هنا سيتم إضافة كود إنشاء النسخة الاحتياطية
    showAlert('سيتم تطوير ميزة النسخ الاحتياطي قريباً', 'info');
}

function restoreBackup() {
    // هنا سيتم إضافة كود استعادة النسخة الاحتياطية
    showAlert('سيتم تطوير ميزة الاستعادة قريباً', 'info');
}

function checkForUpdates() {
    // هنا سيتم إضافة كود فحص التحديثات
    showAlert('النظام محدث إلى أحدث إصدار', 'success');
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 3000);
}

// حساب حجم قاعدة البيانات (محاكاة)
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        document.getElementById('dbSize').textContent = '2.5 MB';
    }, 1000);
});
</script>
{% endblock %}
