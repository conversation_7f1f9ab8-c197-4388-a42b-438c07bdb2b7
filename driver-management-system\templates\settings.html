{% extends "base.html" %}

{% block title %}الإعدادات - نظام إدارة السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات النظام
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('manage_users') }}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </a>
                    <button class="btn btn-outline-success" onclick="exportSettings()">
                        <i class="fas fa-download me-2"></i>
                        تصدير الإعدادات
                    </button>
                </div>
            </div>

            <form method="POST" action="{{ url_for('update_settings') }}">

                <div class="row">
                    <!-- الإعدادات العامة -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-building me-2"></i>
                                    الإعدادات العامة
                                </h5>
                            </div>
                            <div class="card-body">
                                {% for setting in general_settings %}
                                <div class="mb-3">
                                    <label class="form-label">{{ setting.description or setting.key }}</label>
                                    {% if setting.data_type == 'boolean' %}
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox"
                                                   name="setting_{{ setting.key }}"
                                                   id="{{ setting.key }}"
                                                   value="true"
                                                   {{ 'checked' if setting.get_value() else '' }}>
                                            <label class="form-check-label" for="{{ setting.key }}">
                                                تفعيل
                                            </label>
                                        </div>
                                    {% elif setting.key == 'timezone' %}
                                        <select class="form-select" name="setting_{{ setting.key }}">
                                            <option value="Asia/Riyadh" {{ 'selected' if setting.get_value() == 'Asia/Riyadh' else '' }}>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai" {{ 'selected' if setting.get_value() == 'Asia/Dubai' else '' }}>دبي (GMT+4)</option>
                                            <option value="Asia/Kuwait" {{ 'selected' if setting.get_value() == 'Asia/Kuwait' else '' }}>الكويت (GMT+3)</option>
                                            <option value="Asia/Qatar" {{ 'selected' if setting.get_value() == 'Asia/Qatar' else '' }}>قطر (GMT+3)</option>
                                        </select>
                                    {% elif setting.key == 'currency' %}
                                        <select class="form-select" name="setting_{{ setting.key }}">
                                            <option value="ريال سعودي" {{ 'selected' if setting.get_value() == 'ريال سعودي' else '' }}>ريال سعودي</option>
                                            <option value="درهم إماراتي" {{ 'selected' if setting.get_value() == 'درهم إماراتي' else '' }}>درهم إماراتي</option>
                                            <option value="دينار كويتي" {{ 'selected' if setting.get_value() == 'دينار كويتي' else '' }}>دينار كويتي</option>
                                            <option value="ريال قطري" {{ 'selected' if setting.get_value() == 'ريال قطري' else '' }}>ريال قطري</option>
                                        </select>
                                    {% else %}
                                        <input type="text" class="form-control"
                                               name="setting_{{ setting.key }}"
                                               value="{{ setting.get_value() }}"
                                               placeholder="{{ setting.description }}">
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الإشعارات -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bell me-2"></i>
                                    إعدادات الإشعارات
                                </h5>
                            </div>
                            <div class="card-body">
                                {% for setting in notification_settings %}
                                <div class="mb-3">
                                    <label class="form-label">{{ setting.description or setting.key }}</label>
                                    {% if setting.data_type == 'boolean' %}
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox"
                                                   name="setting_{{ setting.key }}"
                                                   id="{{ setting.key }}"
                                                   value="true"
                                                   {{ 'checked' if setting.get_value() else '' }}>
                                            <label class="form-check-label" for="{{ setting.key }}">
                                                تفعيل
                                            </label>
                                        </div>
                                    {% elif setting.data_type == 'integer' %}
                                        <input type="number" class="form-control"
                                               name="setting_{{ setting.key }}"
                                               value="{{ setting.get_value() }}"
                                               min="1" max="30"
                                               placeholder="{{ setting.description }}">
                                    {% else %}
                                        <input type="text" class="form-control"
                                               name="setting_{{ setting.key }}"
                                               value="{{ setting.get_value() }}"
                                               placeholder="{{ setting.description }}">
                                    {% endif %}
                                </div>
                                {% endfor %}

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>
                                        سيتم إرسال التذكيرات تلقائياً حسب الإعدادات المحددة
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات المدفوعات -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    إعدادات المدفوعات
                                </h5>
                            </div>
                            <div class="card-body">
                                {% for setting in payment_settings %}
                                <div class="mb-3">
                                    <label class="form-label">{{ setting.description or setting.key }}</label>
                                    {% if setting.data_type == 'boolean' %}
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox"
                                                   name="setting_{{ setting.key }}"
                                                   id="{{ setting.key }}"
                                                   value="true"
                                                   {{ 'checked' if setting.get_value() else '' }}>
                                            <label class="form-check-label" for="{{ setting.key }}">
                                                تفعيل
                                            </label>
                                        </div>
                                    {% elif setting.key == 'default_payment_type' %}
                                        <select class="form-select" name="setting_{{ setting.key }}">
                                            <option value="شهري" {{ 'selected' if setting.get_value() == 'شهري' else '' }}>شهري</option>
                                            <option value="أسبوعي" {{ 'selected' if setting.get_value() == 'أسبوعي' else '' }}>أسبوعي</option>
                                            <option value="يومي" {{ 'selected' if setting.get_value() == 'يومي' else '' }}>يومي</option>
                                        </select>
                                    {% elif setting.data_type == 'integer' %}
                                        <input type="number" class="form-control"
                                               name="setting_{{ setting.key }}"
                                               value="{{ setting.get_value() }}"
                                               min="0"
                                               placeholder="{{ setting.description }}">
                                    {% elif setting.data_type == 'float' %}
                                        <input type="number" class="form-control"
                                               name="setting_{{ setting.key }}"
                                               value="{{ setting.get_value() }}"
                                               step="0.01" min="0"
                                               placeholder="{{ setting.description }}">
                                    {% else %}
                                        <input type="text" class="form-control"
                                               name="setting_{{ setting.key }}"
                                               value="{{ setting.get_value() }}"
                                               placeholder="{{ setting.description }}">
                                    {% endif %}
                                </div>
                                {% endfor %}

                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <small>
                                        رسوم التأخير ستطبق تلقائياً بعد انتهاء فترة السماح
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ جميع الإعدادات
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg me-3" onclick="resetSettings()">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                                <button type="button" class="btn btn-info btn-lg" data-bs-toggle="modal" data-bs-target="#backupModal">
                                    <i class="fas fa-database me-2"></i>
                                    النسخ الاحتياطي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

        </div>
    </div>
</div>

<!-- Modal النسخ الاحتياطي -->
<div class="modal fade" id="backupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-database me-2"></i>
                    إدارة النسخ الاحتياطي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <button type="button" class="btn btn-primary w-100" onclick="createBackup()">
                        <i class="fas fa-download me-2"></i>
                        إنشاء نسخة احتياطية الآن
                    </button>
                </div>
                <div class="mb-3">
                    <label class="form-label">استعادة من نسخة احتياطية</label>
                    <input type="file" class="form-control" accept=".sql,.db" id="backupFile">
                </div>
                <div class="mb-3">
                    <button type="button" class="btn btn-warning w-100" onclick="restoreBackup()">
                        <i class="fas fa-upload me-2"></i>
                        استعادة النسخة الاحتياطية
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        location.reload();
    }
}

function exportSettings() {
    // تصدير الإعدادات كملف JSON
    const settings = {
        timestamp: new Date().toISOString(),
        general_settings: {},
        notification_settings: {},
        payment_settings: {}
    };

    const blob = new Blob([JSON.stringify(settings, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'settings_' + new Date().toISOString().split('T')[0] + '.json';
    a.click();
    URL.revokeObjectURL(url);
}

function createBackup() {
    fetch('/create_backup', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            } else {
                showAlert('حدث خطأ في إنشاء النسخة الاحتياطية', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
}

function restoreBackup() {
    const fileInput = document.getElementById('backupFile');
    if (!fileInput.files[0]) {
        showAlert('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
        return;
    }

    if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        const formData = new FormData();
        formData.append('backup_file', fileInput.files[0]);

        fetch('/restore_backup', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showAlert('حدث خطأ في استعادة النسخة الاحتياطية', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
