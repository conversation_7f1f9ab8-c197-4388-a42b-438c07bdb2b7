#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة سائق
"""

import requests
import sys

def test_add_driver():
    """اختبار إضافة سائق"""
    base_url = "http://localhost:5000"
    
    print("🔍 اختبار إضافة سائق...")
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        # تسجيل الدخول أولاً
        print("1. تسجيل الدخول...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # إضافة سائق تجريبي
        print("2. إضافة سائق تجريبي...")
        
        driver_data = {
            "full_name": "أحمد محمد السائق",
            "phone": "**********",
            "national_id": "**********",
            "license_number": "L123456789",
            "vehicle_type": "سيارة",
            "vehicle_plate": "ABC-123",
            "payment_type": "شهري",
            "payment_amount": "1500",
            "status": "نشط",
            "rating": "5",
            "notes": "سائق تجريبي للاختبار"
        }
        
        add_response = session.post(f"{base_url}/drivers/add", data=driver_data)
        
        if add_response.status_code in [200, 302]:
            print("✅ تم إضافة السائق بنجاح")
            
            # التحقق من إضافة السائق
            drivers_page = session.get(f"{base_url}/drivers")
            if "أحمد محمد السائق" in drivers_page.text:
                print("✅ السائق ظهر في قائمة السائقين")
            else:
                print("⚠️ السائق لم يظهر في قائمة السائقين")
                
        else:
            print(f"❌ فشل إضافة السائق: {add_response.status_code}")
            print(f"Response: {add_response.text[:500]}...")
            return False
            
        # اختبار إضافة سائق بنفس رقم الهاتف (يجب أن يفشل)
        print("3. اختبار تكرار رقم الهاتف...")
        
        duplicate_driver = driver_data.copy()
        duplicate_driver["full_name"] = "سائق آخر"
        
        duplicate_response = session.post(f"{base_url}/drivers/add", data=duplicate_driver)
        
        if duplicate_response.status_code in [200, 302]:
            # التحقق من رسالة الخطأ
            drivers_page = session.get(f"{base_url}/drivers")
            if "رقم الهاتف مستخدم بالفعل" in drivers_page.text or "error" in drivers_page.text.lower():
                print("✅ تم منع تكرار رقم الهاتف بنجاح")
            else:
                print("⚠️ لم يتم منع تكرار رقم الهاتف")
        else:
            print(f"⚠️ استجابة غير متوقعة لتكرار رقم الهاتف: {duplicate_response.status_code}")
            
        # اختبار إضافة سائق بدون بيانات مطلوبة
        print("4. اختبار البيانات المطلوبة...")
        
        incomplete_driver = {
            "full_name": "",  # فارغ
            "phone": "0509876543",
            "payment_type": ""  # فارغ
        }
        
        incomplete_response = session.post(f"{base_url}/drivers/add", data=incomplete_driver)
        
        if incomplete_response.status_code in [200, 302]:
            drivers_page = session.get(f"{base_url}/drivers")
            if "مطلوبة" in drivers_page.text or "error" in drivers_page.text.lower():
                print("✅ تم التحقق من البيانات المطلوبة بنجاح")
            else:
                print("⚠️ لم يتم التحقق من البيانات المطلوبة")
        else:
            print(f"⚠️ استجابة غير متوقعة للبيانات الناقصة: {incomplete_response.status_code}")
            
        print("\n✅ انتهى اختبار إضافة السائق")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    test_add_driver()
