#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحديث مخطط قاعدة البيانات لإضافة حقل الهاتف للمستخدمين
"""

import sqlite3
import os
from datetime import datetime

def update_user_schema():
    """تحديث مخطط جدول المستخدمين لإضافة حقل الهاتف"""
    
    # مسار قاعدة البيانات
    db_paths = [
        'driver_management.db',
        'instance/driver_management.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ لم يتم العثور على قاعدة البيانات")
        return False
    
    print(f"📁 استخدام قاعدة البيانات: {db_path}")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود حقل الهاتف
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'phone' in columns:
            print("✅ حقل الهاتف موجود بالفعل في جدول المستخدمين")
            conn.close()
            return True
        
        print("🔄 إضافة حقل الهاتف إلى جدول المستخدمين...")
        
        # إضافة حقل الهاتف
        cursor.execute("ALTER TABLE users ADD COLUMN phone VARCHAR(20)")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من نجاح العملية
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'phone' in columns:
            print("✅ تم إضافة حقل الهاتف بنجاح!")
            
            # عرض معلومات الجدول المحدث
            print("\n📋 مخطط جدول المستخدمين المحدث:")
            cursor.execute("PRAGMA table_info(users)")
            for column in cursor.fetchall():
                print(f"   - {column[1]} ({column[2]})")
            
            conn.close()
            return True
        else:
            print("❌ فشل في إضافة حقل الهاتف")
            conn.close()
            return False
            
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث"""
    
    db_paths = [
        'driver_management.db',
        'instance/driver_management.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        return False
    
    try:
        import shutil
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{db_path}.backup_{timestamp}"
        shutil.copy2(db_path, backup_path)
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
        return True
    except Exception as e:
        print(f"⚠️ تحذير: فشل في إنشاء نسخة احتياطية: {e}")
        return False

def test_user_creation():
    """اختبار إنشاء مستخدم جديد مع حقل الهاتف"""
    try:
        from app import app, db, User
        from werkzeug.security import generate_password_hash
        
        with app.app_context():
            # محاولة إنشاء مستخدم تجريبي
            test_user = User(
                username='test_phone_user',
                email='<EMAIL>',
                full_name='مستخدم تجريبي',
                phone='123456789',
                password_hash=generate_password_hash('test123'),
                role='مستخدم'
            )
            
            # إضافة المستخدم إلى الجلسة (بدون حفظ)
            db.session.add(test_user)
            db.session.flush()  # للتحقق من صحة البيانات بدون حفظ
            
            # إلغاء العملية
            db.session.rollback()
            
            print("✅ اختبار إنشاء المستخدم مع حقل الهاتف نجح!")
            return True
            
    except Exception as e:
        print(f"❌ فشل اختبار إنشاء المستخدم: {e}")
        return False

if __name__ == '__main__':
    print("🚀 بدء تحديث مخطط قاعدة البيانات...")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية
    print("1️⃣ إنشاء نسخة احتياطية...")
    backup_database()
    
    # تحديث المخطط
    print("\n2️⃣ تحديث مخطط قاعدة البيانات...")
    if update_user_schema():
        print("\n3️⃣ اختبار التحديث...")
        if test_user_creation():
            print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
            print("✅ يمكن الآن استخدام حقل الهاتف في نموذج التسجيل")
        else:
            print("\n⚠️ تم التحديث ولكن الاختبار فشل")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
    
    print("=" * 50)
    print("✅ انتهى التحديث")
