{% extends "base.html" %}

{% block title %}خريطة السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    خريطة السائقين
                </h2>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" onclick="refreshMap()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث المواقع
                    </button>
                    <button class="btn btn-outline-success" onclick="centerMap()">
                        <i class="fas fa-crosshairs me-2"></i>
                        توسيط الخريطة
                    </button>
                </div>
            </div>

            <!-- إحصائيات المواقع -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|length }}</h4>
                                    <p class="mb-0">السائقين على الخريطة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-map-marker-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|selectattr("status", "equalto", "نشط")|list|length }}</h4>
                                    <p class="mb-0">سائقين نشطين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>0</h4>
                                    <p class="mb-0">في رحلة حالياً</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-route fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>0</h4>
                                    <p class="mb-0">متوقفين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-pause-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الخريطة -->
            <div class="row">
                <div class="col-md-9">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">الخريطة التفاعلية</h5>
                        </div>
                        <div class="card-body p-0">
                            <div id="map" style="height: 600px; width: 100%;"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">قائمة السائقين</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush" style="max-height: 600px; overflow-y: auto;">
                                {% if drivers %}
                                    {% for driver in drivers %}
                                    <div class="list-group-item list-group-item-action" onclick="focusOnDriver({{ driver.latitude }}, {{ driver.longitude }}, '{{ driver.full_name }}')">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    {{ driver.full_name[0] if driver.full_name else 'س' }}
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ driver.full_name or 'غير محدد' }}</h6>
                                                <p class="mb-1 text-muted small">{{ driver.phone or 'لا يوجد رقم' }}</p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    {% if driver.status == 'نشط' %}
                                                        <span class="badge bg-success">{{ driver.status }}</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ driver.status or 'غير محدد' }}</span>
                                                    {% endif %}
                                                    <small class="text-muted">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        {{ "%.4f, %.4f"|format(driver.latitude, driver.longitude) }}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-map-marker-alt fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">لا توجد مواقع للسائقين</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تضمين Google Maps API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDxQWfJdUVeGOkHcnzWyXLOqmVz1B2FWRY&callback=initMap&language=ar&region=SA"></script>

<script>
let map;
let markers = [];

// بيانات السائقين من الخادم
const driversData = [
    {% for driver in drivers %}
    {
        id: {{ driver.id }},
        name: "{{ driver.full_name or 'غير محدد' }}",
        phone: "{{ driver.phone or 'لا يوجد رقم' }}",
        status: "{{ driver.status or 'غير محدد' }}",
        lat: {{ driver.latitude }},
        lng: {{ driver.longitude }},
        vehicle_type: "{{ driver.vehicle_type or 'غير محدد' }}",
        vehicle_number: "{{ driver.vehicle_number or 'غير محدد' }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
];

function initMap() {
    // إعداد الخريطة - الرياض كنقطة مركزية
    const riyadh = { lat: 24.7136, lng: 46.6753 };
    
    map = new google.maps.Map(document.getElementById("map"), {
        zoom: 11,
        center: riyadh,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: [
            {
                featureType: "all",
                elementType: "labels.text",
                stylers: [
                    {
                        fontFamily: "Cairo, Arial, sans-serif"
                    }
                ]
            }
        ]
    });

    // إضافة علامات السائقين
    addDriverMarkers();
}

function addDriverMarkers() {
    // مسح العلامات السابقة
    markers.forEach(marker => marker.setMap(null));
    markers = [];

    driversData.forEach(driver => {
        // تحديد لون العلامة حسب حالة السائق
        let markerColor = 'red'; // افتراضي
        if (driver.status === 'نشط') {
            markerColor = 'green';
        } else if (driver.status === 'غير نشط') {
            markerColor = 'gray';
        }

        // إنشاء العلامة
        const marker = new google.maps.Marker({
            position: { lat: driver.lat, lng: driver.lng },
            map: map,
            title: driver.name,
            icon: {
                url: `https://maps.google.com/mapfiles/ms/icons/${markerColor}-dot.png`,
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        // إنشاء نافذة المعلومات
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="font-family: Cairo, Arial, sans-serif; direction: rtl; text-align: right;">
                    <h6 style="margin: 0 0 10px 0; color: #333;">${driver.name}</h6>
                    <p style="margin: 5px 0; font-size: 14px;">
                        <strong>الهاتف:</strong> ${driver.phone}
                    </p>
                    <p style="margin: 5px 0; font-size: 14px;">
                        <strong>الحالة:</strong> 
                        <span style="color: ${driver.status === 'نشط' ? 'green' : 'red'};">
                            ${driver.status}
                        </span>
                    </p>
                    <p style="margin: 5px 0; font-size: 14px;">
                        <strong>نوع المركبة:</strong> ${driver.vehicle_type}
                    </p>
                    <p style="margin: 5px 0; font-size: 14px;">
                        <strong>رقم المركبة:</strong> ${driver.vehicle_number}
                    </p>
                    <div style="margin-top: 10px;">
                        <button onclick="callDriver('${driver.phone}')" 
                                style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-left: 5px;">
                            <i class="fas fa-phone"></i> اتصال
                        </button>
                        <button onclick="sendMessage('${driver.phone}')" 
                                style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px;">
                            <i class="fas fa-sms"></i> رسالة
                        </button>
                    </div>
                </div>
            `
        });

        // إضافة مستمع للنقر على العلامة
        marker.addListener('click', () => {
            // إغلاق جميع النوافذ المفتوحة
            markers.forEach(m => {
                if (m.infoWindow) {
                    m.infoWindow.close();
                }
            });
            
            // فتح نافذة المعلومات الحالية
            infoWindow.open(map, marker);
        });

        marker.infoWindow = infoWindow;
        markers.push(marker);
    });

    // توسيط الخريطة لتشمل جميع العلامات
    if (markers.length > 0) {
        const bounds = new google.maps.LatLngBounds();
        markers.forEach(marker => bounds.extend(marker.getPosition()));
        map.fitBounds(bounds);
    }
}

function focusOnDriver(lat, lng, name) {
    map.setCenter({ lat: lat, lng: lng });
    map.setZoom(15);
    
    // البحث عن العلامة المطابقة وفتح نافذة المعلومات
    const marker = markers.find(m => 
        Math.abs(m.getPosition().lat() - lat) < 0.0001 && 
        Math.abs(m.getPosition().lng() - lng) < 0.0001
    );
    
    if (marker && marker.infoWindow) {
        marker.infoWindow.open(map, marker);
    }
}

function refreshMap() {
    // هنا سيتم إضافة كود تحديث المواقع من الخادم
    showNotification('تم تحديث مواقع السائقين', 'success');
    addDriverMarkers();
}

function centerMap() {
    if (markers.length > 0) {
        const bounds = new google.maps.LatLngBounds();
        markers.forEach(marker => bounds.extend(marker.getPosition()));
        map.fitBounds(bounds);
    } else {
        // العودة إلى الرياض إذا لم توجد علامات
        map.setCenter({ lat: 24.7136, lng: 46.6753 });
        map.setZoom(11);
    }
}

function callDriver(phone) {
    if (phone && phone !== 'لا يوجد رقم') {
        window.open(`tel:${phone}`);
    } else {
        alert('رقم الهاتف غير متوفر');
    }
}

function sendMessage(phone) {
    if (phone && phone !== 'لا يوجد رقم') {
        window.open(`sms:${phone}`);
    } else {
        alert('رقم الهاتف غير متوفر');
    }
}

function showNotification(message, type = 'info') {
    // إنشاء إشعار بسيط
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
