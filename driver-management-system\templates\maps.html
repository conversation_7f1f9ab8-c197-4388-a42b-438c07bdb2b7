{% extends "base.html" %}

{% block title %}خريطة السائقين{% endblock %}

{% block extra_head %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
.leaflet-popup-content {
    direction: rtl;
    text-align: right;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    خريطة مواقع السائقين
                </h2>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addLocationModal">
                        <i class="fas fa-map-pin me-2"></i>
                        إضافة موقع
                    </button>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>

            <!-- الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ total_drivers }}</h4>
                                    <p class="card-text">إجمالي السائقين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ drivers_with_location }}</h4>
                                    <p class="card-text">لديهم مواقع</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-map-marker-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ drivers_without_location }}</h4>
                                    <p class="card-text">بدون مواقع</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-question-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الخريطة -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-globe me-2"></i>
                                خريطة المواقع
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div id="map" style="height: 500px; width: 100%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة السائقين مع المواقع -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                السائقين مع المواقع ({{ drivers_with_location }})
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if drivers %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>السائق</th>
                                            <th>نوع المركبة</th>
                                            <th>الحالة</th>
                                            <th>الإحداثيات</th>
                                            <th>آخر تحديث</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for driver in drivers %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <div class="avatar-title bg-primary rounded-circle">
                                                            {{ driver.name[0] if driver.name else 'س' }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ driver.name }}</h6>
                                                        <small class="text-muted">{{ driver.phone }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ driver.vehicle_type or 'غير محدد' }}</span>
                                            </td>
                                            <td>
                                                <span class="badge 
                                                    {% if driver.status == 'نشط' %}bg-success
                                                    {% elif driver.status == 'معلق' %}bg-warning
                                                    {% else %}bg-danger{% endif %}">
                                                    {{ driver.status }}
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ "%.6f"|format(driver.current_latitude) }}, {{ "%.6f"|format(driver.current_longitude) }}
                                                </small>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ driver.updated_at.strftime('%Y-%m-%d %H:%M') if driver.updated_at else 'غير محدد' }}
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary"
                                                            onclick="focusOnDriver({{ driver.current_latitude }}, {{ driver.current_longitude }}, '{{ driver.name }}')"
                                                            title="التركيز على الخريطة">
                                                        <i class="fas fa-crosshairs"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning"
                                                            onclick="editLocation({{ driver.id }}, '{{ driver.name }}', {{ driver.current_latitude }}, {{ driver.current_longitude }}, '{{ driver.current_address or '' }}')"
                                                            title="تعديل الموقع">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" 
                                                            onclick="removeLocation({{ driver.id }}, '{{ driver.name }}')"
                                                            title="حذف الموقع">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد سائقين مع مواقع</h5>
                                <p class="text-muted">ابدأ بإضافة مواقع السائقين لتتبعهم على الخريطة</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLocationModal">
                                    <i class="fas fa-map-pin me-2"></i>
                                    إضافة موقع جديد
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة/تعديل موقع -->
<div class="modal fade" id="addLocationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="locationModalTitle">إضافة موقع سائق</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="locationForm" method="POST">
                    <input type="hidden" id="location_driver_id" name="driver_id">
                    
                    <div class="mb-3">
                        <label class="form-label">السائق *</label>
                        <select class="form-select" id="location_driver_select" name="driver_id" required>
                            <option value="">اختر السائق</option>
                            {% for driver in drivers %}
                            <option value="{{ driver.id }}">{{ driver.name }} - {{ driver.phone }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">خط العرض (Latitude) *</label>
                                <input type="number" class="form-control" id="location_latitude" name="latitude" 
                                       step="0.000001" min="-90" max="90" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">خط الطول (Longitude) *</label>
                                <input type="number" class="form-control" id="location_longitude" name="longitude" 
                                       step="0.000001" min="-180" max="180" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" id="location_address" name="address" rows="2" 
                                  placeholder="العنوان التفصيلي (اختياري)"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-info" onclick="getCurrentLocation()">
                            <i class="fas fa-location-arrow me-2"></i>
                            استخدام موقعي الحالي
                        </button>
                        <small class="form-text text-muted">أو انقر على الخريطة لتحديد الموقع</small>
                    </div>
                    
                    <!-- خريطة صغيرة للتحديد -->
                    <div class="mb-3">
                        <div id="locationPickerMap" style="height: 300px; width: 100%;"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="locationForm" class="btn btn-primary">حفظ الموقع</button>
            </div>
        </div>
    </div>
</div>

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
// بيانات السائقين للخريطة
const driversData = {{ drivers_data | tojson }};

// متغيرات الخريطة
let map;
let locationPickerMap;
let markers = [];
let locationMarker;

// تهيئة الخريطة الرئيسية
function initMap() {
    // الموقع الافتراضي (الرياض)
    const defaultLocation = [24.7136, 46.6753];

    // إنشاء الخريطة الرئيسية
    map = L.map('map').setView(defaultLocation, 10);

    // إضافة طبقة الخريطة
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // إضافة علامات السائقين
    addDriverMarkers();

    // إنشاء خريطة اختيار الموقع
    locationPickerMap = L.map('locationPickerMap').setView(defaultLocation, 13);

    // إضافة طبقة الخريطة لخريطة اختيار الموقع
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(locationPickerMap);

    // إضافة مستمع النقر لخريطة اختيار الموقع
    locationPickerMap.on('click', function(e) {
        setLocationMarker(e.latlng);
        document.getElementById('location_latitude').value = e.latlng.lat;
        document.getElementById('location_longitude').value = e.latlng.lng;
    });
}

// إضافة علامات السائقين على الخريطة
function addDriverMarkers() {
    const group = new L.featureGroup();

    driversData.forEach(function(driver) {
        const marker = L.marker([driver.latitude, driver.longitude], {
            icon: getMarkerIcon(driver.status)
        }).addTo(map);

        // نافذة المعلومات
        const popupContent = `
            <div style="direction: rtl; text-align: right;">
                <h6><strong>${driver.name}</strong></h6>
                <p><i class="fas fa-phone"></i> ${driver.phone}</p>
                <p><i class="fas fa-car"></i> ${driver.vehicle_type}</p>
                <p><i class="fas fa-info-circle"></i> ${driver.status}</p>
                <p><i class="fas fa-clock"></i> ${driver.location_updated}</p>
            </div>
        `;

        marker.bindPopup(popupContent);
        markers.push(marker);
        group.addLayer(marker);
    });

    // تعديل حدود الخريطة لتشمل جميع العلامات
    if (markers.length > 0) {
        map.fitBounds(group.getBounds(), {padding: [20, 20]});
    }
}

// الحصول على أيقونة العلامة حسب الحالة
function getMarkerIcon(status) {
    let color;
    switch(status) {
        case 'نشط':
            color = 'green';
            break;
        case 'معلق':
            color = 'orange';
            break;
        default:
            color = 'red';
    }

    return L.divIcon({
        className: 'custom-div-icon',
        html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });
}

// التركيز على سائق معين
function focusOnDriver(lat, lng, name) {
    map.setView([lat, lng], 15);

    // إظهار رسالة
    const popup = L.popup()
        .setLatLng([lat, lng])
        .setContent(`<div style="direction: rtl;"><strong>${name}</strong></div>`)
        .openOn(map);

    setTimeout(() => map.closePopup(popup), 3000);
}

// تعديل موقع سائق
function editLocation(driverId, driverName, lat, lng, address) {
    document.getElementById('locationModalTitle').textContent = `تعديل موقع ${driverName}`;
    document.getElementById('location_driver_id').value = driverId;
    document.getElementById('location_driver_select').style.display = 'none';
    document.getElementById('location_latitude').value = lat;
    document.getElementById('location_longitude').value = lng;
    document.getElementById('location_address').value = address;
    
    // تحديث action النموذج
    document.getElementById('locationForm').action = `/maps/update-location/${driverId}`;
    
    // تحديث الخريطة
    locationPickerMap.setView([lat, lng], 15);
    setLocationMarker(L.latLng(lat, lng));
    
    new bootstrap.Modal(document.getElementById('addLocationModal')).show();
}

// حذف موقع سائق
function removeLocation(driverId, driverName) {
    if (confirm(`هل تريد حذف موقع السائق ${driverName}؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/maps/remove-location/${driverId}`;
        document.body.appendChild(form);
        form.submit();
    }
}

// الحصول على الموقع الحالي
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            document.getElementById('location_latitude').value = lat;
            document.getElementById('location_longitude').value = lng;

            locationPickerMap.setView([lat, lng], 15);
            setLocationMarker(L.latLng(lat, lng));
        }, function() {
            alert('لا يمكن الحصول على موقعك الحالي');
        });
    } else {
        alert('المتصفح لا يدعم تحديد الموقع');
    }
}

// تعيين علامة الموقع
function setLocationMarker(latlng) {
    if (locationMarker) {
        locationPickerMap.removeLayer(locationMarker);
    }

    locationMarker = L.marker(latlng, {
        draggable: true
    }).addTo(locationPickerMap);

    locationMarker.on('dragend', function() {
        const pos = locationMarker.getLatLng();
        document.getElementById('location_latitude').value = pos.lat;
        document.getElementById('location_longitude').value = pos.lng;
    });
}

// إعادة تعيين النموذج عند إغلاق المودال
document.getElementById('addLocationModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('locationModalTitle').textContent = 'إضافة موقع سائق';
    document.getElementById('location_driver_select').style.display = 'block';
    document.getElementById('locationForm').action = '/maps/update-location/0';
    document.getElementById('locationForm').reset();
    if (locationMarker) {
        locationPickerMap.removeLayer(locationMarker);
    }
});

// تهيئة الخريطة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(initMap, 100);
});
</script>
{% endblock %}
