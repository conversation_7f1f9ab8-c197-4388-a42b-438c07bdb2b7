<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليلات الذكاء الاصطناعي - نظام إدارة السائقين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .ai-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        
        .ai-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .ai-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .stat-card {
            text-align: center;
            padding: 1.5rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .risk-high { color: #dc3545; }
        .risk-medium { color: #ffc107; }
        .risk-low { color: #28a745; }
        
        .recommendation-card {
            border-left: 4px solid;
            margin-bottom: 1rem;
        }
        
        .rec-warning { border-left-color: #dc3545; }
        .rec-improvement { border-left-color: #ffc107; }
        .rec-positive { border-left-color: #28a745; }
        .rec-development { border-left-color: #007bff; }
        .rec-automation { border-left-color: #6f42c1; }
        
        .analysis-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #007bff;
        }
        
        .ai-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        .btn-ai {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-ai:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .analysis-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
        }
        
        .navbar-brand {
            font-weight: 700;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: rgba(0,0,0,0.1);">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-brain me-2"></i>
                نظام إدارة السائقين - الذكاء الاصطناعي
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="{{ url_for('drivers') }}">
                    <i class="fas fa-users me-1"></i>
                    السائقين
                </a>
            </div>
        </div>
    </nav>

    <!-- AI Header -->
    <div class="ai-header">
        <div class="container text-center">
            <i class="fas fa-robot ai-icon"></i>
            <h1 class="display-4 mb-3">تحليلات الذكاء الاصطناعي</h1>
            <p class="lead">تحليل ذكي شامل لأنماط السائقين والمدفوعات مع التوقعات والتوصيات</p>
            <button class="btn btn-ai btn-lg mt-3" onclick="refreshAnalysis()">
                <i class="fas fa-sync-alt me-2"></i>
                تحديث التحليلات
            </button>
        </div>
    </div>

    <div class="container">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="ai-card stat-card">
                    <div class="stat-number text-primary">{{ analysis_stats.total_analyses }}</div>
                    <div class="stat-label">إجمالي التحليلات</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="ai-card stat-card">
                    <div class="stat-number risk-high">{{ analysis_stats.high_risk_drivers }}</div>
                    <div class="stat-label">سائقين عالي المخاطر</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="ai-card stat-card">
                    <div class="stat-number risk-medium">{{ analysis_stats.medium_risk_drivers }}</div>
                    <div class="stat-label">سائقين متوسط المخاطر</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="ai-card stat-card">
                    <div class="stat-number risk-low">{{ analysis_stats.low_risk_drivers }}</div>
                    <div class="stat-label">سائقين منخفض المخاطر</div>
                </div>
            </div>
        </div>

        <!-- Analysis Types -->
        <div class="row mb-4">
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line text-primary" style="font-size: 2rem;"></i>
                        <h5 class="mt-3">تحليل أنماط الدفع</h5>
                        <p class="text-muted">{{ analysis_stats.payment_pattern_analyses }} تحليل</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock text-warning" style="font-size: 2rem;"></i>
                        <h5 class="mt-3">توقع التأخير</h5>
                        <p class="text-muted">{{ analysis_stats.delay_predictions }} توقع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="card-body text-center">
                        <i class="fas fa-trophy text-success" style="font-size: 2rem;"></i>
                        <h5 class="mt-3">تحليل الأداء</h5>
                        <p class="text-muted">{{ analysis_stats.performance_analyses }} تحليل</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- General Recommendations -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="ai-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            التوصيات العامة للنظام
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for rec in general_recommendations %}
                        <div class="recommendation-card p-3 rec-{{ rec.type }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ rec.title }}</h6>
                                    <p class="mb-1 text-muted">{{ rec.description }}</p>
                                    <small class="text-primary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        {{ rec.action }}
                                    </small>
                                </div>
                                <span class="badge bg-{{ 'danger' if rec.type == 'تحذير' else 'warning' if rec.type == 'تحسين' else 'success' if rec.type == 'إيجابي' else 'primary' }}">
                                    {{ rec.type }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Analyses -->
        <div class="row">
            <div class="col-12">
                <div class="ai-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            أحدث التحليلات
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for analysis in recent_analyses %}
                        <div class="analysis-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ analysis.driver.name }}</h6>
                                    <p class="mb-1">
                                        <span class="analysis-badge bg-primary text-white">{{ analysis.analysis_type }}</span>
                                        <span class="ms-2 text-muted">{{ analysis.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                    </p>
                                    <small class="text-muted">{{ analysis.recommendations[:100] }}...</small>
                                </div>
                                <div class="text-center">
                                    <div class="stat-number {{ 'risk-high' if analysis.prediction_score < 0.3 else 'risk-medium' if analysis.prediction_score < 0.7 else 'risk-low' }}" style="font-size: 1.5rem;">
                                        {{ "%.0f"|format(analysis.prediction_score * 100) }}%
                                    </div>
                                    <small class="text-muted">درجة التوقع</small>
                                </div>
                            </div>
                            <div class="mt-2">
                                <a href="{{ url_for('ai_driver_analysis', driver_id=analysis.driver_id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحديث التحليلات...</p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshAnalysis() {
            const spinner = document.querySelector('.loading-spinner');
            const content = document.querySelector('.container');
            
            spinner.style.display = 'block';
            content.style.opacity = '0.5';
            
            // إعادة تحميل الصفحة بعد 3 ثواني لمحاكاة التحديث
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
        
        // تحديث تلقائي كل 5 دقائق
        setInterval(() => {
            console.log('تحديث تلقائي للتحليلات...');
            // يمكن إضافة AJAX call هنا للتحديث بدون إعادة تحميل
        }, 300000);
    </script>
</body>
</html>
