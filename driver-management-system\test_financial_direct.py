#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التقرير المالي مباشرة
Test financial report directly
"""

import requests
import time

def test_financial_report():
    """اختبار التقرير المالي"""
    base_url = 'http://127.0.0.1:5000'
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔄 بدء اختبار التقرير المالي...")
        
        # 1. تسجيل الدخول
        print("1. تسجيل الدخول...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f'{base_url}/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # 2. اختبار التقرير المالي
        print("2. اختبار التقرير المالي...")
        response = session.get(f'{base_url}/reports/financial')
        
        if response.status_code == 200:
            print("✅ التقرير المالي يعمل بنجاح")
            
            # فحص محتوى الصفحة
            content = response.text
            if 'إجمالي الإيرادات المحصلة' in content:
                print("✅ إحصائيات الإيرادات موجودة")
            else:
                print("⚠️ إحصائيات الإيرادات مفقودة")
                
            if 'إيرادات معلقة' in content:
                print("✅ إحصائيات الإيرادات المعلقة موجودة")
            else:
                print("⚠️ إحصائيات الإيرادات المعلقة مفقودة")
                
            return True
        else:
            print(f"❌ التقرير المالي: خطأ {response.status_code}")
            print(f"محتوى الخطأ: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == '__main__':
    # بدء الخادم أولاً
    import subprocess
    import os
    import sys
    
    # تغيير المجلد
    os.chdir('driver-management-system')
    
    # بدء الخادم
    print("🔄 بدء خادم Flask...")
    server_process = subprocess.Popen([sys.executable, 'app.py'], 
                                    stdout=subprocess.PIPE, 
                                    stderr=subprocess.PIPE)
    
    # انتظار بدء الخادم
    time.sleep(3)
    
    try:
        # اختبار التقرير
        success = test_financial_report()
        
        if success:
            print("✅ انتهى الاختبار بنجاح")
        else:
            print("❌ فشل الاختبار")
            
    finally:
        # إيقاف الخادم
        server_process.terminate()
        server_process.wait()
