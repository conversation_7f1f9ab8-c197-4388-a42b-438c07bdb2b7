'use client'

import { useState, useEffect } from 'react'
import { 
  Users, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Phone, 
  Car,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

// نوع بيانات السائق
interface Driver {
  id: string
  name: string
  phone: string
  license_number: string
  vehicle_type: string
  vehicle_plate: string
  status: 'active' | 'inactive' | 'suspended'
  commission_rate: number
  total_earnings: number
  created_at: string
}

// مكون بطاقة السائق
function DriverCard({ driver, onEdit, onDelete }: {
  driver: Driver
  onEdit: (driver: Driver) => void
  onDelete: (id: string) => void
}) {
  const statusColors = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    suspended: 'bg-red-100 text-red-800'
  }

  const statusIcons = {
    active: CheckCircle,
    inactive: XCircle,
    suspended: AlertCircle
  }

  const StatusIcon = statusIcons[driver.status]

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-full ml-3">
            <Users size={20} className="text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{driver.name}</h3>
            <p className="text-sm text-gray-600 flex items-center">
              <Phone size={12} className="ml-1" />
              {driver.phone}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <button
            onClick={() => onEdit(driver)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={() => onDelete(driver.id)}
            className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
          >
            <Trash2 size={16} />
          </button>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">رقم الرخصة:</span>
          <span className="text-sm font-medium">{driver.license_number}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">نوع المركبة:</span>
          <span className="text-sm font-medium flex items-center">
            <Car size={12} className="ml-1" />
            {driver.vehicle_type}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">رقم اللوحة:</span>
          <span className="text-sm font-medium">{driver.vehicle_plate}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">نسبة العمولة:</span>
          <span className="text-sm font-medium">{driver.commission_rate}%</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">إجمالي الأرباح:</span>
          <span className="text-sm font-semibold text-green-600">
            {driver.total_earnings.toLocaleString()} ريال
          </span>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">الحالة:</span>
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[driver.status]}`}>
            <StatusIcon size={12} className="ml-1" />
            {driver.status === 'active' ? 'نشط' : driver.status === 'inactive' ? 'غير نشط' : 'معلق'}
          </span>
        </div>
      </div>
    </div>
  )
}

export default function DriversPage() {
  const [drivers, setDrivers] = useState<Driver[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // بيانات وهمية للاختبار
  useEffect(() => {
    const mockDrivers: Driver[] = [
      {
        id: '1',
        name: 'أحمد محمد السعيد',
        phone: '+966501234567',
        license_number: 'DL123456789',
        vehicle_type: 'سيدان',
        vehicle_plate: 'أ ب ج 1234',
        status: 'active',
        commission_rate: 10,
        total_earnings: 15000,
        created_at: '2024-01-15'
      },
      {
        id: '2',
        name: 'محمد علي أحمد',
        phone: '+966502345678',
        license_number: 'DL234567890',
        vehicle_type: 'SUV',
        vehicle_plate: 'د هـ و 5678',
        status: 'active',
        commission_rate: 12,
        total_earnings: 22000,
        created_at: '2024-01-10'
      },
      {
        id: '3',
        name: 'سعد عبدالله محمد',
        phone: '+966503456789',
        license_number: 'DL345678901',
        vehicle_type: 'شاحنة صغيرة',
        vehicle_plate: 'ز ح ط 9012',
        status: 'inactive',
        commission_rate: 8,
        total_earnings: 8500,
        created_at: '2024-01-05'
      }
    ]
    
    setTimeout(() => {
      setDrivers(mockDrivers)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredDrivers = drivers.filter(driver =>
    driver.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    driver.phone.includes(searchTerm) ||
    driver.vehicle_plate.includes(searchTerm)
  )

  const handleEdit = (driver: Driver) => {
    // TODO: فتح نموذج التعديل
    console.log('تعديل السائق:', driver)
  }

  const handleDelete = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا السائق؟')) {
      setDrivers(drivers.filter(driver => driver.id !== id))
    }
  }

  const handleAddNew = () => {
    // TODO: فتح نموذج إضافة سائق جديد
    console.log('إضافة سائق جديد')
  }

  return (
    <div className="min-h-screen bg-gray-100" dir="rtl">
      {/* شريط الإجراءات */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Users className="h-6 w-6 text-blue-600 ml-2" />
              <h2 className="text-lg font-semibold text-gray-900">إدارة السائقين</h2>
            </div>
            <button
              onClick={handleAddNew}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={20} className="ml-2" />
              إضافة سائق جديد
            </button>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* شريط البحث */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="البحث عن سائق (الاسم، الهاتف، رقم اللوحة...)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* قائمة السائقين */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 border border-gray-200 animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            <div className="mb-4 text-sm text-gray-600">
              عرض {filteredDrivers.length} من أصل {drivers.length} سائق
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDrivers.map((driver) => (
                <DriverCard
                  key={driver.id}
                  driver={driver}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                />
              ))}
            </div>
            {filteredDrivers.length === 0 && (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">لم يتم العثور على سائقين يطابقون البحث</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
