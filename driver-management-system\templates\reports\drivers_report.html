{% extends "base.html" %}

{% block title %}تقرير السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> تقرير السائقين المفصل</h2>
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للتقارير
                    </a>
                    <a href="{{ url_for('export_drivers_excel') }}" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ active_drivers }}</h4>
                            <p class="mb-0">سائق نشط</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ suspended_drivers }}</h4>
                            <p class="mb-0">سائق معلق</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ inactive_drivers }}</h4>
                            <p class="mb-0">سائق غير نشط</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ drivers|length }}</h4>
                            <p class="mb-0">إجمالي السائقين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أنواع المركبات -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-car"></i> توزيع أنواع المركبات</h5>
                </div>
                <div class="card-body">
                    <canvas id="vehicleTypesChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> توزيع حالات السائقين</h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول السائقين -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table"></i> قائمة السائقين التفصيلية</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="driversTable">
                    <thead class="table-dark">
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>نوع المركبة</th>
                            <th>لوحة المركبة</th>
                            <th>نوع الدفع</th>
                            <th>مبلغ الدفع</th>
                            <th>الحالة</th>
                            <th>التقييم</th>
                            <th>تاريخ التسجيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for driver in drivers %}
                        <tr>
                            <td>{{ driver.name }}</td>
                            <td>{{ driver.phone }}</td>
                            <td>{{ driver.vehicle_type or '-' }}</td>
                            <td>{{ driver.vehicle_plate or '-' }}</td>
                            <td>{{ driver.payment_type or '-' }}</td>
                            <td>{{ driver.payment_amount or 0 }} ريال</td>
                            <td>
                                {% if driver.status == 'نشط' %}
                                    <span class="badge bg-success">{{ driver.status }}</span>
                                {% elif driver.status == 'معلق' %}
                                    <span class="badge bg-warning">{{ driver.status }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ driver.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if driver.rating %}
                                    {% for i in range(driver.rating|int) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    ({{ driver.rating }})
                                {% else %}
                                    <span class="text-muted">غير مقيم</span>
                                {% endif %}
                            </td>
                            <td>{{ driver.created_at.strftime('%Y-%m-%d') if driver.created_at else '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// رسم بياني لأنواع المركبات
const vehicleTypesCtx = document.getElementById('vehicleTypesChart').getContext('2d');
const vehicleTypesChart = new Chart(vehicleTypesCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for vtype, count in vehicle_types %}'{{ vtype }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for vtype, count in vehicle_types %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني لحالات السائقين
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'pie',
    data: {
        labels: ['نشط', 'معلق', 'غير نشط'],
        datasets: [{
            data: [{{ active_drivers }}, {{ suspended_drivers }}, {{ inactive_drivers }}],
            backgroundColor: ['#28a745', '#ffc107', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// تفعيل DataTable
$(document).ready(function() {
    $('#driversTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        order: [[8, 'desc']], // ترتيب حسب تاريخ التسجيل
        pageLength: 25
    });
});
</script>
{% endblock %}
