{% extends "base.html" %}

{% block title %}إدارة النسخ الاحتياطية - نظام إدارة السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-database me-2"></i>
                    إدارة النسخ الاحتياطية
                </h2>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="createBackup('full')">
                        <i class="fas fa-plus me-2"></i>
                        نسخة احتياطية كاملة
                    </button>
                    <button class="btn btn-outline-primary" onclick="createBackup('database')">
                        <i class="fas fa-database me-2"></i>
                        نسخة قاعدة البيانات فقط
                    </button>
                    <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#restoreModal">
                        <i class="fas fa-upload me-2"></i>
                        استعادة نسخة احتياطية
                    </button>
                </div>
            </div>

            <!-- إحصائيات النسخ الاحتياطية -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-right-primary">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي النسخ
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ backups.total }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-database fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-right-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        النسخ الناجحة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ backups.items | selectattr('status', 'equalto', 'completed') | list | length }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-right-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        النسخ الفاشلة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ backups.items | selectattr('status', 'equalto', 'failed') | list | length }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-right-info">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        الحجم الإجمالي
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ (backups.items | sum(attribute='file_size') / 1024 / 1024) | round(2) }} ميجابايت
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-hdd fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة النسخ الاحتياطية -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>
                        قائمة النسخ الاحتياطية
                    </h6>
                </div>
                <div class="card-body">
                    {% if backups.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>نوع النسخة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحجم</th>
                                    <th>الحالة</th>
                                    <th>المنشئ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups.items %}
                                <tr>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if backup.backup_type == 'full' else 'info' }}">
                                            {{ 'نسخة كاملة' if backup.backup_type == 'full' else 'قاعدة البيانات' }}
                                        </span>
                                    </td>
                                    <td>{{ backup.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ (backup.file_size / 1024 / 1024) | round(2) }} ميجابايت</td>
                                    <td>
                                        {% if backup.status == 'completed' %}
                                            <span class="badge bg-success">مكتملة</span>
                                        {% elif backup.status == 'failed' %}
                                            <span class="badge bg-danger">فاشلة</span>
                                        {% else %}
                                            <span class="badge bg-warning">قيد المعالجة</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ backup.user.username if backup.user else 'النظام' }}</td>
                                    <td>
                                        {% if backup.status == 'completed' %}
                                            <a href="{{ url_for('download_backup', backup_id=backup.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        {% endif %}
                                        {% if current_user.has_permission('admin') %}
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteBackup({{ backup.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% endif %}
                                        {% if backup.status == 'failed' and backup.error_message %}
                                            <button class="btn btn-sm btn-outline-warning" 
                                                    onclick="showError('{{ backup.error_message }}')" title="عرض الخطأ">
                                                <i class="fas fa-exclamation-circle"></i>
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التقسيم -->
                    {% if backups.pages > 1 %}
                    <nav aria-label="تقسيم الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if backups.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('manage_backups', page=backups.prev_num) }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in backups.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != backups.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('manage_backups', page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if backups.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('manage_backups', page=backups.next_num) }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-database fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                        <p class="text-muted">قم بإنشاء أول نسخة احتياطية للنظام</p>
                        <button class="btn btn-primary" onclick="createBackup('full')">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء نسخة احتياطية
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal استعادة النسخة الاحتياطية -->
<div class="modal fade" id="restoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>
                    استعادة نسخة احتياطية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية المختارة.
                    سيتم إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة.
                </div>
                <div class="mb-3">
                    <label for="backupFile" class="form-label">اختر ملف النسخة الاحتياطية</label>
                    <input type="file" class="form-control" id="backupFile" accept=".zip">
                    <div class="form-text">يجب أن يكون الملف بصيغة ZIP</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="restoreBackup()">
                    <i class="fas fa-upload me-2"></i>
                    استعادة النسخة الاحتياطية
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// إنشاء نسخة احتياطية
function createBackup(type = 'full') {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';

    fetch('/create_backup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ backup_type: type })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert(data.message || 'حدث خطأ في إنشاء النسخة الاحتياطية', 'danger');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الاتصال', 'danger');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// استعادة نسخة احتياطية
function restoreBackup() {
    const fileInput = document.getElementById('backupFile');
    if (!fileInput.files[0]) {
        showAlert('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
        return;
    }

    if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        return;
    }

    const formData = new FormData();
    formData.append('backup_file', fileInput.files[0]);

    fetch('/restore_backup', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
            setTimeout(() => location.reload(), 3000);
        } else {
            showAlert(data.message || 'حدث خطأ في استعادة النسخة الاحتياطية', 'danger');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// حذف نسخة احتياطية
function deleteBackup(backupId) {
    if (!confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
        return;
    }

    fetch(`/delete_backup/${backupId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حذف النسخة الاحتياطية بنجاح', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(data.message || 'حدث خطأ في حذف النسخة الاحتياطية', 'danger');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// عرض رسالة الخطأ
function showError(message) {
    alert('تفاصيل الخطأ:\n' + message);
}

// عرض التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
