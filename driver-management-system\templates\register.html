<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - نظام إدارة السائقين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            backdrop-filter: blur(10px);
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .register-header h2 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .register-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-floating input {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-floating input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-floating label {
            color: #666;
            font-weight: 500;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        
        .login-link a:hover {
            color: #764ba2;
        }
        
        .alert {
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .password-requirements {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .password-requirements ul {
            margin: 0;
            padding-right: 15px;
        }
        
        .password-requirements li {
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
            <h2>إنشاء حساب جديد</h2>
            <p>أدخل بياناتك لإنشاء حساب في نظام إدارة السائقين</p>
        </div>

        <!-- عرض الرسائل -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" id="registerForm">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" 
                       placeholder="اسم المستخدم" required minlength="3" maxlength="50">
                <label for="username">
                    <i class="fas fa-user me-2"></i>
                    اسم المستخدم
                </label>
            </div>

            <div class="form-floating">
                <input type="email" class="form-control" id="email" name="email" 
                       placeholder="البريد الإلكتروني" required>
                <label for="email">
                    <i class="fas fa-envelope me-2"></i>
                    البريد الإلكتروني
                </label>
            </div>

            <div class="form-floating">
                <input type="text" class="form-control" id="full_name" name="full_name" 
                       placeholder="الاسم الكامل" required minlength="2" maxlength="100">
                <label for="full_name">
                    <i class="fas fa-id-card me-2"></i>
                    الاسم الكامل
                </label>
            </div>

            <div class="form-floating">
                <input type="tel" class="form-control" id="phone" name="phone" 
                       placeholder="رقم الهاتف (اختياري)" maxlength="20">
                <label for="phone">
                    <i class="fas fa-phone me-2"></i>
                    رقم الهاتف (اختياري)
                </label>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="كلمة المرور" required minlength="6" maxlength="100">
                <label for="password">
                    <i class="fas fa-lock me-2"></i>
                    كلمة المرور
                </label>
                <div class="password-requirements">
                    <strong>متطلبات كلمة المرور:</strong>
                    <ul>
                        <li>6 أحرف على الأقل</li>
                        <li>يُفضل استخدام أحرف وأرقام</li>
                    </ul>
                </div>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                       placeholder="تأكيد كلمة المرور" required minlength="6" maxlength="100">
                <label for="confirm_password">
                    <i class="fas fa-lock me-2"></i>
                    تأكيد كلمة المرور
                </label>
            </div>

            <button type="submit" class="btn btn-primary btn-register">
                <i class="fas fa-user-plus me-2"></i>
                إنشاء الحساب
            </button>
        </form>

        <div class="login-link">
            <p class="mb-0">
                لديك حساب بالفعل؟ 
                <a href="{{ url_for('login') }}">
                    <i class="fas fa-sign-in-alt me-1"></i>
                    تسجيل الدخول
                </a>
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من تطابق كلمات المرور
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        });

        // التحقق من تطابق كلمات المرور أثناء الكتابة
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });

        // إزالة المسافات من اسم المستخدم
        document.getElementById('username').addEventListener('input', function() {
            this.value = this.value.replace(/\s/g, '');
        });
    </script>
</body>
</html>
