{% extends "base.html" %}

{% block title %}تحليلات لوحة التحكم - نظام إدارة السائقين{% endblock %}

{% block extra_head %}
<style>
.analytics-card {
    transition: transform 0.2s;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}
.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}
.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #5a5c69;
}
.metric-label {
    font-size: 0.875rem;
    color: #858796;
    text-transform: uppercase;
    font-weight: 600;
}
.chart-container-analytics {
    position: relative;
    height: 350px;
}
.performance-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}
.indicator-excellent { background-color: #1cc88a; }
.indicator-good { background-color: #36b9cc; }
.indicator-average { background-color: #f6c23e; }
.indicator-poor { background-color: #e74a3b; }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                تحليلات لوحة التحكم المتقدمة
            </h1>
            <div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
                <button class="btn btn-primary" onclick="exportAnalytics()">
                    <i class="fas fa-download me-2"></i>
                    تصدير التحليلات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Performance Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card analytics-card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    نظرة عامة على الأداء
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="text-center">
                            <div class="metric-value text-primary">{{ performance_stats.total_drivers }}</div>
                            <div class="metric-label">إجمالي السائقين</div>
                            <div class="mt-2">
                                <span class="performance-indicator indicator-excellent"></span>
                                <small class="text-muted">{{ "%.1f"|format((performance_stats.active_drivers/performance_stats.total_drivers*100) if performance_stats.total_drivers > 0 else 0) }}% نشط</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="text-center">
                            <div class="metric-value text-success">{{ "{:,.0f}".format(performance_stats.total_revenue) }}</div>
                            <div class="metric-label">إجمالي الإيرادات (ريال)</div>
                            <div class="mt-2">
                                <span class="performance-indicator indicator-good"></span>
                                <small class="text-muted">{{ "{:,.0f}".format(performance_stats.avg_revenue_per_driver) }} ريال/سائق</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="text-center">
                            <div class="metric-value text-info">{{ "%.1f"|format(performance_stats.payment_success_rate) }}%</div>
                            <div class="metric-label">معدل نجاح المدفوعات</div>
                            <div class="mt-2">
                                {% if performance_stats.payment_success_rate >= 90 %}
                                    <span class="performance-indicator indicator-excellent"></span>
                                {% elif performance_stats.payment_success_rate >= 75 %}
                                    <span class="performance-indicator indicator-good"></span>
                                {% elif performance_stats.payment_success_rate >= 60 %}
                                    <span class="performance-indicator indicator-average"></span>
                                {% else %}
                                    <span class="performance-indicator indicator-poor"></span>
                                {% endif %}
                                <small class="text-muted">من {{ performance_stats.total_payments }} دفعة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="text-center">
                            <div class="metric-value text-warning">{{ "%.1f"|format(performance_stats.location_coverage) }}%</div>
                            <div class="metric-label">تغطية المواقع</div>
                            <div class="mt-2">
                                {% if performance_stats.location_coverage >= 80 %}
                                    <span class="performance-indicator indicator-excellent"></span>
                                {% elif performance_stats.location_coverage >= 60 %}
                                    <span class="performance-indicator indicator-good"></span>
                                {% elif performance_stats.location_coverage >= 40 %}
                                    <span class="performance-indicator indicator-average"></span>
                                {% else %}
                                    <span class="performance-indicator indicator-poor"></span>
                                {% endif %}
                                <small class="text-muted">السائقين بمواقع محددة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <!-- Driver Trends Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card analytics-card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line me-2"></i>
                    اتجاهات نمو السائقين (آخر 12 شهر)
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container-analytics">
                    <canvas id="driverTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Success Rate -->
    <div class="col-xl-4 col-lg-5">
        <div class="card analytics-card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>
                    معدل نجاح المدفوعات الشهرية
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container-analytics">
                    <canvas id="paymentSuccessChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <!-- Vehicle Revenue Analysis -->
    <div class="col-xl-6 col-lg-6">
        <div class="card analytics-card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-car me-2"></i>
                    تحليل الإيرادات حسب نوع المركبة
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container-analytics">
                    <canvas id="vehicleRevenueChart"></canvas>
                </div>
                <div class="mt-3">
                    {% for vehicle in vehicle_revenue %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small font-weight-bold">{{ vehicle.vehicle_type }}</span>
                        <div class="text-end">
                            <div class="font-weight-bold text-success">{{ "{:,.0f}".format(vehicle.total_revenue) }} ريال</div>
                            <small class="text-muted">{{ vehicle.payment_count }} دفعة | متوسط: {{ "{:,.0f}".format(vehicle.avg_payment) }} ريال</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods Analysis -->
    <div class="col-xl-6 col-lg-6">
        <div class="card analytics-card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-credit-card me-2"></i>
                    تحليل أنماط الدفع
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container-analytics">
                    <canvas id="paymentPatternsChart"></canvas>
                </div>
                <div class="mt-3">
                    {% for pattern in payment_patterns %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small font-weight-bold">{{ pattern.payment_method }}</span>
                        <div class="text-end">
                            <div class="font-weight-bold text-info">{{ pattern.usage_count }} استخدام</div>
                            <small class="text-muted">{{ "{:,.0f}".format(pattern.total_amount) }} ريال | متوسط: {{ "{:,.0f}".format(pattern.avg_amount) }} ريال</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Payment Analysis Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card analytics-card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table me-2"></i>
                    تحليل المدفوعات الشهرية التفصيلي
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-primary">
                            <tr>
                                <th>الشهر</th>
                                <th>إجمالي المدفوعات</th>
                                <th>المبلغ الإجمالي</th>
                                <th>مدفوع</th>
                                <th>معلق</th>
                                <th>متأخر</th>
                                <th>معدل النجاح</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for month in monthly_payment_analysis %}
                            <tr>
                                <td class="font-weight-bold">{{ month.month_name }}</td>
                                <td>{{ month.total_payments }}</td>
                                <td class="text-success font-weight-bold">{{ "{:,.0f}".format(month.total_amount) }} ريال</td>
                                <td><span class="badge bg-success">{{ month.paid_count }}</span></td>
                                <td><span class="badge bg-warning">{{ month.pending_count }}</span></td>
                                <td><span class="badge bg-danger">{{ month.overdue_count }}</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ month.success_rate }}%">
                                            {{ "%.1f"|format(month.success_rate) }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Chart.js Configuration for Analytics
Chart.defaults.font.family = 'Cairo, sans-serif';

// Driver Trends Chart
const driverTrendsCtx = document.getElementById('driverTrendsChart').getContext('2d');
const driverTrendsChart = new Chart(driverTrendsCtx, {
    type: 'line',
    data: {
        labels: [{% for trend in driver_trends %}'{{ trend.month_name }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'السائقين الجدد',
            data: [{% for trend in driver_trends %}{{ trend.new_drivers }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.3,
            pointBackgroundColor: '#4e73df',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Payment Success Rate Chart
const paymentSuccessCtx = document.getElementById('paymentSuccessChart').getContext('2d');
const paymentSuccessChart = new Chart(paymentSuccessCtx, {
    type: 'line',
    data: {
        labels: [{% for month in monthly_payment_analysis %}'{{ month.month_name }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'معدل النجاح (%)',
            data: [{% for month in monthly_payment_analysis %}{{ month.success_rate }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: '#1cc88a',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.3,
            pointBackgroundColor: '#1cc88a',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// Vehicle Revenue Chart
const vehicleRevenueCtx = document.getElementById('vehicleRevenueChart').getContext('2d');
const vehicleRevenueChart = new Chart(vehicleRevenueCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for vehicle in vehicle_revenue %}'{{ vehicle.vehicle_type }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for vehicle in vehicle_revenue %}{{ vehicle.total_revenue }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b',
                '#858796'
            ],
            borderColor: '#ffffff',
            borderWidth: 3,
            hoverOffset: 10
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 15
                }
            }
        },
        cutout: '60%'
    }
});

// Payment Patterns Chart
const paymentPatternsCtx = document.getElementById('paymentPatternsChart').getContext('2d');
const paymentPatternsChart = new Chart(paymentPatternsCtx, {
    type: 'bar',
    data: {
        labels: [{% for pattern in payment_patterns %}'{{ pattern.payment_method }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'عدد الاستخدامات',
            data: [{% for pattern in payment_patterns %}{{ pattern.usage_count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: '#4e73df',
            borderColor: '#ffffff',
            borderWidth: 2,
            borderRadius: 5
        }, {
            label: 'إجمالي المبلغ (ريال)',
            data: [{% for pattern in payment_patterns %}{{ pattern.total_amount }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: '#1cc88a',
            borderColor: '#ffffff',
            borderWidth: 2,
            borderRadius: 5,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                beginAtZero: true
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                grid: {
                    drawOnChartArea: false,
                },
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        }
    }
});

// Export Analytics Function
function exportAnalytics() {
    // Create export data
    const exportData = {
        performance_stats: {{ performance_stats | tojson }},
        driver_trends: {{ driver_trends | tojson }},
        vehicle_revenue: {{ vehicle_revenue | tojson }},
        monthly_payment_analysis: {{ monthly_payment_analysis | tojson }},
        payment_patterns: {{ payment_patterns | tojson }},
        export_date: new Date().toISOString()
    };

    // Convert to JSON and download
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `dashboard_analytics_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    // Show success message
    alert('تم تصدير التحليلات بنجاح!');
}

// Add loading animation
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.analytics-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
