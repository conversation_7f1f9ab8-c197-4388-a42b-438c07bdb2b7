# -*- coding: utf-8 -*-
"""
وحدة أدوات التصدير المتقدمة
تحتوي على وظائف تصدير البيانات إلى Excel و PDF
"""

import os
import io
from datetime import datetime, timedelta
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.chart import Bar<PERSON>hart, Reference, PieChart
from openpyxl.drawing.image import Image as OpenpyxlImage
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart
from models import Driver, Payment, db
from sqlalchemy import func, extract

class ExcelExporter:
    """فئة تصدير البيانات إلى Excel"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
        
    def create_workbook(self):
        """إنشاء مصنف Excel جديد"""
        self.workbook = Workbook()
        self.worksheet = self.workbook.active
        return self.workbook
    
    def set_arabic_font(self, cell, bold=False, size=11):
        """تعيين خط عربي للخلية"""
        font = Font(name='Arial Unicode MS', size=size, bold=bold)
        cell.font = font
        cell.alignment = Alignment(horizontal='right', vertical='center')
        
    def add_header_style(self, cell, text, bg_color='366092'):
        """إضافة تنسيق رأس الجدول"""
        cell.value = text
        cell.font = Font(name='Arial Unicode MS', size=12, bold=True, color='FFFFFF')
        cell.fill = PatternFill(start_color=bg_color, end_color=bg_color, fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
    def export_drivers_list(self, drivers, filename=None):
        """تصدير قائمة السائقين إلى Excel"""
        if not filename:
            filename = f'drivers_list_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
        wb = self.create_workbook()
        ws = wb.active
        ws.title = "قائمة السائقين"
        
        # إضافة العنوان الرئيسي
        ws.merge_cells('A1:J1')
        title_cell = ws['A1']
        title_cell.value = f'قائمة السائقين - {datetime.now().strftime("%Y/%m/%d")}'
        title_cell.font = Font(name='Arial Unicode MS', size=16, bold=True)
        title_cell.alignment = Alignment(horizontal='center', vertical='center')
        title_cell.fill = PatternFill(start_color='D9E1F2', end_color='D9E1F2', fill_type='solid')
        
        # إضافة رؤوس الأعمدة
        headers = [
            'الرقم', 'الاسم الكامل', 'رقم الهاتف', 'رقم الهوية', 'رقم الرخصة',
            'نوع المركبة', 'رقم اللوحة', 'نوع الدفع', 'مبلغ الدفع', 'الحالة'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col)
            self.add_header_style(cell, header)
            
        # إضافة بيانات السائقين
        for row, driver in enumerate(drivers, 4):
            ws.cell(row=row, column=1, value=driver.id)
            ws.cell(row=row, column=2, value=driver.name)
            ws.cell(row=row, column=3, value=driver.phone)
            ws.cell(row=row, column=4, value=driver.national_id)
            ws.cell(row=row, column=5, value=driver.license_number)
            ws.cell(row=row, column=6, value=driver.vehicle_type)
            ws.cell(row=row, column=7, value=driver.vehicle_plate)
            ws.cell(row=row, column=8, value=driver.payment_type)
            ws.cell(row=row, column=9, value=driver.payment_amount)
            ws.cell(row=row, column=10, value=driver.status)
            
            # تنسيق الخلايا
            for col in range(1, 11):
                cell = ws.cell(row=row, column=col)
                self.set_arabic_font(cell)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
        # تعديل عرض الأعمدة
        column_widths = [8, 20, 15, 15, 15, 15, 15, 12, 12, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width
            
        # حفظ الملف
        filepath = os.path.join('exports', filename)
        os.makedirs('exports', exist_ok=True)
        wb.save(filepath)
        return filepath
        
    def export_payments_report(self, start_date=None, end_date=None, filename=None):
        """تصدير تقرير المدفوعات"""
        if not filename:
            filename = f'payments_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
        # استعلام المدفوعات
        query = Payment.query
        if start_date:
            query = query.filter(Payment.payment_date >= start_date)
        if end_date:
            query = query.filter(Payment.payment_date <= end_date)
            
        payments = query.all()
        
        wb = self.create_workbook()
        ws = wb.active
        ws.title = "تقرير المدفوعات"
        
        # إضافة العنوان
        ws.merge_cells('A1:H1')
        title_cell = ws['A1']
        period_text = ""
        if start_date and end_date:
            period_text = f" من {start_date.strftime('%Y/%m/%d')} إلى {end_date.strftime('%Y/%m/%d')}"
        title_cell.value = f'تقرير المدفوعات{period_text}'
        title_cell.font = Font(name='Arial Unicode MS', size=16, bold=True)
        title_cell.alignment = Alignment(horizontal='center', vertical='center')
        title_cell.fill = PatternFill(start_color='E2EFDA', end_color='E2EFDA', fill_type='solid')
        
        # إضافة الإحصائيات
        total_amount = sum(p.amount for p in payments)
        ws.merge_cells('A2:H2')
        stats_cell = ws['A2']
        stats_cell.value = f'إجمالي المدفوعات: {total_amount:,.2f} ريال | عدد المدفوعات: {len(payments)}'
        stats_cell.font = Font(name='Arial Unicode MS', size=12, bold=True)
        stats_cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # رؤوس الأعمدة
        headers = [
            'الرقم', 'اسم السائق', 'المبلغ', 'تاريخ الدفع', 'نوع الدفع', 'الحالة', 'طريقة الدفع', 'ملاحظات'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=4, column=col)
            self.add_header_style(cell, header, 'A9D08E')
            
        # بيانات المدفوعات
        for row, payment in enumerate(payments, 5):
            ws.cell(row=row, column=1, value=payment.id)
            ws.cell(row=row, column=2, value=payment.driver.name if payment.driver else 'غير محدد')
            ws.cell(row=row, column=3, value=payment.amount)
            ws.cell(row=row, column=4, value=payment.payment_date.strftime('%Y/%m/%d') if payment.payment_date else '')
            ws.cell(row=row, column=5, value=payment.payment_type)
            ws.cell(row=row, column=6, value=payment.status)
            ws.cell(row=row, column=7, value=payment.payment_method)
            ws.cell(row=row, column=8, value=payment.notes)
            
            # تنسيق الخلايا
            for col in range(1, 9):
                cell = ws.cell(row=row, column=col)
                self.set_arabic_font(cell)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
        # تعديل عرض الأعمدة
        column_widths = [8, 20, 12, 12, 12, 12, 15, 25]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width
            
        # إضافة رسم بياني للمدفوعات الشهرية
        self.add_monthly_payments_chart(ws, payments)
        
        # حفظ الملف
        filepath = os.path.join('exports', filename)
        os.makedirs('exports', exist_ok=True)
        wb.save(filepath)
        return filepath
        
    def add_monthly_payments_chart(self, worksheet, payments):
        """إضافة رسم بياني للمدفوعات الشهرية"""
        # تجميع المدفوعات حسب الشهر
        monthly_data = {}
        for payment in payments:
            if payment.payment_date:
                month_key = payment.payment_date.strftime('%Y-%m')
                monthly_data[month_key] = monthly_data.get(month_key, 0) + payment.amount
                
        if not monthly_data:
            return
            
        # إضافة البيانات في ورقة منفصلة
        chart_start_row = len(payments) + 7
        worksheet.cell(row=chart_start_row, column=1, value='الشهر')
        worksheet.cell(row=chart_start_row, column=2, value='المبلغ')
        
        for i, (month, amount) in enumerate(monthly_data.items(), 1):
            worksheet.cell(row=chart_start_row + i, column=1, value=month)
            worksheet.cell(row=chart_start_row + i, column=2, value=amount)
            
        # إنشاء الرسم البياني
        chart = BarChart()
        chart.title = "المدفوعات الشهرية"
        chart.y_axis.title = "المبلغ (ريال)"
        chart.x_axis.title = "الشهر"
        
        data = Reference(worksheet, min_col=2, min_row=chart_start_row, 
                        max_row=chart_start_row + len(monthly_data))
        cats = Reference(worksheet, min_col=1, min_row=chart_start_row + 1, 
                        max_row=chart_start_row + len(monthly_data))
        
        chart.add_data(data, titles_from_data=True)
        chart.set_categories(cats)
        
        # إضافة الرسم البياني إلى الورقة
        worksheet.add_chart(chart, f"D{chart_start_row}")


class PDFExporter:
    """فئة تصدير البيانات إلى PDF"""
    
    def __init__(self):
        self.setup_arabic_font()
        
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            # محاولة تسجيل خط عربي
            pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
        except:
            # في حالة عدم وجود الخط، استخدم الخط الافتراضي
            pass
            
    def create_styles(self):
        """إنشاء أنماط النص"""
        styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=styles['Title'],
            fontName='Arabic',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.darkblue
        ))
        
        # نمط العنوان الفرعي
        styles.add(ParagraphStyle(
            name='ArabicHeading',
            parent=styles['Heading1'],
            fontName='Arabic',
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=12,
            textColor=colors.darkgreen
        ))
        
        # نمط النص العادي
        styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=styles['Normal'],
            fontName='Arabic',
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=6
        ))
        
        return styles
        
    def export_driver_report(self, driver_id, filename=None):
        """تصدير تقرير سائق فردي"""
        if not filename:
            filename = f'driver_report_{driver_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
            
        driver = Driver.query.get_or_404(driver_id)
        payments = Payment.query.filter_by(driver_id=driver_id).all()
        
        filepath = os.path.join('exports', filename)
        os.makedirs('exports', exist_ok=True)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        story = []
        styles = self.create_styles()
        
        # العنوان الرئيسي
        title = Paragraph(f"تقرير السائق: {driver.name}", styles['ArabicTitle'])
        story.append(title)
        story.append(Spacer(1, 20))
        
        # معلومات السائق
        driver_info = [
            ['البيان', 'القيمة'],
            ['الاسم الكامل', driver.name],
            ['رقم الهاتف', driver.phone or 'غير محدد'],
            ['رقم الهوية', driver.national_id or 'غير محدد'],
            ['رقم الرخصة', driver.license_number or 'غير محدد'],
            ['نوع المركبة', driver.vehicle_type or 'غير محدد'],
            ['رقم اللوحة', driver.vehicle_plate or 'غير محدد'],
            ['نوع الدفع', driver.payment_type or 'غير محدد'],
            ['مبلغ الدفع', f'{driver.payment_amount or 0:,.2f} ريال'],
            ['الحالة', driver.status or 'غير محدد'],
            ['تاريخ التسجيل', driver.created_at.strftime('%Y/%m/%d') if driver.created_at else 'غير محدد']
        ]
        
        driver_table = Table(driver_info, colWidths=[2*inch, 3*inch])
        driver_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(driver_table)
        story.append(Spacer(1, 20))
        
        # إحصائيات المدفوعات
        if payments:
            total_payments = sum(p.amount for p in payments)
            paid_payments = sum(p.amount for p in payments if p.status == 'مدفوع')
            pending_payments = sum(p.amount for p in payments if p.status == 'معلق')
            
            stats_title = Paragraph("إحصائيات المدفوعات", styles['ArabicHeading'])
            story.append(stats_title)
            
            stats_data = [
                ['البيان', 'القيمة'],
                ['إجمالي المدفوعات', f'{total_payments:,.2f} ريال'],
                ['المدفوعات المسددة', f'{paid_payments:,.2f} ريال'],
                ['المدفوعات المعلقة', f'{pending_payments:,.2f} ريال'],
                ['عدد المدفوعات', str(len(payments))]
            ]
            
            stats_table = Table(stats_data, colWidths=[2*inch, 3*inch])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(stats_table)
            story.append(Spacer(1, 20))
            
        # تاريخ التقرير
        report_date = Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y/%m/%d %H:%M')}", 
                               styles['ArabicNormal'])
        story.append(Spacer(1, 30))
        story.append(report_date)
        
        doc.build(story)
        return filepath


class ExcelExporter:
    """فئة تصدير Excel مع دعم اللغة العربية والتنسيق المتقدم"""

    def __init__(self):
        self.arabic_font = 'Arial Unicode MS'

    def create_workbook(self):
        """إنشاء مصنف Excel جديد"""
        return Workbook()

    def set_arabic_font(self, cell, size=11, bold=False):
        """تطبيق الخط العربي على الخلية"""
        cell.font = Font(name=self.arabic_font, size=size, bold=bold)
        cell.alignment = Alignment(horizontal='right', vertical='center')

    def add_header_style(self, cell, value, color='4472C4'):
        """إضافة تنسيق رأس العمود"""
        cell.value = value
        cell.font = Font(name=self.arabic_font, size=12, bold=True, color='FFFFFF')
        cell.fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    def export_financial_report(self, year=None, month=None, filename=None):
        """تصدير التقرير المالي الشامل"""
        if not filename:
            filename = f'financial_report_{year}_{month or "all"}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        if not year:
            year = datetime.now().year

        wb = self.create_workbook()
        ws = wb.active
        ws.title = "التقرير المالي"

        # استعلام البيانات المالية
        query = Payment.query.filter(extract('year', Payment.payment_date) == year)
        if month:
            query = query.filter(extract('month', Payment.payment_date) == month)

        payments = query.all()

        # إضافة العنوان
        ws.merge_cells('A1:G1')
        title_cell = ws['A1']
        period_text = f"سنة {year}"
        if month:
            period_text += f" - شهر {month}"
        title_cell.value = f'التقرير المالي الشامل - {period_text}'
        title_cell.font = Font(name='Arial Unicode MS', size=16, bold=True)
        title_cell.alignment = Alignment(horizontal='center', vertical='center')
        title_cell.fill = PatternFill(start_color='FFD700', end_color='FFD700', fill_type='solid')

        # إضافة الإحصائيات العامة
        total_amount = sum(p.amount for p in payments)
        paid_amount = sum(p.amount for p in payments if p.status == 'مدفوع')
        pending_amount = sum(p.amount for p in payments if p.status == 'معلق')

        ws.merge_cells('A2:G2')
        stats_cell = ws['A2']
        stats_cell.value = f'إجمالي المبلغ: {total_amount:,.2f} ريال | المدفوع: {paid_amount:,.2f} ريال | المعلق: {pending_amount:,.2f} ريال'
        stats_cell.font = Font(name='Arial Unicode MS', size=12, bold=True)
        stats_cell.alignment = Alignment(horizontal='center', vertical='center')

        # رؤوس الأعمدة
        headers = ['التاريخ', 'السائق', 'المبلغ', 'الحالة', 'نوع الدفع', 'طريقة الدفع', 'ملاحظات']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=4, column=col)
            self.add_header_style(cell, header, 'FF6B35')

        # بيانات المدفوعات
        for row, payment in enumerate(payments, 5):
            ws.cell(row=row, column=1, value=payment.payment_date.strftime('%Y/%m/%d') if payment.payment_date else '')
            ws.cell(row=row, column=2, value=payment.driver.name if payment.driver else 'غير محدد')
            ws.cell(row=row, column=3, value=payment.amount)
            ws.cell(row=row, column=4, value=payment.status)
            ws.cell(row=row, column=5, value=payment.payment_type)
            ws.cell(row=row, column=6, value=payment.payment_method)
            ws.cell(row=row, column=7, value=payment.notes)

            # تنسيق الخلايا
            for col in range(1, 8):
                cell = ws.cell(row=row, column=col)
                self.set_arabic_font(cell)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

        # تعديل عرض الأعمدة
        column_widths = [12, 20, 12, 12, 12, 15, 25]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width

        # إضافة رسم بياني دائري لحالات المدفوعات
        self.add_payment_status_pie_chart(ws, payments)

        # حفظ الملف
        filepath = os.path.join('exports', filename)
        os.makedirs('exports', exist_ok=True)
        wb.save(filepath)
        return filepath

    def add_payment_status_pie_chart(self, worksheet, payments):
        """إضافة رسم بياني دائري لحالات المدفوعات"""
        # تجميع البيانات حسب الحالة
        status_data = {}
        for payment in payments:
            status = payment.status or 'غير محدد'
            status_data[status] = status_data.get(status, 0) + payment.amount

        if not status_data:
            return

        # إضافة البيانات في ورقة منفصلة
        chart_start_row = len(payments) + 7
        worksheet.cell(row=chart_start_row, column=1, value='الحالة')
        worksheet.cell(row=chart_start_row, column=2, value='المبلغ')

        for i, (status, amount) in enumerate(status_data.items(), 1):
            worksheet.cell(row=chart_start_row + i, column=1, value=status)
            worksheet.cell(row=chart_start_row + i, column=2, value=amount)

        # إنشاء الرسم البياني الدائري
        chart = PieChart()
        chart.title = "توزيع المدفوعات حسب الحالة"

        data = Reference(worksheet, min_col=2, min_row=chart_start_row,
                        max_row=chart_start_row + len(status_data))
        labels = Reference(worksheet, min_col=1, min_row=chart_start_row + 1,
                          max_row=chart_start_row + len(status_data))

        chart.add_data(data, titles_from_data=True)
        chart.set_categories(labels)

        # إضافة الرسم البياني إلى الورقة
        worksheet.add_chart(chart, f"E{chart_start_row}")
