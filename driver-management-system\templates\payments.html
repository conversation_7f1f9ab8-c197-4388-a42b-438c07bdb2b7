{% extends "base.html" %}

{% block title %}إدارة المدفوعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    إدارة المدفوعات
                </h2>
                <div>
                    <a href="{{ url_for('payment_analytics') }}" class="btn btn-info me-2">
                        <i class="fas fa-chart-line me-2"></i>
                        التحليلات
                    </a>
                    <a href="{{ url_for('payment_reminders') }}" class="btn btn-warning me-2">
                        <i class="fas fa-bell me-2"></i>
                        التذكيرات
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </button>
                </div>
            </div>

            <!-- إحصائيات المدفوعات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ "%.0f"|format(total_payments or 0) }} ريال</h4>
                                    <p class="mb-0">إجمالي المدفوعات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-receipt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ "%.0f"|format(monthly_payments or 0) }} ريال</h4>
                                    <p class="mb-0">مدفوعات الشهر</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ pending_payments or 0 }}</h4>
                                    <p class="mb-0">مدفوعات متأخرة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-university fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ payments|selectattr("payment_method", "equalto", "كاش")|list|length }}</h4>
                                    <p class="mb-0">مدفوعات نقدية</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-coins fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المدفوعات -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">سجل المدفوعات</h5>
                    <div>
                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دفعة
                        </button>
                        <button class="btn btn-info me-2" onclick="updatePaymentStatus()">
                            <i class="fas fa-sync me-2"></i>
                            تحديث الحالات
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-tasks me-2"></i>
                                عمليات مجمعة
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="bulkAction('mark_completed')">تحديد كمكتمل</a></li>
                                <li><a class="dropdown-item" href="#" onclick="bulkAction('mark_pending')">تحديد كمعلق</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">حذف المحدد</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>التاريخ</th>
                                    <th>السائق</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>الحالة</th>
                                    <th>رقم المرجع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="payment-checkbox" value="{{ payment.id }}">
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else 'غير محدد' }}</strong>
                                            <br>
                                            <small class="text-muted">{{ payment.payment_date.strftime('%H:%M') if payment.payment_date else '' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if payment.driver %}
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ payment.driver.name[0] if payment.driver.name else 'س' }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ payment.driver.name or 'غير محدد' }}</h6>
                                                    <small class="text-muted">{{ payment.driver.phone or 'لا يوجد رقم' }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">سائق محذوف</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ "%.2f"|format(payment.amount) }} ر.س</strong>
                                    </td>
                                    <td>
                                        {% if payment.payment_method == 'تحويل' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-university me-1"></i>
                                                {{ payment.payment_method }}
                                            </span>
                                        {% elif payment.payment_method == 'كاش' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-coins me-1"></i>
                                                {{ payment.payment_method }}
                                            </span>
                                        {% elif payment.payment_method == 'شيك' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-money-check me-1"></i>
                                                {{ payment.payment_method }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ payment.payment_method or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if payment.status == 'مكتمل' %}bg-success
                                            {% elif payment.status == 'معلق' %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ payment.status }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if payment.reference_number %}
                                            <code>{{ payment.reference_number }}</code>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" title="تعديل"
                                                    onclick="editPayment({{ payment.id }}, '{{ payment.driver.name if payment.driver else 'غير معروف' }}', {{ payment.amount }}, '{{ payment.payment_method }}', '{{ payment.reference_number or '' }}', '{{ payment.notes or '' }}', '{{ payment.status }}', '{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '' }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" title="حذف"
                                                    onclick="deletePayment({{ payment.id }}, '{{ payment.driver.name if payment.driver else 'غير معروف' }}', {{ payment.amount }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد مدفوعات مسجلة</h5>
                        <p class="text-muted">ابدأ بإضافة أول دفعة لتتبع المدفوعات</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دفعة جديدة
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة دفعة جديدة -->
<div class="modal fade" id="addPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دفعة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPaymentForm" method="POST" action="{{ url_for('add_payment') }}">
                    <div class="mb-3">
                        <label class="form-label">السائق *</label>
                        <select class="form-select" name="driver_id" required>
                            <option value="">اختر السائق</option>
                            {% for driver in drivers %}
                            <option value="{{ driver.id }}">{{ driver.name }} - {{ driver.phone }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المبلغ (ر.س) *</label>
                        <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع *</label>
                        <select class="form-select" name="payment_method" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="نقدي">نقدي</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="شيك">شيك</option>
                            <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم المرجع</label>
                        <input type="text" class="form-control" name="reference_number" placeholder="رقم التحويل أو الشيك">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="addPaymentForm" class="btn btn-primary">حفظ الدفعة</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل دفعة -->
<div class="modal fade" id="editPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الدفعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPaymentForm" method="POST">
                    <input type="hidden" id="edit_payment_id" name="payment_id">

                    <div class="mb-3">
                        <label class="form-label">المبلغ (ر.س) *</label>
                        <input type="number" class="form-control" id="edit_amount" name="amount" step="0.01" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع *</label>
                        <select class="form-select" id="edit_payment_method" name="payment_method" required>
                            <option value="نقدي">نقدي</option>
                            <option value="تحويل">تحويل بنكي</option>
                            <option value="شيك">شيك</option>
                            <option value="بطاقة">بطاقة ائتمان</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="edit_status" name="status">
                            <option value="مكتمل">مكتمل</option>
                            <option value="معلق">معلق</option>
                            <option value="ملغي">ملغي</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">تاريخ الدفع</label>
                        <input type="date" class="form-control" id="edit_payment_date" name="payment_date">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">رقم المرجع</label>
                        <input type="text" class="form-control" id="edit_reference_number" name="reference_number" placeholder="رقم المرجع للتحويل أو الشيك">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الملاحظات</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="editPaymentForm" class="btn btn-primary">حفظ التعديلات</button>
            </div>
        </div>
    </div>
</div>

<script>
// تحديد/إلغاء تحديد جميع المربعات
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.payment-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// تعديل دفعة
function editPayment(id, driverName, amount, method, reference, notes, status, date) {
    document.getElementById('edit_payment_id').value = id;
    document.getElementById('edit_amount').value = amount;
    document.getElementById('edit_payment_method').value = method;
    document.getElementById('edit_status').value = status;
    document.getElementById('edit_payment_date').value = date;
    document.getElementById('edit_reference_number').value = reference;
    document.getElementById('edit_notes').value = notes;

    // تحديث action للنموذج
    document.getElementById('editPaymentForm').action = `/payments/edit/${id}`;

    // إظهار المودال
    new bootstrap.Modal(document.getElementById('editPaymentModal')).show();
}

// العمليات المجمعة
function bulkAction(action) {
    const checkboxes = document.querySelectorAll('.payment-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('يرجى اختيار دفعة واحدة على الأقل');
        return;
    }

    const paymentIds = Array.from(checkboxes).map(cb => cb.value);
    let confirmMessage = '';

    switch(action) {
        case 'delete':
            confirmMessage = `هل أنت متأكد من حذف ${paymentIds.length} دفعة؟`;
            break;
        case 'mark_completed':
            confirmMessage = `هل تريد تحديد ${paymentIds.length} دفعة كمكتملة؟`;
            break;
        case 'mark_pending':
            confirmMessage = `هل تريد تحديد ${paymentIds.length} دفعة كمعلقة؟`;
            break;
    }

    if (confirm(confirmMessage)) {
        const formData = new FormData();
        formData.append('bulk_action', action);
        paymentIds.forEach(id => formData.append('payment_ids', id));

        fetch('/payments/bulk-action', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('حدث خطأ في تنفيذ العملية');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تنفيذ العملية');
        });
    }
}

function deletePayment(paymentId, driverName, amount) {
    if (confirm('هل أنت متأكد من حذف دفعة بمبلغ ' + amount + ' ريال للسائق "' + driverName + '"؟')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/payments/delete/' + paymentId;
        document.body.appendChild(form);
        form.submit();
    }
}

function updatePaymentStatus() {
    if (confirm('هل تريد تحديث حالات الدفع لجميع السائقين؟')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/payments/update-status';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
