{% extends "base.html" %}

{% block title %}إدارة المدفوعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    إدارة المدفوعات
                </h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دفعة جديدة
                </button>
            </div>

            <!-- إحصائيات المدفوعات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ payments|length }}</h4>
                                    <p class="mb-0">إجمالي المدفوعات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-receipt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ payments|sum(attribute='amount')|round(2) if payments else 0 }} ر.س</h4>
                                    <p class="mb-0">إجمالي المبلغ</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ payments|selectattr("payment_method", "equalto", "تحويل")|list|length }}</h4>
                                    <p class="mb-0">تحويلات بنكية</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-university fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ payments|selectattr("payment_method", "equalto", "كاش")|list|length }}</h4>
                                    <p class="mb-0">مدفوعات نقدية</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-coins fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المدفوعات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">سجل المدفوعات</h5>
                </div>
                <div class="card-body">
                    {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>السائق</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>رقم المرجع</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else 'غير محدد' }}</strong>
                                            <br>
                                            <small class="text-muted">{{ payment.payment_date.strftime('%H:%M') if payment.payment_date else '' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if payment.driver %}
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ payment.driver.full_name[0] if payment.driver.full_name else 'س' }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ payment.driver.full_name or 'غير محدد' }}</h6>
                                                    <small class="text-muted">{{ payment.driver.phone or 'لا يوجد رقم' }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">سائق محذوف</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ "%.2f"|format(payment.amount) }} ر.س</strong>
                                    </td>
                                    <td>
                                        {% if payment.payment_method == 'تحويل' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-university me-1"></i>
                                                {{ payment.payment_method }}
                                            </span>
                                        {% elif payment.payment_method == 'كاش' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-coins me-1"></i>
                                                {{ payment.payment_method }}
                                            </span>
                                        {% elif payment.payment_method == 'شيك' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-money-check me-1"></i>
                                                {{ payment.payment_method }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ payment.payment_method or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.reference_number %}
                                            <code>{{ payment.reference_number }}</code>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.notes %}
                                            <span title="{{ payment.notes }}">
                                                {{ payment.notes[:30] + '...' if payment.notes|length > 30 else payment.notes }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد مدفوعات مسجلة</h5>
                        <p class="text-muted">ابدأ بإضافة أول دفعة لتتبع المدفوعات</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دفعة جديدة
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة دفعة جديدة -->
<div class="modal fade" id="addPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دفعة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPaymentForm">
                    <div class="mb-3">
                        <label class="form-label">السائق *</label>
                        <select class="form-select" name="driver_id" required>
                            <option value="">اختر السائق</option>
                            <!-- سيتم ملء هذه القائمة من قاعدة البيانات -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المبلغ (ر.س) *</label>
                        <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع *</label>
                        <select class="form-select" name="payment_method" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="تحويل">تحويل بنكي</option>
                            <option value="كاش">نقدي</option>
                            <option value="شيك">شيك</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم المرجع</label>
                        <input type="text" class="form-control" name="reference_number" placeholder="رقم التحويل أو الشيك">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تاريخ الدفع *</label>
                        <input type="datetime-local" class="form-control" name="payment_date" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="savePayment()">حفظ الدفعة</button>
            </div>
        </div>
    </div>
</div>

<script>
function savePayment() {
    // هنا سيتم إضافة كود حفظ الدفعة
    alert('سيتم تطوير هذه الميزة قريباً');
}
</script>
{% endblock %}
