'use client'

import { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  DollarSign, 
  Users, 
  Car,
  Calendar,
  Download,
  Filter
} from 'lucide-react'
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from 'recharts'

// بيانات وهمية للرسوم البيانية
const monthlyEarnings = [
  { month: 'يناير', earnings: 45000, trips: 180 },
  { month: 'فبراير', earnings: 52000, trips: 210 },
  { month: 'مارس', earnings: 48000, trips: 195 },
  { month: 'أبريل', earnings: 61000, trips: 245 },
  { month: 'مايو', earnings: 55000, trips: 220 },
  { month: 'يونيو', earnings: 67000, trips: 270 }
]

const driverPerformance = [
  { name: 'أحمد محمد', trips: 45, earnings: 11250 },
  { name: 'محمد علي', trips: 38, earnings: 9500 },
  { name: 'سعد عبدالله', trips: 42, earnings: 10500 },
  { name: 'عبدالرحمن سالم', trips: 35, earnings: 8750 },
  { name: 'خالد أحمد', trips: 40, earnings: 10000 }
]

const tripStatusData = [
  { name: 'مكتملة', value: 850, color: '#10B981' },
  { name: 'جارية', value: 25, color: '#3B82F6' },
  { name: 'في الانتظار', value: 15, color: '#F59E0B' },
  { name: 'ملغية', value: 35, color: '#EF4444' }
]

// مكون بطاقة الإحصائيات
function StatCard({ title, value, change, icon: Icon, color = "blue" }: {
  title: string
  value: string | number
  change?: string
  icon: any
  color?: string
}) {
  const colorClasses = {
    blue: "bg-blue-500",
    green: "bg-green-500", 
    yellow: "bg-yellow-500",
    purple: "bg-purple-500"
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className="text-sm text-green-600 mt-1">
              <TrendingUp size={12} className="inline ml-1" />
              {change}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color as keyof typeof colorClasses]} text-white`}>
          <Icon size={24} />
        </div>
      </div>
    </div>
  )
}

export default function ReportsPage() {
  const [dateRange, setDateRange] = useState('6months')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }, [])

  const handleExport = () => {
    // TODO: تنفيذ تصدير التقارير
    console.log('تصدير التقارير')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل التقارير...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100" dir="rtl">
      {/* شريط الإجراءات */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <TrendingUp className="h-6 w-6 text-blue-600 ml-2" />
              <h2 className="text-lg font-semibold text-gray-900">التقارير المالية</h2>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="1month">الشهر الماضي</option>
                <option value="3months">آخر 3 أشهر</option>
                <option value="6months">آخر 6 أشهر</option>
                <option value="1year">السنة الماضية</option>
              </select>
              <button
                onClick={handleExport}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download size={20} className="ml-2" />
                تصدير التقرير
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="إجمالي الأرباح"
            value="328,000 ريال"
            change="+12.5% من الشهر الماضي"
            icon={DollarSign}
            color="green"
          />
          <StatCard
            title="عدد الرحلات"
            value="1,320"
            change="+8.2% من الشهر الماضي"
            icon={Car}
            color="blue"
          />
          <StatCard
            title="السائقين النشطين"
            value="485"
            change="+3.1% من الشهر الماضي"
            icon={Users}
            color="purple"
          />
          <StatCard
            title="متوسط الأجرة"
            value="248 ريال"
            change="+5.7% من الشهر الماضي"
            icon={TrendingUp}
            color="yellow"
          />
        </div>

        {/* الرسوم البيانية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* رسم بياني للأرباح الشهرية */}
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الأرباح الشهرية</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyEarnings}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    `${value.toLocaleString()} ريال`, 
                    name === 'earnings' ? 'الأرباح' : 'الرحلات'
                  ]}
                />
                <Line 
                  type="monotone" 
                  dataKey="earnings" 
                  stroke="#3B82F6" 
                  strokeWidth={3}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* رسم بياني لحالة الرحلات */}
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع حالة الرحلات</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={tripStatusData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {tripStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} رحلة`, 'العدد']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* أداء السائقين */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">أداء أفضل السائقين</h3>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={driverPerformance} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="name" type="category" width={120} />
              <Tooltip 
                formatter={(value, name) => [
                  name === 'trips' ? `${value} رحلة` : `${value.toLocaleString()} ريال`,
                  name === 'trips' ? 'عدد الرحلات' : 'الأرباح'
                ]}
              />
              <Bar dataKey="earnings" fill="#10B981" name="earnings" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  )
}
