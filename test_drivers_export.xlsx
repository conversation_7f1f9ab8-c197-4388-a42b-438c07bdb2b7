<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السائقين</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-car me-2"></i>
                نظام إدارة السائقين
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/drivers">
                            <i class="fas fa-users me-1"></i>
                            السائقين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/payments">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            المدفوعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/map">
                            <i class="fas fa-map-marked-alt me-1"></i>
                            الخريطة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/notifications">
                            <i class="fas fa-bell me-1"></i>
                            الإشعارات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/export">
                            <i class="fas fa-download"></i>
                            مركز التصدير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            المدير العام
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="fas fa-user-cog me-1"></i>
                                الملف الشخصي
                            </a></li>
                            
                            <li><a class="dropdown-item" href="/settings">
                                <i class="fas fa-cogs me-1"></i>
                                لوحة الإدارة
                            </a></li>
                            
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                    
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    
        
            <div class="container mt-3">
                
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        حدث خطأ أثناء التصدير: خطأ في تصدير قائمة السائقين: &#39;Driver&#39; object has no attribute &#39;salary&#39;
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                
            </div>
        
    

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    إدارة السائقين
                </h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة سائق جديد
                </button>
                <div class="btn-group ms-2" role="group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/export/drivers/excel">
                            <i class="fas fa-file-excel text-success me-2"></i>
                            تصدير إلى Excel
                        </a></li>
                        <li><a class="dropdown-item" href="/export">
                            <i class="fas fa-download text-primary me-2"></i>
                            مركز التصدير المتقدم
                        </a></li>
                    </ul>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>1</h4>
                                    <p class="mb-0">إجمالي السائقين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>1</h4>
                                    <p class="mb-0">السائقين النشطين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>0</h4>
                                    <p class="mb-0">متأخرين في الدفع</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>0</h4>
                                    <p class="mb-0">سائقين ملتزمين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول السائقين -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">قائمة السائقين</h5>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سائق جديد
                    </button>
                </div>
                <div class="card-body">
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>رقم الرخصة</th>
                                    <th>الحالة</th>
                                    <th>حالة الدفع</th>
                                    <th>التصنيف</th>
                                    <th>التقييم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    أ
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">أحمد محمد السائق</h6>
                                                <small class="text-muted">0501234567</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>0501234567</td>
                                    <td>L123456789</td>
                                    <td>
                                        
                                            <span class="badge bg-success">نشط</span>
                                        
                                    </td>
                                    <td>
                                        
                                            <span class="badge bg-warning">ملتزم</span>
                                        
                                    </td>
                                    <td>
                                        
                                            <span class="badge bg-secondary">غير محدد</span>
                                        
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            
                                                
                                                    <i class="fas fa-star text-warning"></i>
                                                
                                            
                                                
                                                    <i class="fas fa-star text-warning"></i>
                                                
                                            
                                                
                                                    <i class="fas fa-star text-warning"></i>
                                                
                                            
                                                
                                                    <i class="fas fa-star text-warning"></i>
                                                
                                            
                                                
                                                    <i class="fas fa-star text-warning"></i>
                                                
                                            
                                            <small class="ms-1">(5.0)</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" title="تحديث الموقع" onclick="updateLocation(1)">
                                                <i class="fas fa-map-marker-alt"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" title="تعديل" onclick="editDriver(1, 'أحمد محمد السائق', '0501234567', '1234567890', 'L123456789', 'سيارة', 'ABC-123', 'شهري', 1500.0, 'نشط', 'سائق تجريبي للاختبار')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <a href="/export/driver/1/pdf" class="btn btn-sm btn-outline-info" title="تصدير تقرير PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="حذف" onclick="deleteDriver(1, 'أحمد محمد السائق')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                            </tbody>
                        </table>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة سائق جديد -->
<div class="modal fade" id="addDriverModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سائق جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDriverForm" method="POST" action="/drivers/add">
                    <input type="hidden" id="driver_id" name="driver_id" value="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهوية *</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الرخصة *</label>
                                <input type="text" class="form-control" id="license_number" name="license_number" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المركبة</label>
                                <select class="form-select" id="vehicle_type" name="vehicle_type">
                                    <option value="">اختر نوع المركبة</option>
                                    <option value="سيارة">سيارة</option>
                                    <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                                    <option value="شاحنة كبيرة">شاحنة كبيرة</option>
                                    <option value="حافلة">حافلة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم اللوحة</label>
                                <input type="text" class="form-control" id="vehicle_plate" name="vehicle_plate">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع الدفع</label>
                                <select class="form-select" id="payment_type" name="payment_type">
                                    <option value="">اختر نوع الدفع</option>
                                    <option value="شهري">شهري</option>
                                    <option value="أسبوعي">أسبوعي</option>
                                    <option value="يومي">يومي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مبلغ الدفع</label>
                                <input type="number" class="form-control" id="payment_amount" name="payment_amount" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                    <option value="معلق">معلق</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التقييم</label>
                                <select class="form-select" id="rating" name="rating">
                                    <option value="">بدون تقييم</option>
                                    <option value="5">ممتاز (5 نجوم)</option>
                                    <option value="4">جيد جداً (4 نجوم)</option>
                                    <option value="3">جيد (3 نجوم)</option>
                                    <option value="2">مقبول (2 نجمة)</option>
                                    <option value="1">ضعيف (1 نجمة)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveDriver()">حفظ السائق</button>
            </div>
        </div>
    </div>
</div>




<script>
function editDriver(id, name, phone, national_id, license_number, vehicle_type, vehicle_plate, payment_type, payment_amount, status, notes) {
    // ملء النموذج بالبيانات الحالية
    document.getElementById('driver_id').value = id;
    document.getElementById('name').value = name;
    document.getElementById('phone').value = phone;
    document.getElementById('national_id').value = national_id;
    document.getElementById('license_number').value = license_number;
    document.getElementById('vehicle_type').value = vehicle_type;
    document.getElementById('vehicle_plate').value = vehicle_plate;
    document.getElementById('payment_type').value = payment_type;
    document.getElementById('payment_amount').value = payment_amount;
    document.getElementById('status').value = status;
    document.getElementById('notes').value = notes;

    // تغيير عنوان النموذج وزر الحفظ
    document.querySelector('#addDriverModal .modal-title').textContent = 'تعديل السائق';
    document.querySelector('#addDriverModal .btn-primary').textContent = 'تحديث السائق';

    // تغيير action النموذج للتحديث
    document.getElementById('addDriverForm').action = '/drivers/edit/' + id;

    // عرض النموذج
    var modal = new bootstrap.Modal(document.getElementById('addDriverModal'));
    modal.show();
}

function deleteDriver(id, name) {
    if (confirm('هل أنت متأكد من حذف السائق "' + name + '"؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/drivers/delete/' + id;
        document.body.appendChild(form);
        form.submit();
    }
}

function updateLocation(id) {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '/drivers/update-location/' + id;

            var latInput = document.createElement('input');
            latInput.type = 'hidden';
            latInput.name = 'latitude';
            latInput.value = position.coords.latitude;
            form.appendChild(latInput);

            var lngInput = document.createElement('input');
            lngInput.type = 'hidden';
            lngInput.name = 'longitude';
            lngInput.value = position.coords.longitude;
            form.appendChild(lngInput);

            var addressInput = document.createElement('input');
            addressInput.type = 'hidden';
            addressInput.name = 'address';
            addressInput.value = 'موقع محدث تلقائياً';
            form.appendChild(addressInput);

            document.body.appendChild(form);
            form.submit();
        }, function(error) {
            alert('لا يمكن الحصول على الموقع: ' + error.message);
        });
    } else {
        alert('المتصفح لا يدعم تحديد الموقع');
    }
}

function saveDriver() {
    // التحقق من صحة البيانات
    const name = document.getElementById('name').value.trim();
    const phone = document.getElementById('phone').value.trim();
    const nationalId = document.getElementById('national_id').value.trim();
    const licenseNumber = document.getElementById('license_number').value.trim();

    if (!name) {
        alert('يرجى إدخال اسم السائق');
        return;
    }

    if (!phone) {
        alert('يرجى إدخال رقم الهاتف');
        return;
    }

    if (!nationalId) {
        alert('يرجى إدخال رقم الهوية');
        return;
    }

    if (!licenseNumber) {
        alert('يرجى إدخال رقم الرخصة');
        return;
    }

    // إرسال النموذج
    document.getElementById('addDriverForm').submit();
}

// إعادة تعيين النموذج عند إضافة سائق جديد
function resetDriverForm() {
    document.getElementById('driver_id').value = '';
    document.getElementById('addDriverForm').action = '/drivers/add';
    document.querySelector('#addDriverModal .modal-title').textContent = 'إضافة سائق جديد';
    document.querySelector('#addDriverModal .btn-primary').textContent = 'حفظ السائق';
    document.getElementById('addDriverForm').reset();
}

// إضافة مستمع للحدث عند فتح النموذج
document.getElementById('addDriverModal').addEventListener('show.bs.modal', function (event) {
    // إذا لم يتم استدعاء editDriver، فهذا يعني أننا نضيف سائق جديد
    if (!event.relatedTarget || !event.relatedTarget.onclick || !event.relatedTarget.onclick.toString().includes('editDriver')) {
        resetDriverForm();
    }
});
</script>

    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 نظام إدارة السائقين. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
    
    
</body>
</html>