{% extends "base.html" %}

{% block title %}إدارة السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    إدارة السائقين
                </h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة سائق جديد
                </button>
                <div class="btn-group ms-2" role="group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('export_drivers_excel') }}">
                            <i class="fas fa-file-excel text-success me-2"></i>
                            تصدير إلى Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('export_center') }}">
                            <i class="fas fa-download text-primary me-2"></i>
                            مركز التصدير المتقدم
                        </a></li>
                    </ul>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|length }}</h4>
                                    <p class="mb-0">إجمالي السائقين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|selectattr("status", "equalto", "نشط")|list|length }}</h4>
                                    <p class="mb-0">السائقين النشطين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|selectattr("payment_status", "equalto", "متأخر")|list|length }}</h4>
                                    <p class="mb-0">متأخرين في الدفع</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|selectattr("classification", "equalto", "ملتزم")|list|length }}</h4>
                                    <p class="mb-0">سائقين ملتزمين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول السائقين -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">قائمة السائقين</h5>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سائق جديد
                    </button>
                </div>
                <div class="card-body">
                    {% if drivers %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>رقم الرخصة</th>
                                    <th>الحالة</th>
                                    <th>حالة الدفع</th>
                                    <th>التصنيف</th>
                                    <th>التقييم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for driver in drivers %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    {{ driver.name[0] if driver.name else 'س' }}
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ driver.name or 'غير محدد' }}</h6>
                                                <small class="text-muted">{{ driver.phone or 'لا يوجد رقم' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ driver.phone or 'غير محدد' }}</td>
                                    <td>{{ driver.license_number or 'غير محدد' }}</td>
                                    <td>
                                        {% if driver.status == 'نشط' %}
                                            <span class="badge bg-success">{{ driver.status }}</span>
                                        {% elif driver.status == 'غير نشط' %}
                                            <span class="badge bg-secondary">{{ driver.status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ driver.status or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if driver.payment_status == 'مدفوع' %}
                                            <span class="badge bg-success">{{ driver.payment_status }}</span>
                                        {% elif driver.payment_status == 'متأخر' %}
                                            <span class="badge bg-danger">{{ driver.payment_status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ driver.payment_status or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if driver.classification == 'ملتزم' %}
                                            <span class="badge bg-success">{{ driver.classification }}</span>
                                        {% elif driver.classification == 'متوسط' %}
                                            <span class="badge bg-warning">{{ driver.classification }}</span>
                                        {% elif driver.classification == 'متأخر' %}
                                            <span class="badge bg-danger">{{ driver.classification }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% for i in range(1, 6) %}
                                                {% if i <= (driver.rating or 0) %}
                                                    <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                    <i class="far fa-star text-muted"></i>
                                                {% endif %}
                                            {% endfor %}
                                            <small class="ms-1">({{ "%.1f"|format(driver.rating or 0) }})</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" title="تحديث الموقع" onclick="updateLocation({{ driver.id }})">
                                                <i class="fas fa-map-marker-alt"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" title="تعديل" onclick="editDriver({{ driver.id }}, '{{ driver.name }}', '{{ driver.phone }}', '{{ driver.national_id }}', '{{ driver.license_number }}', '{{ driver.vehicle_type }}', '{{ driver.vehicle_plate }}', '{{ driver.payment_type }}', {{ driver.payment_amount or 0 }}, '{{ driver.status }}', '{{ driver.notes }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <a href="{{ url_for('export_driver_pdf', driver_id=driver.id) }}" class="btn btn-sm btn-outline-info" title="تصدير تقرير PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="حذف" onclick="deleteDriver({{ driver.id }}, '{{ driver.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد سائقين مسجلين</h5>
                        <p class="text-muted">ابدأ بإضافة سائق جديد لإدارة أسطولك</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة سائق جديد
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة سائق جديد -->
<div class="modal fade" id="addDriverModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سائق جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDriverForm" method="POST" action="/drivers/add">
                    <input type="hidden" id="driver_id" name="driver_id" value="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهوية *</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الرخصة *</label>
                                <input type="text" class="form-control" id="license_number" name="license_number" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المركبة</label>
                                <select class="form-select" id="vehicle_type" name="vehicle_type">
                                    <option value="">اختر نوع المركبة</option>
                                    <option value="سيارة">سيارة</option>
                                    <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                                    <option value="شاحنة كبيرة">شاحنة كبيرة</option>
                                    <option value="حافلة">حافلة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم اللوحة</label>
                                <input type="text" class="form-control" id="vehicle_plate" name="vehicle_plate">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع الدفع</label>
                                <select class="form-select" id="payment_type" name="payment_type">
                                    <option value="">اختر نوع الدفع</option>
                                    <option value="شهري">شهري</option>
                                    <option value="أسبوعي">أسبوعي</option>
                                    <option value="يومي">يومي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مبلغ الدفع</label>
                                <input type="number" class="form-control" id="payment_amount" name="payment_amount" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                    <option value="معلق">معلق</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التقييم</label>
                                <select class="form-select" id="rating" name="rating">
                                    <option value="">بدون تقييم</option>
                                    <option value="5">ممتاز (5 نجوم)</option>
                                    <option value="4">جيد جداً (4 نجوم)</option>
                                    <option value="3">جيد (3 نجوم)</option>
                                    <option value="2">مقبول (2 نجمة)</option>
                                    <option value="1">ضعيف (1 نجمة)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveDriver()">حفظ السائق</button>
            </div>
        </div>
    </div>
</div>




<script>
function editDriver(id, name, phone, national_id, license_number, vehicle_type, vehicle_plate, payment_type, payment_amount, status, notes) {
    // ملء النموذج بالبيانات الحالية
    document.getElementById('driver_id').value = id;
    document.getElementById('name').value = name;
    document.getElementById('phone').value = phone;
    document.getElementById('national_id').value = national_id;
    document.getElementById('license_number').value = license_number;
    document.getElementById('vehicle_type').value = vehicle_type;
    document.getElementById('vehicle_plate').value = vehicle_plate;
    document.getElementById('payment_type').value = payment_type;
    document.getElementById('payment_amount').value = payment_amount;
    document.getElementById('status').value = status;
    document.getElementById('notes').value = notes;

    // تغيير عنوان النموذج وزر الحفظ
    document.querySelector('#addDriverModal .modal-title').textContent = 'تعديل السائق';
    document.querySelector('#addDriverModal .btn-primary').textContent = 'تحديث السائق';

    // تغيير action النموذج للتحديث
    document.getElementById('addDriverForm').action = '/drivers/edit/' + id;

    // عرض النموذج
    var modal = new bootstrap.Modal(document.getElementById('addDriverModal'));
    modal.show();
}

function deleteDriver(id, name) {
    if (confirm('هل أنت متأكد من حذف السائق "' + name + '"؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/drivers/delete/' + id;
        document.body.appendChild(form);
        form.submit();
    }
}

function updateLocation(id) {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '/drivers/update-location/' + id;

            var latInput = document.createElement('input');
            latInput.type = 'hidden';
            latInput.name = 'latitude';
            latInput.value = position.coords.latitude;
            form.appendChild(latInput);

            var lngInput = document.createElement('input');
            lngInput.type = 'hidden';
            lngInput.name = 'longitude';
            lngInput.value = position.coords.longitude;
            form.appendChild(lngInput);

            var addressInput = document.createElement('input');
            addressInput.type = 'hidden';
            addressInput.name = 'address';
            addressInput.value = 'موقع محدث تلقائياً';
            form.appendChild(addressInput);

            document.body.appendChild(form);
            form.submit();
        }, function(error) {
            alert('لا يمكن الحصول على الموقع: ' + error.message);
        });
    } else {
        alert('المتصفح لا يدعم تحديد الموقع');
    }
}

function saveDriver() {
    // التحقق من صحة البيانات
    const name = document.getElementById('name').value.trim();
    const phone = document.getElementById('phone').value.trim();
    const nationalId = document.getElementById('national_id').value.trim();
    const licenseNumber = document.getElementById('license_number').value.trim();

    if (!name) {
        alert('يرجى إدخال اسم السائق');
        return;
    }

    if (!phone) {
        alert('يرجى إدخال رقم الهاتف');
        return;
    }

    if (!nationalId) {
        alert('يرجى إدخال رقم الهوية');
        return;
    }

    if (!licenseNumber) {
        alert('يرجى إدخال رقم الرخصة');
        return;
    }

    // إرسال النموذج
    document.getElementById('addDriverForm').submit();
}

// إعادة تعيين النموذج عند إضافة سائق جديد
function resetDriverForm() {
    document.getElementById('driver_id').value = '';
    document.getElementById('addDriverForm').action = '/drivers/add';
    document.querySelector('#addDriverModal .modal-title').textContent = 'إضافة سائق جديد';
    document.querySelector('#addDriverModal .btn-primary').textContent = 'حفظ السائق';
    document.getElementById('addDriverForm').reset();
}

// إضافة مستمع للحدث عند فتح النموذج
document.getElementById('addDriverModal').addEventListener('show.bs.modal', function (event) {
    // إذا لم يتم استدعاء editDriver، فهذا يعني أننا نضيف سائق جديد
    if (!event.relatedTarget || !event.relatedTarget.onclick || !event.relatedTarget.onclick.toString().includes('editDriver')) {
        resetDriverForm();
    }
});
</script>
{% endblock %}
