{% extends "base.html" %}

{% block title %}إدارة السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    إدارة السائقين
                </h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة سائق جديد
                </button>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|length }}</h4>
                                    <p class="mb-0">إجمالي السائقين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|selectattr("status", "equalto", "نشط")|list|length }}</h4>
                                    <p class="mb-0">السائقين النشطين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|selectattr("payment_status", "equalto", "متأخر")|list|length }}</h4>
                                    <p class="mb-0">متأخرين في الدفع</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ drivers|selectattr("classification", "equalto", "ملتزم")|list|length }}</h4>
                                    <p class="mb-0">سائقين ملتزمين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول السائقين -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">قائمة السائقين</h5>
                </div>
                <div class="card-body">
                    {% if drivers %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>رقم الرخصة</th>
                                    <th>الحالة</th>
                                    <th>حالة الدفع</th>
                                    <th>التصنيف</th>
                                    <th>التقييم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for driver in drivers %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    {{ driver.full_name[0] if driver.full_name else 'س' }}
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ driver.full_name or 'غير محدد' }}</h6>
                                                <small class="text-muted">{{ driver.email or 'لا يوجد إيميل' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ driver.phone or 'غير محدد' }}</td>
                                    <td>{{ driver.license_number or 'غير محدد' }}</td>
                                    <td>
                                        {% if driver.status == 'نشط' %}
                                            <span class="badge bg-success">{{ driver.status }}</span>
                                        {% elif driver.status == 'غير نشط' %}
                                            <span class="badge bg-secondary">{{ driver.status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ driver.status or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if driver.payment_status == 'مدفوع' %}
                                            <span class="badge bg-success">{{ driver.payment_status }}</span>
                                        {% elif driver.payment_status == 'متأخر' %}
                                            <span class="badge bg-danger">{{ driver.payment_status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ driver.payment_status or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if driver.classification == 'ملتزم' %}
                                            <span class="badge bg-success">{{ driver.classification }}</span>
                                        {% elif driver.classification == 'متوسط' %}
                                            <span class="badge bg-warning">{{ driver.classification }}</span>
                                        {% elif driver.classification == 'متأخر' %}
                                            <span class="badge bg-danger">{{ driver.classification }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% for i in range(1, 6) %}
                                                {% if i <= (driver.rating or 0) %}
                                                    <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                    <i class="far fa-star text-muted"></i>
                                                {% endif %}
                                            {% endfor %}
                                            <small class="ms-1">({{ "%.1f"|format(driver.rating or 0) }})</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد سائقين مسجلين</h5>
                        <p class="text-muted">ابدأ بإضافة سائق جديد لإدارة أسطولك</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDriverModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة سائق جديد
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة سائق جديد -->
<div class="modal fade" id="addDriverModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سائق جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDriverForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" name="phone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الرخصة *</label>
                                <input type="text" class="form-control" name="license_number" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المركبة</label>
                                <select class="form-select" name="vehicle_type">
                                    <option value="">اختر نوع المركبة</option>
                                    <option value="سيارة">سيارة</option>
                                    <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                                    <option value="شاحنة كبيرة">شاحنة كبيرة</option>
                                    <option value="حافلة">حافلة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم المركبة</label>
                                <input type="text" class="form-control" name="vehicle_number">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" name="address" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveDriver()">حفظ السائق</button>
            </div>
        </div>
    </div>
</div>

<script>
function saveDriver() {
    // هنا سيتم إضافة كود حفظ السائق
    alert('سيتم تطوير هذه الميزة قريباً');
}
</script>
{% endblock %}
