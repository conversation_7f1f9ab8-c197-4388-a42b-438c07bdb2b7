<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليل ذكي مفصل - {{ driver.name }} - نظام إدارة السائقين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .driver-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        
        .ai-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .ai-card:hover {
            transform: translateY(-5px);
        }
        
        .analysis-section {
            padding: 1.5rem;
        }
        
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .score-excellent { background: linear-gradient(135deg, #28a745, #20c997); }
        .score-good { background: linear-gradient(135deg, #17a2b8, #007bff); }
        .score-average { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .score-poor { background: linear-gradient(135deg, #dc3545, #e83e8c); }
        
        .metric-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #007bff;
        }
        
        .recommendation-item {
            background: #e7f3ff;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-left: 3px solid #007bff;
        }
        
        .comparison-bar {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        
        .comparison-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            transition: width 0.5s ease;
        }
        
        .risk-indicator {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .risk-high { background: #f8d7da; color: #721c24; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-low { background: #d1edff; color: #0c5460; }
        
        .chart-container {
            position: relative;
            height: 250px;
            margin: 1rem 0;
        }
        
        .btn-ai {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .btn-ai:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .navbar-brand {
            font-weight: 700;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: rgba(0,0,0,0.1);">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-brain me-2"></i>
                نظام إدارة السائقين - الذكاء الاصطناعي
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('ai_analysis_dashboard') }}">
                    <i class="fas fa-chart-bar me-1"></i>
                    التحليلات
                </a>
                <a class="nav-link" href="{{ url_for('drivers') }}">
                    <i class="fas fa-users me-1"></i>
                    السائقين
                </a>
            </div>
        </div>
    </nav>

    <!-- Driver Header -->
    <div class="driver-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 mb-2">{{ driver.name }}</h1>
                    <p class="lead mb-1">
                        <i class="fas fa-phone me-2"></i>{{ driver.phone }}
                        <span class="ms-3">
                            <i class="fas fa-car me-2"></i>{{ driver.vehicle_type }}
                        </span>
                    </p>
                    <span class="badge bg-{{ 'success' if driver.status == 'نشط' else 'warning' }} fs-6">
                        {{ driver.status }}
                    </span>
                </div>
                <div class="col-md-4 text-center">
                    <button class="btn btn-ai btn-lg" onclick="refreshDriverAnalysis()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث التحليل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Performance Overview -->
        <div class="row mb-4">
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="analysis-section text-center">
                        <h5 class="mb-3">تحليل الأداء العام</h5>
                        <div class="score-circle {{ 'score-excellent' if performance_analysis.performance_score >= 80 else 'score-good' if performance_analysis.performance_score >= 60 else 'score-average' if performance_analysis.performance_score >= 40 else 'score-poor' }}">
                            {{ performance_analysis.performance_score }}/100
                        </div>
                        <h6 class="text-muted">{{ performance_analysis.performance_level }}</h6>
                        <p class="small text-muted">معدل النجاح: {{ performance_analysis.success_rate }}%</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="analysis-section text-center">
                        <h5 class="mb-3">توقع التأخير</h5>
                        <div class="score-circle {{ 'score-low' if delay_prediction.delay_probability <= 0.3 else 'score-average' if delay_prediction.delay_probability <= 0.6 else 'score-poor' }}">
                            {{ "%.0f"|format(delay_prediction.delay_probability * 100) }}%
                        </div>
                        <span class="risk-indicator risk-{{ delay_prediction.risk_level.replace('عالي', 'high').replace('متوسط', 'medium').replace('منخفض', 'low') }}">
                            {{ delay_prediction.risk_level }}
                        </span>
                        <p class="small text-muted mt-2">الدفعة التالية: {{ delay_prediction.predicted_next_payment }}</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="analysis-section text-center">
                        <h5 class="mb-3">انتظام المدفوعات</h5>
                        <div class="score-circle {{ 'score-excellent' if payment_analysis.regularity_score >= 0.8 else 'score-good' if payment_analysis.regularity_score >= 0.6 else 'score-average' if payment_analysis.regularity_score >= 0.4 else 'score-poor' }}">
                            {{ "%.0f"|format(payment_analysis.regularity_score * 100) }}%
                        </div>
                        <h6 class="text-muted">{{ payment_analysis.pattern_type }}</h6>
                        <p class="small text-muted">{{ payment_analysis.payment_frequency }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Metrics -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-3">
                <div class="ai-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            المقاييس المالية
                        </h5>
                    </div>
                    <div class="analysis-section">
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>إجمالي الإيرادات</span>
                                <strong>{{ "%.2f"|format(performance_analysis.total_revenue) }} ريال</strong>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>متوسط الدفعة</span>
                                <strong>{{ "%.2f"|format(performance_analysis.average_payment) }} ريال</strong>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>معدل النمو</span>
                                <strong class="{{ 'text-success' if performance_analysis.growth_rate > 0 else 'text-danger' }}">
                                    {{ "%.1f"|format(performance_analysis.growth_rate) }}%
                                </strong>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>عدد المدفوعات</span>
                                <strong>{{ performance_analysis.total_payments_count }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-3">
                <div class="ai-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            تحليل التوقيت
                        </h5>
                    </div>
                    <div class="analysis-section">
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>متوسط الفترة بين المدفوعات</span>
                                <strong>{{ delay_prediction.avg_interval_days }} يوم</strong>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>أيام منذ آخر دفعة</span>
                                <strong>{{ delay_prediction.days_since_last_payment }} يوم</strong>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>اتجاه المدفوعات</span>
                                <strong>{{ payment_analysis.trend }}</strong>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="d-flex justify-content-between">
                                <span>آخر نشاط</span>
                                <strong>{{ performance_analysis.last_activity or 'غير محدد' }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparative Analysis -->
        {% if comparative_analysis %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="ai-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-balance-scale me-2"></i>
                            التحليل المقارن
                        </h5>
                    </div>
                    <div class="analysis-section">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>ترتيب الإيرادات</h6>
                                <p class="text-muted">المرتبة {{ comparative_analysis.revenue_rank }} من {{ comparative_analysis.total_drivers }}</p>
                                <div class="comparison-bar">
                                    <div class="comparison-fill" style="width: {{ comparative_analysis.revenue_percentile }}%"></div>
                                </div>
                                <small class="text-muted">أفضل من {{ comparative_analysis.revenue_percentile }}% من السائقين</small>
                            </div>
                            <div class="col-md-6">
                                <h6>ترتيب عدد المدفوعات</h6>
                                <p class="text-muted">المرتبة {{ comparative_analysis.payment_count_rank }} من {{ comparative_analysis.total_drivers }}</p>
                                <div class="comparison-bar">
                                    <div class="comparison-fill" style="width: {{ comparative_analysis.payment_percentile }}%"></div>
                                </div>
                                <small class="text-muted">أفضل من {{ comparative_analysis.payment_percentile }}% من السائقين</small>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="metric-item">
                                    <div class="d-flex justify-content-between">
                                        <span>مقارنة بمتوسط الإيرادات</span>
                                        <strong class="{{ 'text-success' if comparative_analysis.vs_avg_revenue > 0 else 'text-danger' }}">
                                            {{ "%.1f"|format(comparative_analysis.vs_avg_revenue) }}%
                                        </strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="metric-item">
                                    <div class="d-flex justify-content-between">
                                        <span>مقارنة بمتوسط المدفوعات</span>
                                        <strong class="{{ 'text-success' if comparative_analysis.vs_avg_payments > 0 else 'text-danger' }}">
                                            {{ "%.1f"|format(comparative_analysis.vs_avg_payments) }}%
                                        </strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Recommendations -->
        <div class="row mb-4">
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">توصيات الأداء</h6>
                    </div>
                    <div class="analysis-section">
                        {% for rec in performance_analysis.recommendations %}
                        <div class="recommendation-item">
                            <i class="fas fa-lightbulb me-2 text-warning"></i>
                            {{ rec }}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">توصيات التأخير</h6>
                    </div>
                    <div class="analysis-section">
                        {% for rec in delay_prediction.recommendations %}
                        <div class="recommendation-item">
                            <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                            {{ rec }}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-3">
                <div class="ai-card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">توصيات المدفوعات</h6>
                    </div>
                    <div class="analysis-section">
                        {% for rec in payment_analysis.recommendations %}
                        <div class="recommendation-item">
                            <i class="fas fa-credit-card me-2 text-primary"></i>
                            {{ rec }}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <a href="{{ url_for('driver_detail', driver_id=driver.id) }}" class="btn btn-ai me-2">
                    <i class="fas fa-user me-2"></i>
                    عرض ملف السائق
                </a>
                <a href="{{ url_for('payments') }}?driver_id={{ driver.id }}" class="btn btn-ai me-2">
                    <i class="fas fa-money-bill me-2"></i>
                    عرض المدفوعات
                </a>
                <button class="btn btn-ai" onclick="exportAnalysis()">
                    <i class="fas fa-download me-2"></i>
                    تصدير التحليل
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshDriverAnalysis() {
            window.location.reload();
        }
        
        function exportAnalysis() {
            const analysisData = {
                driver: {
                    name: '{{ driver.name }}',
                    phone: '{{ driver.phone }}',
                    status: '{{ driver.status }}'
                },
                performance: {{ performance_analysis | tojson }},
                delay_prediction: {{ delay_prediction | tojson }},
                payment_analysis: {{ payment_analysis | tojson }},
                comparative_analysis: {{ comparative_analysis | tojson }},
                export_date: new Date().toISOString()
            };
            
            const dataStr = JSON.stringify(analysisData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `ai_analysis_{{ driver.name }}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
