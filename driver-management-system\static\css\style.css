/* نظام إدارة السائقين - الأنماط المخصصة */

/* الخطوط العربية */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
}

/* تحسين الألوان */
:root {
    --primary-color: #4e73df;
    --success-color: #1cc88a;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --info-color: #36b9cc;
    --dark-color: #5a5c69;
    --light-color: #f8f9fc;
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* الحدود الملونة للبطاقات */
.border-right-primary {
    border-right: 0.25rem solid var(--primary-color) !important;
}

.border-right-success {
    border-right: 0.25rem solid var(--success-color) !important;
}

.border-right-warning {
    border-right: 0.25rem solid var(--warning-color) !important;
}

.border-right-info {
    border-right: 0.25rem solid var(--info-color) !important;
}

/* تحسين الأزرار */
.btn {
    border-radius: 0.35rem;
    font-weight: 600;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* تحسين الجداول */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

/* تحسين النماذج */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* الأيقونات الدائرية */
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين الإشعارات */
.alert {
    border-radius: 0.35rem;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* تحسين الشريط الجانبي */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* تحسين الرسوم البيانية */
.chart-area {
    position: relative;
    height: 20rem;
    width: 100%;
}

.chart-pie {
    position: relative;
    height: 15rem;
    width: 100%;
}

/* تحسين البحث */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* تحسين الصفحات */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: 0.35rem;
    margin: 0 0.125rem;
    border: 1px solid #d1d3e2;
    color: var(--primary-color);
}

.page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* تحسين الشارات */
.badge {
    font-weight: 600;
    border-radius: 0.35rem;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 0.35rem;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.dropdown-item {
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
}

/* تحسين الخريطة */
.map-container {
    height: 400px;
    border-radius: 0.35rem;
    overflow: hidden;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* تحسين رفع الملفات */
.file-upload-area {
    border: 2px dashed #d1d3e2;
    border-radius: 0.35rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(78, 115, 223, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(28, 200, 138, 0.05);
}

/* تحسين الحالات */
.status-active {
    color: var(--success-color);
}

.status-inactive {
    color: var(--danger-color);
}

.status-pending {
    color: var(--warning-color);
}

/* تحسين التقييمات */
.rating {
    color: #ffc107;
}

.rating .fa-star {
    margin: 0 0.1rem;
}

/* تحسين الإحصائيات */
.stat-card {
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

/* تحسين الجداول المتجاوبة */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: 0.35rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

/* تحسين الطباعة */
@media print {
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
}

/* تحسين الرسوم المتحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* تحسينات البحث الذكي */
.search-container {
    position: relative;
}

#searchResults {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

.search-category {
    margin-bottom: 0.5rem;
}

.search-category:last-child {
    margin-bottom: 0;
}

.search-category-title {
    color: #495057;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    margin: 0;
}

.search-item {
    padding: 0.75rem;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-2px);
}

.search-item:last-child {
    border-bottom: none;
}

.search-item-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.search-item-main {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-item-title {
    font-weight: 500;
    color: #212529;
}

.search-item-details {
    margin-right: 1.75rem;
}

.search-no-results {
    padding: 1.5rem;
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

.search-footer {
    padding: 0.5rem 0.75rem;
    text-align: center;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    font-size: 0.875rem;
}

/* تحسينات المستندات */
.document-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #fafbfc;
}

.document-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(78, 115, 223, 0.05);
}

.document-upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(28, 200, 138, 0.05);
}

.document-icon {
    font-size: 2.5rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.document-list-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
    background-color: white;
}

.document-list-item:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.document-info {
    flex: 1;
    margin-left: 1rem;
}

.document-name {
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.document-meta {
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.document-actions {
    display: flex;
    gap: 0.5rem;
}

.document-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

/* تحسينات الفلاتر */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* تحسينات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}
