#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التقرير المالي مباشرة
Test financial report directly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import Payment

def test_financial_queries():
    """اختبار استعلامات التقرير المالي"""
    with app.app_context():
        try:
            print("🔍 اختبار استعلامات التقرير المالي...")
            
            # اختبار الإحصائيات الأساسية
            print("1. اختبار إجمالي الإيرادات...")
            total_revenue = db.session.query(db.func.sum(Payment.amount)).filter_by(status='مكتمل').scalar() or 0
            print(f"✅ إجمالي الإيرادات: {total_revenue}")
            
            print("2. اختبار الإيرادات المعلقة...")
            pending_revenue = db.session.query(db.func.sum(Payment.amount)).filter_by(status='معلق').scalar() or 0
            print(f"✅ الإيرادات المعلقة: {pending_revenue}")
            
            print("3. اختبار المدفوعات الشهرية...")
            try:
                # محاولة استخدام strftime
                monthly_payments = db.session.query(
                    db.func.strftime('%Y-%m', Payment.payment_date).label('month'),
                    db.func.sum(Payment.amount).label('total')
                ).filter_by(status='مكتمل').group_by(db.func.strftime('%Y-%m', Payment.payment_date)).all()
                
                print(f"✅ المدفوعات الشهرية: {len(monthly_payments)} شهر")
                for payment in monthly_payments[:3]:  # عرض أول 3 أشهر
                    print(f"   - {payment.month}: {payment.total}")
                    
            except Exception as e:
                print(f"❌ خطأ في استعلام المدفوعات الشهرية: {e}")
                
                # استخدام طريقة بديلة
                print("🔄 استخدام طريقة بديلة...")
                all_payments = Payment.query.filter_by(status='مكتمل').all()
                monthly_data = {}
                
                for payment in all_payments:
                    month_key = payment.payment_date.strftime('%Y-%m')
                    if month_key not in monthly_data:
                        monthly_data[month_key] = 0
                    monthly_data[month_key] += payment.amount
                
                print(f"✅ المدفوعات الشهرية (طريقة بديلة): {len(monthly_data)} شهر")
                for month, total in list(monthly_data.items())[:3]:
                    print(f"   - {month}: {total}")
            
            print("✅ جميع الاستعلامات تعمل بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار الاستعلامات: {e}")
            return False

if __name__ == '__main__':
    success = test_financial_queries()
    
    if success:
        print("✅ انتهى الاختبار بنجاح")
    else:
        print("❌ فشل الاختبار")
