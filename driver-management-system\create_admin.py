#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء مستخدم admin افتراضي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User
from werkzeug.security import generate_password_hash

def create_admin_user():
    """إنشاء مستخدم admin افتراضي"""
    with app.app_context():
        try:
            # التحقق من وجود المستخدم
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print('✅ المستخدم admin موجود بالفعل')
                print(f'Username: {admin.username}')
                print(f'Role: {admin.role}')
                print(f'Active: {admin.is_active}')
                return True
            else:
                print('⚠️ المستخدم admin غير موجود - سيتم إنشاؤه')
                
                # إنشاء مستخدم admin
                admin = User(
                    username='admin',
                    password_hash=generate_password_hash('admin123'),
                    role='admin',
                    is_active=True
                )
                db.session.add(admin)
                db.session.commit()
                print('✅ تم إنشاء المستخدم admin بنجاح')
                print('Username: admin')
                print('Password: admin123')
                return True
                
        except Exception as e:
            print(f'❌ خطأ في إنشاء المستخدم: {str(e)}')
            return False

if __name__ == "__main__":
    create_admin_user()
