#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام المصادقة والأمان المحسن
Enhanced Authentication and Security Testing
"""

import requests
import time
import sys

def test_authentication_security():
    """اختبار ميزات الأمان في نظام المصادقة"""
    base_url = 'http://127.0.0.1:5000'
    session = requests.Session()
    
    print("🔐 اختبار نظام المصادقة والأمان المحسن...")
    print("=" * 50)
    
    try:
        # 1. اختبار تسجيل الدخول العادي
        print("1. اختبار تسجيل الدخول العادي...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(f'{base_url}/login', data=login_data)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تسجيل الدخول العادي يعمل")
        else:
            print("❌ فشل تسجيل الدخول العادي")
            return False
        
        # تسجيل الخروج
        session.get(f'{base_url}/logout')
        
        # 2. اختبار محاولات تسجيل الدخول الفاشلة
        print("2. اختبار محاولات تسجيل الدخول الفاشلة...")
        failed_attempts = 0
        for i in range(6):  # محاولة 6 مرات (أكثر من الحد المسموح)
            wrong_data = {
                'username': 'admin',
                'password': 'wrong_password'
            }
            response = session.post(f'{base_url}/login', data=wrong_data)
            failed_attempts += 1
            
            if i < 4:  # أول 4 محاولات
                if 'المحاولات المتبقية' in response.text:
                    print(f"✅ محاولة {i+1}: رسالة المحاولات المتبقية ظاهرة")
                else:
                    print(f"⚠️ محاولة {i+1}: رسالة المحاولات المتبقية غير ظاهرة")
            else:  # المحاولة الخامسة والسادسة
                if 'تم قفل حسابك' in response.text:
                    print(f"✅ محاولة {i+1}: تم قفل الحساب كما هو متوقع")
                    break
                else:
                    print(f"❌ محاولة {i+1}: لم يتم قفل الحساب")
        
        # 3. اختبار الوصول للصفحات المحمية بدون تسجيل دخول
        print("3. اختبار الوصول للصفحات المحمية...")
        new_session = requests.Session()
        protected_pages = ['/dashboard', '/drivers', '/payments', '/reports']
        
        for page in protected_pages:
            response = new_session.get(f'{base_url}{page}')
            if response.status_code == 302 and 'login' in response.headers.get('Location', ''):
                print(f"✅ {page}: إعادة توجيه لصفحة تسجيل الدخول")
            else:
                print(f"❌ {page}: لم يتم إعادة التوجيه لصفحة تسجيل الدخول")
        
        # 4. اختبار صفحة نسيان كلمة المرور
        print("4. اختبار صفحة نسيان كلمة المرور...")
        response = new_session.get(f'{base_url}/forgot-password')
        if response.status_code == 200 and 'نسيت كلمة المرور' in response.text:
            print("✅ صفحة نسيان كلمة المرور تعمل")
            
            # اختبار طلب إعادة تعيين
            reset_data = {
                'email': '<EMAIL>'
            }
            response = new_session.post(f'{base_url}/forgot-password', data=reset_data)
            if 'تم إنشاء رمز إعادة التعيين' in response.text:
                print("✅ طلب إعادة تعيين كلمة المرور يعمل")
            else:
                print("⚠️ طلب إعادة تعيين كلمة المرور لا يعمل")
        else:
            print("❌ صفحة نسيان كلمة المرور لا تعمل")
        
        # 5. اختبار رؤوس الأمان
        print("5. اختبار رؤوس الأمان...")
        response = new_session.get(f'{base_url}/login')
        security_headers = {
            'X-Frame-Options': 'DENY',
            'X-Content-Type-Options': 'nosniff',
            'X-XSS-Protection': '1; mode=block',
            'Content-Security-Policy': 'default-src',
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
        
        for header, expected_value in security_headers.items():
            if header in response.headers:
                if expected_value in response.headers[header]:
                    print(f"✅ {header}: موجود ومُعين بشكل صحيح")
                else:
                    print(f"⚠️ {header}: موجود لكن القيمة غير متوقعة")
            else:
                print(f"❌ {header}: غير موجود")
        
        # 6. اختبار ميزة "تذكرني"
        print("6. اختبار ميزة تذكرني...")
        remember_session = requests.Session()
        remember_data = {
            'username': 'admin',
            'password': 'admin123',
            'remember_me': 'on'
        }
        
        response = remember_session.post(f'{base_url}/login', data=remember_data)
        if response.status_code == 200:
            # التحقق من وجود كوكيز الجلسة
            cookies = remember_session.cookies
            if any('remember' in cookie.name.lower() for cookie in cookies):
                print("✅ ميزة تذكرني تعمل (كوكيز الجلسة موجودة)")
            else:
                print("⚠️ ميزة تذكرني: لا توجد كوكيز خاصة بالتذكر")
        else:
            print("❌ فشل اختبار ميزة تذكرني")
        
        print("\n" + "=" * 50)
        print("✅ انتهى اختبار نظام المصادقة والأمان")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأمان: {str(e)}")
        return False

def test_password_strength():
    """اختبار قوة كلمة المرور"""
    print("\n🔒 اختبار قوة كلمة المرور...")
    
    # استيراد النموذج للاختبار المحلي
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from models import User
        
        user = User()
        
        # كلمات مرور ضعيفة
        weak_passwords = [
            '123456',      # قصيرة جداً
            'password',    # بدون أرقام أو رموز
            'Password',    # بدون أرقام أو رموز
            'Password1',   # بدون رموز
            'password1!',  # بدون حروف كبيرة
        ]
        
        # كلمات مرور قوية
        strong_passwords = [
            'MyStr0ng!Pass',
            'Secure#123Pass',
            'Admin@2024!',
        ]
        
        print("اختبار كلمات المرور الضعيفة:")
        for pwd in weak_passwords:
            if not user.is_password_strong(pwd):
                print(f"✅ '{pwd}': تم رفضها كما هو متوقع")
            else:
                print(f"❌ '{pwd}': تم قبولها خطأً")
        
        print("\nاختبار كلمات المرور القوية:")
        for pwd in strong_passwords:
            if user.is_password_strong(pwd):
                print(f"✅ '{pwd}': تم قبولها كما هو متوقع")
            else:
                print(f"❌ '{pwd}': تم رفضها خطأً")
                
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قوة كلمة المرور: {str(e)}")
        return False

if __name__ == '__main__':
    # بدء الخادم أولاً
    import subprocess
    import os
    
    # تغيير المجلد
    os.chdir('driver-management-system')
    
    # بدء الخادم
    print("🔄 بدء خادم Flask...")
    server_process = subprocess.Popen([sys.executable, 'app.py'], 
                                    stdout=subprocess.PIPE, 
                                    stderr=subprocess.PIPE)
    
    # انتظار بدء الخادم
    time.sleep(3)
    
    try:
        # اختبار الأمان
        auth_success = test_authentication_security()
        
        # اختبار قوة كلمة المرور
        pwd_success = test_password_strength()
        
        if auth_success and pwd_success:
            print("\n🎉 جميع اختبارات الأمان نجحت!")
        else:
            print("\n⚠️ بعض اختبارات الأمان فشلت")
            
    finally:
        # إيقاف الخادم
        server_process.terminate()
        server_process.wait()
