{% extends "base.html" %}

{% block title %}الإشعارات - نظام إدارة السائقين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-bell me-2"></i>
                    إدارة الإشعارات
                </h2>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkNotificationModal">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال جماعي
                    </button>
                    <button class="btn btn-outline-success" onclick="refreshNotifications()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <!-- إحصائيات الإشعارات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ total_overdue or 0 }}</h4>
                                    <p class="mb-0">متأخرين في الدفع</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ total_upcoming or 0 }}</h4>
                                    <p class="mb-0">مستحقين قريباً</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>0</h4>
                                    <p class="mb-0">إشعارات مرسلة اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-paper-plane fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>0</h4>
                                    <p class="mb-0">ردود إيجابية</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-thumbs-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- السائقين المتأخرين -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                السائقين المتأخرين في الدفع
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            {% if overdue_drivers %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>السائق</th>
                                            <th>المبلغ</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for driver in overdue_drivers %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <div class="avatar-title bg-danger rounded-circle">
                                                            {{ driver.name[0] if driver.name else 'س' }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ driver.name or 'غير محدد' }}</h6>
                                                        <small class="text-muted">{{ driver.phone or 'لا يوجد رقم' }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong class="text-danger">{{ "%.0f"|format(driver.payment_amount or 0) }} ريال</strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ driver.next_payment_due.strftime('%Y-%m-%d') if driver.next_payment_due else 'غير محدد' }}
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-success" onclick="sendWhatsApp({{ driver.id }}, '{{ driver.name }}', '{{ driver.phone }}', {{ driver.payment_amount or 0 }})" title="WhatsApp">
                                                        <i class="fab fa-whatsapp"></i>
                                                    </button>
                                                    <button class="btn btn-outline-primary" onclick="sendSMS({{ driver.id }}, '{{ driver.name }}', '{{ driver.phone }}')" title="SMS">
                                                        <i class="fas fa-sms"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="text-muted mb-0">لا يوجد سائقين متأخرين في الدفع</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- السائقين المستحقين قريباً -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                مستحقين خلال 3 أيام
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            {% if upcoming_due %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>السائق</th>
                                            <th>المبلغ</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for driver in upcoming_due %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <div class="avatar-title bg-warning rounded-circle">
                                                            {{ driver.name[0] if driver.name else 'س' }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ driver.name or 'غير محدد' }}</h6>
                                                        <small class="text-muted">{{ driver.phone or 'لا يوجد رقم' }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong class="text-warning">{{ "%.0f"|format(driver.payment_amount or 0) }} ريال</strong>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ driver.next_payment_due.strftime('%Y-%m-%d') if driver.next_payment_due else 'غير محدد' }}
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-success" onclick="sendWhatsApp({{ driver.id }}, '{{ driver.name }}', '{{ driver.phone }}', {{ driver.payment_amount or 0 }})" title="WhatsApp">
                                                        <i class="fab fa-whatsapp"></i>
                                                    </button>
                                                    <button class="btn btn-outline-primary" onclick="sendSMS({{ driver.id }}, '{{ driver.name }}', '{{ driver.phone }}')" title="SMS">
                                                        <i class="fas fa-sms"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                                <p class="text-muted mb-0">لا يوجد دفعات مستحقة قريباً</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الإرسال الجماعي -->
<div class="modal fade" id="bulkNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال إشعارات جماعية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('send_bulk_notifications') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نوع الإشعار *</label>
                        <select class="form-select" name="type" required>
                            <option value="">اختر نوع الإشعار</option>
                            <option value="whatsapp">WhatsApp</option>
                            <option value="sms">رسائل SMS</option>
                            <option value="email">البريد الإلكتروني</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نص الرسالة *</label>
                        <textarea class="form-control" name="message" rows="5" required placeholder="مرحباً {name}، نذكرك بأن لديك دفعة مستحقة بمبلغ {amount} ريال. يرجى التواصل معنا لتسوية المبلغ."></textarea>
                        <div class="form-text">
                            يمكنك استخدام المتغيرات التالية: {name} لاسم السائق، {amount} للمبلغ المستحق
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إرسال الإشعارات للسائقين المتأخرين في الدفع فقط ({{ total_overdue or 0 }} سائق)
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إرسال الإشعارات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إرسال WhatsApp
function sendWhatsApp(driverId, driverName, phone, amount) {
    if (!phone || phone === 'لا يوجد رقم') {
        alert('لا يوجد رقم هاتف للسائق ' + driverName);
        return;
    }

    const message = `مرحباً ${driverName}، نذكرك بأن لديك دفعة مستحقة بمبلغ ${amount} ريال. يرجى التواصل معنا لتسوية المبلغ.`;
    const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`;

    // فتح WhatsApp في نافذة جديدة
    window.open(whatsappUrl, '_blank');

    // إرسال طلب لتسجيل الإشعار
    fetch('/notifications/whatsapp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `driver_id=${driverId}&message=${encodeURIComponent(message)}`
    }).then(response => {
        if (response.ok) {
            showNotification('تم إنشاء رابط WhatsApp بنجاح', 'success');
        }
    }).catch(error => {
        console.error('خطأ في إرسال الإشعار:', error);
    });
}

// إرسال SMS
function sendSMS(driverId, driverName, phone) {
    if (!phone || phone === 'لا يوجد رقم') {
        alert('لا يوجد رقم هاتف للسائق ' + driverName);
        return;
    }

    const message = `مرحباً ${driverName}، نذكرك بأن لديك دفعة مستحقة. يرجى التواصل معنا.`;

    // إرسال طلب SMS
    fetch('/notifications/sms', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `driver_id=${driverId}&message=${encodeURIComponent(message)}`
    }).then(response => {
        if (response.ok) {
            showNotification('سيتم تطوير إرسال SMS قريباً', 'info');
        }
    }).catch(error => {
        console.error('خطأ في إرسال SMS:', error);
    });
}

// تحديث الإشعارات
function refreshNotifications() {
    location.reload();
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
